// 测试新的图片消息格式
const coze = require('./coze-api-v3-client');

async function testImageFormats() {
  console.log('🧪 测试新的图片消息格式');
  console.log('=' .repeat(60));
  
  const userId = 'image_format_test_user';
  const mediaInfo = {
    numMedia: 1,
    mediaUrl: 'https://api.twilio.com/2010-04-01/Accounts/AC7657552c992e2a3737961532e7e609d1/Messages/MMaa6c44a7ff79f4ad4ce1f49781ddfdbf/Media/ME10a86f9b7de3168ed95543d5f99ea554',
    mediaType: 'image/jpeg'
  };
  
  try {
    // 测试1: 纯文本消息
    console.log('\n1️⃣ 测试纯文本消息');
    console.log('👤 用户: 你好，我想了解一下产品');
    const result1 = await coze.sendMessage('你好，我想了解一下产品', userId);
    console.log('🤖 机器人:', result1.text.substring(0, 80) + '...');
    
    // 等待2秒
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 测试2: 纯图片消息（应该自动添加上下文）
    console.log('\n2️⃣ 测试纯图片消息');
    console.log('👤 用户: [发送了一张图片，无文字]');
    console.log('📎 媒体URL:', mediaInfo.mediaUrl.substring(0, 50) + '...');
    
    const result2 = await coze.sendMessage('', userId, null, mediaInfo);
    console.log('🤖 机器人:', result2.text.substring(0, 80) + '...');
    
    // 等待2秒
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 测试3: 图片+文本组合消息
    console.log('\n3️⃣ 测试图片+文本组合消息');
    console.log('👤 用户: 这是什么产品？[附带图片]');
    
    const result3 = await coze.sendMessage('这是什么产品？', userId, null, mediaInfo);
    console.log('🤖 机器人:', result3.text.substring(0, 80) + '...');
    
    // 显示完整聊天历史
    const finalHistory = coze.getChatHistory(userId);
    console.log(`\n📚 完整聊天历史: ${finalHistory.length} 条记录`);
    
    console.log('\n📋 聊天历史详情:');
    finalHistory.forEach((msg, index) => {
      const role = msg.role === 'user' ? '👤 用户' : '🤖 机器人';
      console.log(`   ${index + 1}. ${role}:`);
      console.log(`      类型: ${msg.content_type}`);
      
      if (msg.content_type === 'object_string') {
        try {
          const parsed = JSON.parse(msg.content);
          console.log(`      内容: [object_string数组]`);
          parsed.forEach((item, i) => {
            if (item.type === 'text') {
              console.log(`        ${i + 1}. 文本: "${item.text}"`);
            } else if (item.type === 'image') {
              console.log(`        ${i + 1}. 图片: ${item.file_url.substring(0, 30)}...`);
            }
          });
        } catch (e) {
          console.log(`      内容: ${msg.content.substring(0, 50)}...`);
        }
      } else {
        const content = msg.content.length > 60 ? msg.content.substring(0, 60) + '...' : msg.content;
        console.log(`      内容: "${content}"`);
      }
      console.log('');
    });
    
    console.log('✅ 图片格式测试完成');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.error('错误详情:', error);
  }
}

// 测试API格式验证
async function testApiFormatValidation() {
  console.log('\n🧪 测试API格式验证');
  console.log('-'.repeat(40));
  
  const userId = 'format_validation_user';
  
  try {
    // 清除历史
    coze.clearUserConversationId(userId);
    
    // 测试：直接发送纯图片消息（没有上下文）
    console.log('📸 测试：直接发送纯图片消息（应该自动添加上下文）');
    
    const mediaInfo = {
      numMedia: 1,
      mediaUrl: 'https://example.com/test-image.jpg',
      mediaType: 'image/jpeg'
    };
    
    const result = await coze.sendMessage('', userId, null, mediaInfo);
    console.log('✅ 成功处理纯图片消息');
    console.log('🤖 回复:', result.text.substring(0, 60) + '...');
    
    // 检查历史记录
    const history = coze.getChatHistory(userId);
    console.log(`📚 历史记录数: ${history.length}`);
    
    // 应该有上下文文本消息 + 图片消息
    const hasContextText = history.some(msg => 
      msg.content_type === 'text' && msg.content === '请分析这张图片'
    );
    console.log(`🔍 自动添加上下文: ${hasContextText ? '✅ 是' : '❌ 否'}`);
    
  } catch (error) {
    console.error('❌ 格式验证测试失败:', error.message);
  }
}

// 主函数
async function main() {
  await testImageFormats();
  await testApiFormatValidation();
}

main();
