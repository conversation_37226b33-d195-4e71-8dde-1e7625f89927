// 简单的服务器测试脚本
const axios = require('axios');

const BASE_URL = 'http://localhost:3001';
const API_KEY = 'demo_api_key_12345';

async function testServer() {
  console.log('🚀 开始测试Twilio Business Messaging API...\n');

  try {
    // 测试健康检查
    console.log('1. 测试健康检查端点...');
    const healthResponse = await axios.get(`${BASE_URL}/health`);
    console.log('✅ 健康检查成功:', healthResponse.data.message);
    console.log('   版本:', healthResponse.data.version);
    console.log('   环境:', healthResponse.data.environment);
    console.log('');

    // 测试API信息
    console.log('2. 测试API信息端点...');
    const apiResponse = await axios.get(`${BASE_URL}/api`);
    console.log('✅ API信息获取成功:', apiResponse.data.message);
    console.log('   可用端点:', Object.keys(apiResponse.data.endpoints).join(', '));
    console.log('');

    // 测试认证（应该失败）
    console.log('3. 测试无认证访问（应该失败）...');
    try {
      await axios.post(`${BASE_URL}/api/messages/send`, {
        to: '+**********',
        body: 'Test message'
      });
      console.log('❌ 认证测试失败 - 应该被拒绝');
    } catch (error) {
      if (error.response && error.response.status === 401) {
        console.log('✅ 认证保护正常工作');
      } else {
        console.log('❌ 意外错误:', error.message);
      }
    }
    console.log('');

    // 测试带认证的请求（模拟）
    console.log('4. 测试带API Key的请求...');
    try {
      await axios.post(`${BASE_URL}/api/messages/send`, {
        to: '+**********',
        body: 'Test message'
      }, {
        headers: {
          'X-API-Key': API_KEY,
          'Content-Type': 'application/json'
        }
      });
      console.log('✅ API Key认证成功（注意：实际发送可能因Twilio配置而失败）');
    } catch (error) {
      if (error.response) {
        console.log('⚠️  API请求失败（预期的）:', error.response.data.error || error.response.data.message);
        console.log('   状态码:', error.response.status);
      } else {
        console.log('❌ 网络错误:', error.message);
      }
    }
    console.log('');

    // 测试Webhook健康检查
    console.log('5. 测试Webhook健康检查...');
    const webhookHealthResponse = await axios.get(`${BASE_URL}/webhook/health`);
    console.log('✅ Webhook健康检查成功:', webhookHealthResponse.data.message);
    console.log('');

    console.log('🎉 基础功能测试完成！');
    console.log('\n📋 测试总结:');
    console.log('   ✅ 服务器启动正常');
    console.log('   ✅ 健康检查端点工作正常');
    console.log('   ✅ API信息端点工作正常');
    console.log('   ✅ 认证中间件工作正常');
    console.log('   ✅ Webhook端点可访问');
    console.log('\n💡 提示:');
    console.log('   - 配置真实的Twilio凭据以测试消息发送功能');
    console.log('   - 配置MongoDB以测试数据持久化功能');
    console.log('   - 查看API文档: http://localhost:3000/docs');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.code === 'ECONNREFUSED') {
      console.error(`   服务器未启动或端口${BASE_URL.split(':')[2]}不可用`);
      console.error('   请先运行: npm start 或 node src/app.js');
    }
  }
}

// 运行测试
testServer();
