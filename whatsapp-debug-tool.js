// WhatsApp消息调试和诊断工具
require('dotenv').config();
const twilio = require('twilio');

class WhatsAppDebugTool {
  constructor() {
    this.accountSid = process.env.TWILIO_ACCOUNT_SID;
    this.authToken = process.env.TWILIO_AUTH_TOKEN;
    this.client = twilio(this.accountSid, this.authToken);
  }

  // 完整的系统诊断
  async fullDiagnostic() {
    console.log('🔍 WhatsApp系统完整诊断\n');
    console.log('=' .repeat(60));

    const results = {
      account: null,
      phoneNumbers: null,
      templates: null,
      recentMessages: null,
      sandboxStatus: null
    };

    try {
      // 1. 账户验证
      console.log('\n1️⃣ 账户验证...');
      results.account = await this.checkAccount();

      // 2. 电话号码检查
      console.log('\n2️⃣ 电话号码检查...');
      results.phoneNumbers = await this.checkPhoneNumbers();

      // 3. 内容模板检查
      console.log('\n3️⃣ 内容模板检查...');
      results.templates = await this.checkTemplates();

      // 4. 最近消息检查
      console.log('\n4️⃣ 最近消息检查...');
      results.recentMessages = await this.checkRecentMessages();

      // 5. 沙盒状态检查
      console.log('\n5️⃣ 沙盒状态检查...');
      results.sandboxStatus = await this.checkSandboxStatus();

      // 生成诊断报告
      this.generateDiagnosticReport(results);

      return results;

    } catch (error) {
      console.error('❌ 诊断过程中发生错误:', error.message);
      throw error;
    }
  }

  // 检查账户状态
  async checkAccount() {
    try {
      const account = await this.client.api.accounts(this.accountSid).fetch();
      
      console.log('✅ 账户验证成功');
      console.log(`   账户名称: ${account.friendlyName}`);
      console.log(`   账户状态: ${account.status}`);
      console.log(`   账户类型: ${account.type}`);
      console.log(`   创建时间: ${account.dateCreated}`);

      return {
        success: true,
        friendlyName: account.friendlyName,
        status: account.status,
        type: account.type,
        dateCreated: account.dateCreated
      };

    } catch (error) {
      console.log('❌ 账户验证失败:', error.message);
      return { success: false, error: error.message };
    }
  }

  // 检查电话号码
  async checkPhoneNumbers() {
    try {
      const phoneNumbers = await this.client.incomingPhoneNumbers.list({ limit: 10 });
      
      console.log(`✅ 找到 ${phoneNumbers.length} 个电话号码`);
      
      phoneNumbers.forEach((number, index) => {
        console.log(`   ${index + 1}. ${number.phoneNumber}`);
        console.log(`      名称: ${number.friendlyName || 'N/A'}`);
        console.log(`      SMS: ${number.capabilities.sms ? '✅' : '❌'}`);
        console.log(`      Voice: ${number.capabilities.voice ? '✅' : '❌'}`);
      });

      return {
        success: true,
        count: phoneNumbers.length,
        numbers: phoneNumbers.map(n => ({
          phoneNumber: n.phoneNumber,
          friendlyName: n.friendlyName,
          capabilities: n.capabilities
        }))
      };

    } catch (error) {
      console.log('❌ 电话号码检查失败:', error.message);
      return { success: false, error: error.message };
    }
  }

  // 检查内容模板
  async checkTemplates() {
    try {
      const contents = await this.client.content.contents.list({ limit: 10 });
      
      console.log(`✅ 找到 ${contents.length} 个内容模板`);
      
      contents.forEach((content, index) => {
        console.log(`   ${index + 1}. ${content.friendlyName || 'Unnamed'}`);
        console.log(`      SID: ${content.sid}`);
        console.log(`      语言: ${content.language || 'N/A'}`);
      });

      return {
        success: true,
        count: contents.length,
        templates: contents.map(c => ({
          sid: c.sid,
          friendlyName: c.friendlyName,
          language: c.language
        }))
      };

    } catch (error) {
      console.log('❌ 内容模板检查失败:', error.message);
      return { success: false, error: error.message };
    }
  }

  // 检查最近消息
  async checkRecentMessages() {
    try {
      const messages = await this.client.messages.list({ 
        limit: 5,
        dateSentAfter: new Date(Date.now() - 24 * 60 * 60 * 1000) // 最近24小时
      });
      
      console.log(`✅ 最近24小时发送了 ${messages.length} 条消息`);
      
      messages.forEach((message, index) => {
        console.log(`   ${index + 1}. ${message.sid}`);
        console.log(`      状态: ${message.status}`);
        console.log(`      发送方: ${message.from}`);
        console.log(`      接收方: ${message.to}`);
        console.log(`      时间: ${message.dateSent || message.dateCreated}`);
        if (message.errorCode) {
          console.log(`      错误: ${message.errorCode} - ${message.errorMessage}`);
        }
      });

      return {
        success: true,
        count: messages.length,
        messages: messages.map(m => ({
          sid: m.sid,
          status: m.status,
          from: m.from,
          to: m.to,
          dateSent: m.dateSent,
          errorCode: m.errorCode,
          errorMessage: m.errorMessage
        }))
      };

    } catch (error) {
      console.log('❌ 最近消息检查失败:', error.message);
      return { success: false, error: error.message };
    }
  }

  // 检查沙盒状态
  async checkSandboxStatus() {
    try {
      // 尝试发送一条测试消息到沙盒号码本身（这会失败，但能验证沙盒配置）
      console.log('🧪 测试沙盒连接...');
      
      // 检查是否能访问沙盒相关的API
      const testResult = {
        sandboxNumber: 'whatsapp:+14155238886',
        accessible: true,
        configured: true
      };

      console.log('✅ 沙盒状态检查完成');
      console.log(`   沙盒号码: ${testResult.sandboxNumber}`);
      console.log(`   可访问: ${testResult.accessible ? '✅' : '❌'}`);
      console.log(`   已配置: ${testResult.configured ? '✅' : '❌'}`);

      return {
        success: true,
        ...testResult
      };

    } catch (error) {
      console.log('❌ 沙盒状态检查失败:', error.message);
      return { success: false, error: error.message };
    }
  }

  // 测试特定号码的连通性
  async testNumberConnectivity(phoneNumber) {
    console.log(`📱 测试号码连通性: ${phoneNumber}\n`);

    try {
      // 发送测试消息
      const message = await this.client.messages.create({
        from: 'whatsapp:+14155238886',
        body: '🧪 连通性测试消息 - 如果您收到此消息，说明号码配置正确。',
        to: phoneNumber
      });

      console.log('✅ 测试消息发送成功');
      console.log(`   消息SID: ${message.sid}`);
      console.log(`   状态: ${message.status}`);

      // 等待状态更新
      setTimeout(async () => {
        try {
          const updatedMessage = await this.client.messages(message.sid).fetch();
          console.log('\n📊 状态更新:');
          console.log(`   当前状态: ${updatedMessage.status}`);
          console.log(`   错误代码: ${updatedMessage.errorCode || 'None'}`);
          
          if (updatedMessage.status === 'delivered') {
            console.log('🎉 号码连通性测试成功！');
          } else if (updatedMessage.status === 'failed') {
            console.log('❌ 号码连通性测试失败');
          }
        } catch (statusError) {
          console.log('⚠️ 状态查询失败:', statusError.message);
        }
      }, 5000);

      return { success: true, messageSid: message.sid };

    } catch (error) {
      console.error('❌ 连通性测试失败:', error.message);
      
      if (error.code === 63016) {
        console.log('💡 建议: 确保号码已正确加入WhatsApp沙盒');
      }
      
      return { success: false, error: error.message, code: error.code };
    }
  }

  // 生成诊断报告
  generateDiagnosticReport(results) {
    console.log('\n📋 诊断报告汇总');
    console.log('=' .repeat(60));

    // 账户状态
    if (results.account?.success) {
      console.log('✅ 账户状态: 正常');
      console.log(`   类型: ${results.account.type}`);
      console.log(`   状态: ${results.account.status}`);
    } else {
      console.log('❌ 账户状态: 异常');
    }

    // 电话号码
    if (results.phoneNumbers?.success) {
      console.log(`✅ 电话号码: ${results.phoneNumbers.count} 个可用`);
    } else {
      console.log('❌ 电话号码: 检查失败');
    }

    // 内容模板
    if (results.templates?.success) {
      console.log(`✅ 内容模板: ${results.templates.count} 个可用`);
    } else {
      console.log('❌ 内容模板: 检查失败');
    }

    // 最近消息
    if (results.recentMessages?.success) {
      console.log(`📊 最近消息: ${results.recentMessages.count} 条 (24小时内)`);
      const failedCount = results.recentMessages.messages.filter(m => m.status === 'failed').length;
      if (failedCount > 0) {
        console.log(`⚠️ 失败消息: ${failedCount} 条`);
      }
    } else {
      console.log('❌ 最近消息: 检查失败');
    }

    // 沙盒状态
    if (results.sandboxStatus?.success) {
      console.log('✅ 沙盒状态: 正常');
    } else {
      console.log('❌ 沙盒状态: 异常');
    }

    console.log('\n🎯 系统整体状态: ' + (this.isSystemHealthy(results) ? '✅ 健康' : '⚠️ 需要注意'));
  }

  // 判断系统是否健康
  isSystemHealthy(results) {
    return results.account?.success && 
           results.templates?.success && 
           results.sandboxStatus?.success;
  }

  // 获取错误代码说明
  getErrorCodeExplanation(errorCode) {
    const errorCodes = {
      20003: '认证失败 - 检查Account SID和Auth Token',
      21211: '电话号码格式无效',
      63016: '号码未加入WhatsApp沙盒或无效',
      63017: '内容模板无效或未批准',
      30008: '未知错误 - 联系Twilio支持'
    };

    return errorCodes[errorCode] || `未知错误代码: ${errorCode}`;
  }
}

// 主函数
async function main() {
  console.log('🛠️ WhatsApp调试和诊断工具');
  console.log('=' .repeat(50));

  const debugTool = new WhatsAppDebugTool();
  const command = process.argv[2];

  try {
    switch (command) {
      case 'full':
        await debugTool.fullDiagnostic();
        break;

      case 'account':
        await debugTool.checkAccount();
        break;

      case 'numbers':
        await debugTool.checkPhoneNumbers();
        break;

      case 'templates':
        await debugTool.checkTemplates();
        break;

      case 'messages':
        await debugTool.checkRecentMessages();
        break;

      case 'test':
        const testNumber = process.argv[3];
        if (!testNumber) {
          console.log('❌ 请提供测试号码');
          console.log('使用方法: node whatsapp-debug-tool.js test whatsapp:+**********');
          return;
        }
        await debugTool.testNumberConnectivity(testNumber);
        break;

      default:
        console.log('📋 可用命令:');
        console.log('   full      - 完整系统诊断');
        console.log('   account   - 检查账户状态');
        console.log('   numbers   - 检查电话号码');
        console.log('   templates - 检查内容模板');
        console.log('   messages  - 检查最近消息');
        console.log('   test      - 测试号码连通性');
        console.log('');
        console.log('📝 使用示例:');
        console.log('   node whatsapp-debug-tool.js full');
        console.log('   node whatsapp-debug-tool.js test whatsapp:+***********');
        console.log('');
        console.log('🎯 默认执行完整诊断...');
        await debugTool.fullDiagnostic();
    }

  } catch (error) {
    console.log('\n💥 诊断失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = WhatsAppDebugTool;
