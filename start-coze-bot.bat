@echo off
echo 🤖 启动WhatsApp Coze智能机器人
echo =====================================
echo.

echo 📋 这个脚本将帮助您:
echo    1. 测试Coze API连接
echo    2. 启动智能回复服务
echo    3. 配置Webhook
echo.

echo ⚠️  请确保您已经:
echo    ✅ 完成WhatsApp沙盒设置
echo    ✅ 配置了Coze机器人
echo    ✅ 安装了项目依赖 (npm install)
echo.

pause

echo 🔄 第一步: 测试Coze API连接...
echo.

node test-coze-api.js connection

echo.
echo 📝 如果连接测试成功，按任意键继续...
pause

echo.
echo 🚀 第二步: 启动智能回复服务...
echo.

start "WhatsApp Coze Bot" cmd /k "node whatsapp-coze-bot.js"

timeout /t 3 /nobreak >nul

echo ✅ 智能回复服务已启动在新窗口中
echo.

echo 📡 第三步: 设置ngrok隧道
echo    请在新的命令行窗口运行:
echo    ngrok http 3002
echo.

echo 📋 然后复制ngrok提供的HTTPS URL
echo    例如: https://abc123.ngrok.io
echo.

echo 🔧 第四步: 在Twilio控制台配置Webhook:
echo    1. 访问: https://console.twilio.com/us1/develop/sms/try-it-out/whatsapp-learn
echo    2. 找到 "When a message comes in" 字段
echo    3. 输入: https://your-ngrok-url.ngrok.io/whatsapp-webhook
echo    4. 保存配置
echo.

echo 🧪 第五步: 测试智能回复
echo    从您的WhatsApp发送消息到: +1 ************
echo    机器人将使用Coze AI进行智能回复
echo.

echo 📊 监控地址:
echo    健康检查: http://localhost:3002/health
echo    会话查看: http://localhost:3002/sessions
echo.

echo 🎯 设置完成！观察智能回复服务窗口的日志。
echo.

echo 💡 特色功能:
echo    ✅ 智能对话回复
echo    ✅ 上下文记忆
echo    ✅ 多用户会话管理
echo    ✅ 自动错误处理
echo.

pause
