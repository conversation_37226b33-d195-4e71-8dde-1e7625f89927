const express = require('express');
const messageRoutes = require('./messages');
const statusRoutes = require('./status');
const webhookRoutes = require('./webhook');

const router = express.Router();

// API路由
router.use('/api/messages', messageRoutes);
router.use('/api/status', statusRoutes);
router.use('/webhook', webhookRoutes);

// 健康检查
router.get('/health', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'Twilio Business Messaging API is healthy',
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version || '1.0.0',
    environment: process.env.NODE_ENV || 'development',
  });
});

// API信息
router.get('/api', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'Twilio Business Messaging API',
    version: process.env.npm_package_version || '1.0.0',
    documentation: '/docs',
    endpoints: {
      messages: '/api/messages',
      status: '/api/status',
      webhooks: '/webhook',
      health: '/health',
    },
  });
});

module.exports = router;
