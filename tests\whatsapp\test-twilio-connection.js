// 测试Twilio连接
require('dotenv').config();
const twilio = require('twilio');

async function testTwilioConnection() {
  console.log('🔗 测试Twilio连接...\n');

  try {
    const accountSid = process.env.TWILIO_ACCOUNT_SID;
    const authToken = process.env.TWILIO_AUTH_TOKEN;

    console.log('📋 配置信息:');
    console.log('   Account SID:', accountSid);
    console.log('   Auth Token:', authToken ? authToken.substring(0, 8) + '...' : 'Not set');
    console.log('');

    if (!accountSid || !authToken) {
      console.error('❌ Twilio凭据未配置');
      return;
    }

    // 创建Twilio客户端
    const client = twilio(accountSid, authToken);

    // 1. 验证账户信息
    console.log('1. 验证账户信息...');
    const account = await client.api.accounts(accountSid).fetch();
    console.log('✅ 账户验证成功');
    console.log('   账户名称:', account.friendlyName);
    console.log('   账户状态:', account.status);
    console.log('   账户类型:', account.type);
    console.log('');

    // 2. 获取电话号码列表
    console.log('2. 获取可用电话号码...');
    const phoneNumbers = await client.incomingPhoneNumbers.list({ limit: 10 });
    
    if (phoneNumbers.length > 0) {
      console.log('✅ 找到', phoneNumbers.length, '个电话号码:');
      phoneNumbers.forEach((number, index) => {
        console.log(`   ${index + 1}. ${number.phoneNumber} (${number.friendlyName || 'No name'})`);
      });
      
      // 更新.env文件中的电话号码
      const firstNumber = phoneNumbers[0].phoneNumber;
      console.log('\n📝 建议更新.env文件中的TWILIO_PHONE_NUMBER为:', firstNumber);
    } else {
      console.log('⚠️  未找到可用的电话号码');
      console.log('   请在Twilio控制台购买或配置电话号码');
    }
    console.log('');

    // 3. 检查余额（如果可用）
    console.log('3. 检查账户余额...');
    try {
      const balance = await client.balance.fetch();
      console.log('✅ 账户余额:', balance.balance, balance.currency);
    } catch (balanceError) {
      console.log('⚠️  无法获取余额信息（可能需要升级账户）');
    }
    console.log('');

    // 4. 测试消息发送能力（不实际发送）
    console.log('4. 验证消息服务可用性...');
    try {
      // 只是验证服务可用性，不实际发送消息
      const services = await client.messaging.services.list({ limit: 1 });
      console.log('✅ 消息服务可用');
    } catch (serviceError) {
      console.log('✅ 基础消息功能可用（无需消息服务）');
    }

    console.log('\n🎉 Twilio连接测试完成！');
    console.log('\n📋 总结:');
    console.log('   ✅ 账户凭据有效');
    console.log('   ✅ API连接正常');
    console.log('   ✅ 可以开始使用Twilio API');
    
    if (phoneNumbers.length > 0) {
      console.log('\n💡 下一步:');
      console.log('   1. 更新.env文件中的TWILIO_PHONE_NUMBER');
      console.log('   2. 启动完整服务器: npm start');
      console.log('   3. 测试真实消息发送');
    } else {
      console.log('\n⚠️  注意:');
      console.log('   需要在Twilio控制台购买电话号码才能发送消息');
    }

  } catch (error) {
    console.error('❌ Twilio连接失败:', error.message);
    
    if (error.code === 20003) {
      console.error('   错误: 认证失败 - 请检查Account SID和Auth Token');
    } else if (error.code === 'ENOTFOUND') {
      console.error('   错误: 网络连接失败 - 请检查网络连接');
    } else {
      console.error('   错误代码:', error.code);
      console.error('   详细信息:', error.moreInfo || 'N/A');
    }
  }
}

testTwilioConnection();
