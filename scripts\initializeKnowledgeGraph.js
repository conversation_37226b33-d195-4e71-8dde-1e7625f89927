#!/usr/bin/env node
// 知识图谱初始化脚本 - 预先处理和写入业务数据到ZEP
require('dotenv').config();
const KnowledgeGraphService = require('../src/services/knowledgeGraphService');

async function main() {
  console.log('🚀 开始初始化畅游网络知识图谱...');
  console.log('=' .repeat(60));

  const knowledgeService = new KnowledgeGraphService();

  try {
    // 检查环境变量
    if (!process.env.ZEP_API_KEY) {
      throw new Error('ZEP_API_KEY 环境变量未设置');
    }

    console.log('✅ 环境变量检查通过');
    console.log(`🔑 ZEP API Key: ${process.env.ZEP_API_KEY.substring(0, 10)}...`);

    // 检查是否需要重置
    const shouldReset = process.argv.includes('--reset') || process.argv.includes('-r');

    let result;
    if (shouldReset) {
      console.log('🔄 执行重置模式...');
      result = await knowledgeService.resetKnowledgeBase();
    } else {
      // 初始化知识图谱
      result = await knowledgeService.initializeKnowledgeBase();
    }
    
    if (result) {
      console.log('\n🎉 知识图谱初始化成功！');
      
      // 等待数据处理
      console.log('\n⏳ 等待知识图谱数据处理...');
      await new Promise(resolve => setTimeout(resolve, 5000));

      // 调试：列出所有数据
      console.log('\n🔍 调试：检查知识图谱中的数据...');
      await knowledgeService.debugListKnowledgeGraph();

      // 测试不同搜索方法
      console.log('\n🧪 测试不同搜索方法...');
      await knowledgeService.testSearchMethods('畅游 Pro');

      // 测试知识搜索
      console.log('\n🧪 测试知识搜索功能...');
      const testQueries = [
        '畅游 Pro 的价格是多少？',
        '有什么流量套餐？',
        '售后服务怎么样？',
        '适合商务人士的产品'
      ];

      for (const query of testQueries) {
        console.log(`\n🔍 测试查询: "${query}"`);
        const searchResult = await knowledgeService.searchKnowledge(query);

        if (searchResult.found) {
          console.log(`   ✅ 找到 ${searchResult.count} 条相关知识 (方法: ${searchResult.searchMethod})`);
          console.log(`   📝 知识摘要: ${searchResult.knowledge.substring(0, 200)}...`);
        } else {
          console.log(`   ⚠️ 未找到相关知识 (方法: ${searchResult.searchMethod})`);
        }
      }
      
      // 检查知识图谱状态
      console.log('\n📊 检查知识图谱状态...');
      const status = await knowledgeService.checkKnowledgeBaseStatus();
      console.log(`   状态: ${status.status}`);
      if (status.user) {
        console.log(`   用户: ${status.user.firstName} ${status.user.lastName}`);
        console.log(`   邮箱: ${status.user.email}`);
      }
      
      console.log('\n✨ 知识图谱已准备就绪，可以启动应用程序！');
      
    } else {
      console.log('\n❌ 知识图谱初始化失败');
      process.exit(1);
    }
    
  } catch (error) {
    console.error('\n❌ 初始化过程中发生错误:', error.message);
    console.error('详细错误:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main().then(() => {
    console.log('\n👋 初始化完成，脚本退出');
    process.exit(0);
  }).catch((error) => {
    console.error('\n💥 脚本执行失败:', error.message);
    process.exit(1);
  });
}

module.exports = main;
