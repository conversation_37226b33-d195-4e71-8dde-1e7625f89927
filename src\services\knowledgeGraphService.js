// 知识图谱服务 - 管理畅游网络业务知识
require('dotenv').config();
const { ZepClient } = require('@getzep/zep-cloud');

class KnowledgeGraphService {
  constructor() {
    this.zepClient = new ZepClient({
      apiKey: process.env.ZEP_API_KEY
    });
    this.KNOWLEDGE_USER_ID = 'changyou_knowledge_base_user';
  }

  // 初始化知识图谱（仅在首次运行或更新时调用）
  async initializeKnowledgeBase() {
    try {
      console.log('🧠 初始化畅游网络知识图谱...');
      
      // 检查用户是否已存在
      try {
        await this.zepClient.user.get(this.KNOWLEDGE_USER_ID);
        console.log('✅ 知识图谱用户已存在');
        return true;
      } catch (error) {
        // 用户不存在，创建新用户并添加数据
        console.log('📝 创建知识图谱用户...');
        await this.zepClient.user.add({
          userId: this.KNOWLEDGE_USER_ID,
          email: '<EMAIL>',
          firstName: '畅游网络',
          lastName: '知识库',
          metadata: {
            source: 'changyou-knowledge-base',
            created: new Date().toISOString(),
            version: '1.0'
          }
        });
        
        // 添加业务数据到知识图谱
        await this.addBusinessDataToKnowledgeGraph();
        return true;
      }
    } catch (error) {
      console.error('❌ 知识图谱初始化失败:', error.message);
      return false;
    }
  }

  // 添加业务数据到知识图谱
  async addBusinessDataToKnowledgeGraph() {
    console.log('📚 添加畅游网络业务数据到知识图谱...');
    
    const businessData = [
      // 产品型号与定价策略
      {
        type: 'text',
        data: 'Changyou Mini Router: Price 299 yuan, 8-10 hours battery life, supports up to 5 devices, 4G LTE network technology. Core features: ultra-portable, suitable for personal short trips. Target users: personal travelers, backpackers. Ideal for 1-2 people usage.'
      },
      {
        type: 'text',
        data: 'Changyou Pro Router: Price 599 yuan, 15-18 hours battery life, supports up to 10 devices, 5G/4G LTE intelligent switching. Core features: balanced performance and battery life, suitable for business travel. Target users: business people, students. Ideal for 3-6 people usage.'
      },
      {
        type: 'text',
        data: 'Changyou Max Router: Price 999 yuan, 20-24 hours battery life, supports up to 16 devices, global mainstream 5G/4G LTE bands. Core features: flagship performance, multi-device support, suitable for family or team travel. Target users: family trips, small business teams. Ideal for 7+ people usage.'
      },
      // 流量套餐定价策略
      {
        type: 'text',
        data: 'Pay-as-you-go Data Plans (suitable for low-frequency users): 80 yuan for 10GB (valid for 30 days), 150 yuan for 25GB (valid for 60 days). Features: flexible billing, pay-as-needed usage.'
      },
      {
        type: 'text',
        data: 'Monthly Data Plans (suitable for regular users): 128 yuan for 50GB, 198 yuan for 100GB, 288 yuan for 200GB (suitable for heavy users or multi-device sharing). Features: monthly billing, stable usage.'
      },
      {
        type: 'text',
        data: 'Annual Data Plans (best value option): 1299 yuan for 600GB (equivalent to 108 yuan per month for 50GB), 1999 yuan for 1200GB (equivalent to 166 yuan per month for 100GB). Features: high cost-effectiveness, long-term benefits.'
      },
      {
        type: 'text',
        data: 'Regional Special Packages: Southeast Asia Travel Package 99 yuan for 30GB (valid for 15 days), Europe-America Business Package 188 yuan for 50GB (valid for 30 days). Designed for specific travel destinations.'
      },
      {
        type: 'text',
        data: 'Promotional Strategies: First-time purchase bonus - get free 10GB basic data package worth 80 yuan with any device purchase. Bundle discount - 10% off when purchasing multiple same packages. Existing user renewal discount - 15% off annual packages for existing users.'
      },
      // 售前政策
      {
        type: 'text',
        data: 'Pre-sales Service Policy - Free Consultation: Online customer service, WeChat official account, and phone hotline provide one-on-one professional consultation. Customer service will answer questions about product performance, coverage, usage methods, and package selection.'
      },
      {
        type: 'text',
        data: 'Pre-sales Service Policy - Needs Assessment: Customer service staff will actively inquire about travel destinations, number of users, device quantity, and main internet usage habits (browsing, video, online office) to recommend the most suitable device model and data package.'
      },
      {
        type: 'text',
        data: 'Pre-sales Service Policy - Transparent Pricing: All fees including device cost, package fees, and possible additional costs (shipping) are clearly disclosed before purchase. No hidden charges. All package validity periods, data quotas, and overage billing rules are clearly explained.'
      },
      {
        type: 'text',
        data: 'Pre-sales Service Policy - 7-day No-reason Trial: Users can apply for no-reason returns within 7 days of receiving the device (if device is in good condition and does not affect resale). Refund completed within 3 business days after receiving returned product.'
      },
      {
        type: 'text',
        data: 'Pre-sales Service Policy - Shipping and Delivery: For domestic orders, we promise to ship within 24 hours of payment. Partner with major courier companies to ensure fast and safe delivery. Provide tracking numbers for real-time logistics status checking.'
      },
      // 售后政策
      {
        type: 'text',
        data: 'After-sales Service Policy - One-year Hardware Warranty: Device main unit enjoys one-year free warranty service from purchase date. For non-human damage performance failures during warranty period, free repair or replacement provided.'
      },
      {
        type: 'text',
        data: 'After-sales Service Policy - Lifetime Technical Support: 7x12 hours online technical support to answer any questions during usage, such as network connection, device settings, data usage queries. Detailed electronic and printed manuals provided, plus online FAQ page.'
      },
      {
        type: 'text',
        data: 'After-sales Service Policy - Data Management and Reminders: Users can check real-time data usage through official App or mini-program. System automatically sends reminder SMS or App notifications when package usage reaches 80% and 100% to prevent disconnection due to data depletion.'
      },
      {
        type: 'text',
        data: 'After-sales Service Policy - Package Renewal and Upgrade: Users can renew or upgrade data packages anytime through official channels. New packages take effect immediately without waiting. Support multiple payment methods including Alipay, WeChat Pay, and credit cards.'
      },
      {
        type: 'text',
        data: 'After-sales Service Policy - Device Loss or Damage: If device is lost, users can immediately contact customer service to suspend service and prevent data theft. For out-of-warranty or human-damaged devices, cost-price repair service provided. Existing users get discounts on new device purchases.'
      },
      {
        type: 'text',
        data: 'After-sales Service Policy - User Feedback Channels: Welcome user feedback and suggestions through any channel. Dedicated team follows up to continuously optimize products and services.'
      }
    ];
    
    for (let i = 0; i < businessData.length; i++) {
      const item = businessData[i];
      console.log(`📝 添加第 ${i + 1}/${businessData.length} 个数据项...`);
      console.log(`   内容: ${item.data.substring(0, 50)}...`);

      try {
        const addResult = await this.zepClient.graph.add({
          userId: this.KNOWLEDGE_USER_ID,
          ...item
        });
        console.log(`   ✅ 第 ${i + 1} 个数据项添加成功`);
        console.log(`   📊 添加结果:`, JSON.stringify(addResult, null, 2));

        // 添加延迟，确保数据被正确处理
        if (i < businessData.length - 1) {
          console.log(`   ⏳ 等待 2 秒...`);
          await new Promise(resolve => setTimeout(resolve, 2000));
        }
      } catch (error) {
        console.error(`   ❌ 第 ${i + 1} 个数据项添加失败:`, error.message);
        console.error(`   详细错误:`, error);

        // 即使失败也继续处理下一个
        continue;
      }
    }

    console.log(`✅ ${businessData.length} 个业务数据项处理完成`);
  }

  // 搜索知识图谱
  async searchKnowledge(query) {
    try {
      // 检查查询是否有效
      if (!query || query === null || query === undefined || query.trim() === '') {
        console.log(`⚠️ 搜索查询为空或无效: "${query}"`);
        return {
          found: false,
          knowledge: '',
          edges: [],
          reason: 'empty_query'
        };
      }

      // console.log(`🔍 搜索知识图谱: "${query}"`);

      // 尝试多种搜索方式
      let searchResults = null;

      // 方法1: 直接搜索
      try {
        searchResults = await this.zepClient.graph.search({
          userId: this.KNOWLEDGE_USER_ID,
          query: query
        });
        // console.log(`📊 直接搜索结果:`, searchResults);
      } catch (searchError) {
        console.log(`⚠️ 直接搜索失败: ${searchError.message}`);
      }

      // 如果直接搜索没有结果，尝试关键词搜索
      if (!searchResults || !searchResults.edges || searchResults.edges.length === 0) {
        console.log('🔄 尝试关键词搜索...');

        // 提取关键词
        const keywords = this.extractKeywords(query);
        console.log(`🔑 提取的关键词: ${keywords.join(', ')}`);

        for (const keyword of keywords) {
          try {
            const keywordResults = await this.zepClient.graph.search({
              userId: this.KNOWLEDGE_USER_ID,
              query: keyword
            });

            if (keywordResults && keywordResults.edges && keywordResults.edges.length > 0) {
              console.log(`✅ 关键词 "${keyword}" 找到 ${keywordResults.edges.length} 条结果`);
              searchResults = keywordResults;
              break;
            }
          } catch (keywordError) {
            console.log(`⚠️ 关键词 "${keyword}" 搜索失败: ${keywordError.message}`);
          }
        }
      }

      if (searchResults && searchResults.edges && searchResults.edges.length > 0) {
        const knowledge = searchResults.edges.slice(0, 5).map(edge => edge.fact).join('\n');
        console.log(`✅ 找到 ${searchResults.edges.length} 条相关知识`);
        console.log(`📝 知识内容预览: ${knowledge.substring(0, 200)}...`);

        return {
          found: true,
          knowledge: knowledge,
          count: searchResults.edges.length,
          searchMethod: 'graph_search'
        };
      } else {
        console.log('⚠️ 所有搜索方法都未找到相关知识');

        // 返回一些基础知识作为后备
        const fallbackKnowledge = this.getFallbackKnowledge(query);
        if (fallbackKnowledge) {
          console.log('🔄 使用后备知识');
          return {
            found: true,
            knowledge: fallbackKnowledge,
            count: 1,
            searchMethod: 'fallback'
          };
        }

        return {
          found: false,
          knowledge: '',
          count: 0,
          searchMethod: 'none'
        };
      }
    } catch (error) {
      console.error('❌ 知识图谱搜索失败:', error.message);
      console.error('详细错误:', error);

      // 尝试后备知识
      const fallbackKnowledge = this.getFallbackKnowledge(query);
      if (fallbackKnowledge) {
        return {
          found: true,
          knowledge: fallbackKnowledge,
          count: 1,
          searchMethod: 'fallback_on_error',
          error: error.message
        };
      }

      return {
        found: false,
        knowledge: '',
        count: 0,
        error: error.message,
        searchMethod: 'error'
      };
    }
  }

  // 提取关键词
  extractKeywords(query) {
    // 检查查询是否有效
    if (!query || query === null || query === undefined || typeof query !== 'string') {
      console.log(`⚠️ 关键词提取失败: 查询无效 "${query}"`);
      return [];
    }

    const keywords = [];
    const lowerQuery = query.toLowerCase();

    // 产品关键词
    if (lowerQuery.includes('畅游') || lowerQuery.includes('mini') || lowerQuery.includes('pro') || lowerQuery.includes('max')) {
      if (lowerQuery.includes('mini')) keywords.push('畅游 Mini');
      if (lowerQuery.includes('pro')) keywords.push('畅游 Pro');
      if (lowerQuery.includes('max')) keywords.push('畅游 Max');
      if (lowerQuery.includes('畅游') && !keywords.length) keywords.push('畅游');
    }

    // 价格关键词
    if (lowerQuery.includes('价格') || lowerQuery.includes('多少钱') || lowerQuery.includes('费用')) {
      keywords.push('价格', '299', '599', '999');
    }

    // 流量套餐关键词
    if (lowerQuery.includes('流量') || lowerQuery.includes('套餐') || lowerQuery.includes('包月') || lowerQuery.includes('包年')) {
      keywords.push('流量套餐', '月度套餐', '年度套餐');
    }

    // 服务关键词
    if (lowerQuery.includes('服务') || lowerQuery.includes('售后') || lowerQuery.includes('售前') || lowerQuery.includes('支持')) {
      keywords.push('售后服务', '售前服务', '技术支持');
    }

    // 商务关键词
    if (lowerQuery.includes('商务') || lowerQuery.includes('商业') || lowerQuery.includes('办公')) {
      keywords.push('商务人士', '商务');
    }

    // 如果没有找到特定关键词，使用原查询
    if (keywords.length === 0) {
      keywords.push(query);
    }

    return keywords;
  }

  // 获取后备知识
  getFallbackKnowledge(query) {
    const lowerQuery = query.toLowerCase();

    if (lowerQuery.includes('价格') || lowerQuery.includes('多少钱')) {
      return `畅游网络产品价格：
- 畅游 Mini: ¥299 (极致便携，适合个人旅行)
- 畅游 Pro: ¥599 (平衡性能，适合商务人士)
- 畅游 Max: ¥999 (旗舰性能，适合家庭团队)`;
    }

    if (lowerQuery.includes('流量') || lowerQuery.includes('套餐')) {
      return `畅游网络流量套餐：
- 按量计费：¥80/10GB(30天)、¥150/25GB(60天)
- 月度套餐：¥128/50GB、¥198/100GB、¥288/200GB
- 年度套餐：¥1299/600GB、¥1999/1200GB（老用户85折）`;
    }

    if (lowerQuery.includes('服务') || lowerQuery.includes('售后')) {
      return `畅游网络服务政策：
- 售前：免费咨询、需求评估、7天无理由试用、24小时发货
- 售后：一年保修、终身技术支持、7x12小时在线服务`;
    }

    if (lowerQuery.includes('商务')) {
      return `适合商务人士的产品：
畅游 Pro (¥599) - 15-18小时续航，支持10台设备，5G/4G智能切换，专为商务差旅设计`;
    }

    return null;
  }

  // 检查知识图谱状态
  async checkKnowledgeBaseStatus() {
    try {
      const user = await this.zepClient.user.get(this.KNOWLEDGE_USER_ID);
      return {
        status: 'healthy',
        user: user,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        status: 'error',
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  // 更新知识图谱（用于业务数据更新）
  async updateKnowledgeBase() {
    try {
      console.log('🔄 更新知识图谱...');
      // 这里可以实现增量更新逻辑
      await this.addBusinessDataToKnowledgeGraph();
      return true;
    } catch (error) {
      console.error('❌ 知识图谱更新失败:', error.message);
      return false;
    }
  }

  // 清理并重新初始化知识图谱
  async resetKnowledgeBase() {
    try {
      console.log('🗑️ 清理并重新初始化知识图谱...');

      // 删除现有用户（这会清理所有相关数据）
      try {
        await this.zepClient.user.delete(this.KNOWLEDGE_USER_ID);
        console.log('✅ 已删除现有知识图谱用户');
      } catch (deleteError) {
        console.log('⚠️ 删除用户失败（可能不存在）:', deleteError.message);
      }

      // 重新初始化
      await this.initializeKnowledgeBase();

      return true;
    } catch (error) {
      console.error('❌ 重置知识图谱失败:', error.message);
      return false;
    }
  }

  // 调试方法：列出知识图谱中的所有数据
  async debugListKnowledgeGraph() {
    try {
      console.log('🔍 调试：列出知识图谱中的所有数据...');

      // 尝试获取用户的所有图谱数据
      const searchResults = await this.zepClient.graph.search({
        userId: this.KNOWLEDGE_USER_ID,
        query: '*' // 尝试通配符搜索
      });

      console.log('📊 搜索结果结构:', JSON.stringify(searchResults, null, 2));

      if (searchResults && searchResults.edges) {
        console.log(`📈 找到 ${searchResults.edges.length} 条边数据`);
        searchResults.edges.forEach((edge, index) => {
          console.log(`${index + 1}. ${edge.fact}`);
        });
      } else {
        console.log('⚠️ 没有找到边数据');
      }

      return searchResults;
    } catch (error) {
      console.error('❌ 调试列表失败:', error.message);
      return null;
    }
  }

  // 测试不同的搜索方法
  async testSearchMethods(query) {
    console.log(`🧪 测试不同搜索方法: "${query}"`);

    const methods = [
      { name: '基础搜索', params: { userId: this.KNOWLEDGE_USER_ID, query: query } },
      { name: '空查询', params: { userId: this.KNOWLEDGE_USER_ID, query: '' } },
      { name: '通配符', params: { userId: this.KNOWLEDGE_USER_ID, query: '*' } },
      { name: '产品搜索', params: { userId: this.KNOWLEDGE_USER_ID, query: 'product' } },
      { name: '畅游搜索', params: { userId: this.KNOWLEDGE_USER_ID, query: '畅游' } }
    ];

    for (const method of methods) {
      try {
        console.log(`\n🔍 测试 ${method.name}...`);
        const result = await this.zepClient.graph.search(method.params);
        console.log(`   结果: ${result?.edges?.length || 0} 条数据`);
        if (result?.edges?.length > 0) {
          console.log(`   示例: ${result.edges[0].fact.substring(0, 100)}...`);
        }
      } catch (error) {
        console.log(`   ❌ ${method.name} 失败: ${error.message}`);
      }
    }
  }
}

module.exports = KnowledgeGraphService;
