#!/usr/bin/env node

// 迁移到统一机器人的辅助脚本
const fs = require('fs');
const path = require('path');

console.log('🔄 WhatsApp机器人迁移助手');
console.log('=' .repeat(50));

// 检查文件存在性
const filesToCheck = [
  'webhook-receiver.js',
  'whatsapp-coze-bot.js',
  'whatsapp-unified-bot.js'
];

console.log('\n📁 检查文件状态...');
const fileStatus = {};

filesToCheck.forEach(filename => {
  const exists = fs.existsSync(filename);
  fileStatus[filename] = exists;
  console.log(`   ${exists ? '✅' : '❌'} ${filename}: ${exists ? '存在' : '不存在'}`);
});

// 检查是否需要迁移
if (!fileStatus['whatsapp-unified-bot.js']) {
  console.log('\n❌ 错误: whatsapp-unified-bot.js 文件不存在！');
  console.log('请确保已经创建了统一机器人文件。');
  process.exit(1);
}

console.log('\n✅ 统一机器人文件已存在！');

// 检查旧文件
const hasOldFiles = fileStatus['webhook-receiver.js'] || fileStatus['whatsapp-coze-bot.js'];

if (hasOldFiles) {
  console.log('\n📋 发现旧文件，建议迁移步骤:');
  console.log('');
  
  if (fileStatus['webhook-receiver.js']) {
    console.log('🔸 webhook-receiver.js 功能已整合到统一机器人中:');
    console.log('   - 消息接收和状态更新处理');
    console.log('   - 健康检查和消息记录');
    console.log('   - 基础的自动回复功能');
    console.log('');
  }
  
  if (fileStatus['whatsapp-coze-bot.js']) {
    console.log('🔸 whatsapp-coze-bot.js 功能已整合到统一机器人中:');
    console.log('   - Coze API智能回复');
    console.log('   - 图片下载和处理');
    console.log('   - 用户会话管理');
    console.log('   - 火山引擎TOS集成');
    console.log('');
  }
  
  console.log('📝 迁移建议:');
  console.log('   1. 测试新的统一机器人功能');
  console.log('   2. 更新Webhook配置指向新服务');
  console.log('   3. 确认所有功能正常工作');
  console.log('   4. 备份旧文件（可选）');
  console.log('   5. 删除旧文件（可选）');
  console.log('');
  
  console.log('🚀 启动新服务:');
  console.log('   node start-unified-bot.js');
  console.log('');
  
  console.log('⚠️  注意事项:');
  console.log('   - 确保.env文件配置正确');
  console.log('   - 更新Twilio Webhook URL');
  console.log('   - 测试图片消息处理功能');
  console.log('   - 验证Coze API集成正常');
  
} else {
  console.log('\n✨ 没有发现旧文件，可以直接使用统一机器人！');
  console.log('');
  console.log('🚀 启动服务:');
  console.log('   node start-unified-bot.js');
}

console.log('\n📚 更多信息请查看: README-unified-bot.md');
console.log('');

// 检查环境配置
console.log('🔍 检查环境配置...');
require('dotenv').config();

const requiredEnvVars = [
  'TWILIO_ACCOUNT_SID',
  'TWILIO_AUTH_TOKEN',
  'COZE_API_TOKEN',
  'COZE_BOT_ID'
];

const missingVars = [];
requiredEnvVars.forEach(varName => {
  if (!process.env[varName]) {
    missingVars.push(varName);
  }
});

if (missingVars.length > 0) {
  console.log('\n⚠️  环境变量配置不完整:');
  missingVars.forEach(varName => {
    console.log(`   - ${varName}: 未配置`);
  });
  console.log('\n请在.env文件中配置这些变量。');
} else {
  console.log('\n✅ 环境变量配置完整！');
}

console.log('\n🎉 迁移检查完成！');
