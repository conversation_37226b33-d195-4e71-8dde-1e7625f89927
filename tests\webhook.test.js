const request = require('supertest');
const app = require('../src/app');
const { Message, Conversation } = require('../src/models');
const database = require('../src/config/database');

describe('Webhook API Tests', () => {
  let testMessage;

  beforeAll(async () => {
    // 连接测试数据库
    process.env.MONGODB_URI = process.env.MONGODB_TEST_URI || 'mongodb://localhost:27017/twilio_messaging_test';
    await database.connect();
  });

  afterAll(async () => {
    // 清理测试数据并断开连接
    await Message.deleteMany({});
    await Conversation.deleteMany({});
    await database.disconnect();
  });

  beforeEach(async () => {
    // 每个测试前清理数据
    await Message.deleteMany({});
    await Conversation.deleteMany({});

    // 创建测试消息
    testMessage = await Message.create({
      twilioSid: 'SM_webhook_test_123',
      direction: 'outbound',
      from: '+0987654321',
      to: '+1234567890',
      body: 'Test webhook message',
      status: 'sent',
      conversationId: 'webhook-conv-123'
    });
  });

  describe('POST /webhook/message-status', () => {
    it('should handle message status update webhook', async () => {
      const webhookData = {
        MessageSid: testMessage.twilioSid,
        MessageStatus: 'delivered',
        From: '+0987654321',
        To: '+1234567890',
        Price: '0.0075',
        PriceUnit: 'USD'
      };

      const response = await request(app)
        .post('/webhook/message-status')
        .send(webhookData)
        .expect(200);

      expect(response.body.success).toBe(true);

      // 验证消息状态已更新
      const updatedMessage = await Message.findOne({ twilioSid: testMessage.twilioSid });
      expect(updatedMessage.status).toBe('delivered');
      expect(updatedMessage.price).toBe('0.0075');
      expect(updatedMessage.priceUnit).toBe('USD');
    });

    it('should handle message status update with error', async () => {
      const webhookData = {
        MessageSid: testMessage.twilioSid,
        MessageStatus: 'failed',
        ErrorCode: '30008',
        ErrorMessage: 'Unknown error occurred',
        From: '+0987654321',
        To: '+1234567890'
      };

      const response = await request(app)
        .post('/webhook/message-status')
        .send(webhookData)
        .expect(200);

      expect(response.body.success).toBe(true);

      // 验证错误信息已保存
      const updatedMessage = await Message.findOne({ twilioSid: testMessage.twilioSid });
      expect(updatedMessage.status).toBe('failed');
      expect(updatedMessage.errorCode).toBe('30008');
      expect(updatedMessage.errorMessage).toBe('Unknown error occurred');
    });

    it('should handle webhook for non-existent message', async () => {
      const webhookData = {
        MessageSid: 'SM_non_existent_123',
        MessageStatus: 'delivered',
        From: '+0987654321',
        To: '+1234567890'
      };

      const response = await request(app)
        .post('/webhook/message-status')
        .send(webhookData)
        .expect(400);

      expect(response.body.error).toBe('Message not found');
    });
  });

  describe('POST /webhook/incoming-message', () => {
    it('should handle incoming text message', async () => {
      const webhookData = {
        MessageSid: 'SM_incoming_123',
        From: '+1234567890',
        To: '+0987654321',
        Body: 'Hello, this is an incoming message',
        NumMedia: '0'
      };

      const response = await request(app)
        .post('/webhook/incoming-message')
        .set('Content-Type', 'application/x-www-form-urlencoded')
        .send(webhookData)
        .expect(200);

      // 验证响应是TwiML格式
      expect(response.text).toContain('<?xml version="1.0" encoding="UTF-8"?>');
      expect(response.text).toContain('<Response>');

      // 验证消息已保存
      const savedMessage = await Message.findOne({ twilioSid: 'SM_incoming_123' });
      expect(savedMessage).toBeTruthy();
      expect(savedMessage.direction).toBe('inbound');
      expect(savedMessage.from).toBe('+1234567890');
      expect(savedMessage.to).toBe('+0987654321');
      expect(savedMessage.body).toBe('Hello, this is an incoming message');
      expect(savedMessage.status).toBe('received');
    });

    it('should handle incoming message with media', async () => {
      const webhookData = {
        MessageSid: 'SM_incoming_media_123',
        From: '+1234567890',
        To: '+0987654321',
        Body: 'Check out this image',
        NumMedia: '2',
        MediaUrl0: 'https://example.com/image1.jpg',
        MediaContentType0: 'image/jpeg',
        MediaUrl1: 'https://example.com/image2.png',
        MediaContentType1: 'image/png'
      };

      const response = await request(app)
        .post('/webhook/incoming-message')
        .send(webhookData)
        .expect(200);

      // 验证消息已保存
      const savedMessage = await Message.findOne({ twilioSid: 'SM_incoming_media_123' });
      expect(savedMessage).toBeTruthy();
      expect(savedMessage.mediaUrls).toHaveLength(2);
      expect(savedMessage.mediaUrls).toContain('https://example.com/image1.jpg');
      expect(savedMessage.mediaUrls).toContain('https://example.com/image2.png');
      expect(savedMessage.messageType).toBe('media');
    });

    it('should create conversation for new incoming message', async () => {
      const webhookData = {
        MessageSid: 'SM_new_conversation_123',
        From: '+9999999999',
        To: '+0987654321',
        Body: 'Starting new conversation',
        NumMedia: '0'
      };

      const response = await request(app)
        .post('/webhook/incoming-message')
        .send(webhookData)
        .expect(200);

      // 验证消息已保存
      const savedMessage = await Message.findOne({ twilioSid: 'SM_new_conversation_123' });
      expect(savedMessage).toBeTruthy();
      expect(savedMessage.conversationId).toBeTruthy();

      // 验证会话已创建
      const conversation = await Conversation.findOne({ 
        conversationId: savedMessage.conversationId 
      });
      expect(conversation).toBeTruthy();
      expect(conversation.participants).toHaveLength(2);
      expect(conversation.messageCount.total).toBe(1);
      expect(conversation.messageCount.inbound).toBe(1);
    });
  });

  describe('POST /webhook/generic', () => {
    it('should handle generic webhook for debugging', async () => {
      const webhookData = {
        testField: 'testValue',
        timestamp: new Date().toISOString()
      };

      const response = await request(app)
        .post('/webhook/generic')
        .send(webhookData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Webhook received');
      expect(response.body).toHaveProperty('timestamp');
    });
  });

  describe('GET /webhook/health', () => {
    it('should return webhook health status', async () => {
      const response = await request(app)
        .get('/webhook/health')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Webhook endpoint is healthy');
      expect(response.body).toHaveProperty('timestamp');
      expect(response.body).toHaveProperty('version');
    });
  });

  describe('Webhook Security', () => {
    it('should handle missing webhook data gracefully', async () => {
      const response = await request(app)
        .post('/webhook/message-status')
        .send({})
        .expect(400);

      expect(response.body.error).toBe('Message not found');
    });

    it('should handle malformed webhook data', async () => {
      const response = await request(app)
        .post('/webhook/incoming-message')
        .send({
          // 缺少必需字段
          MessageSid: 'SM_malformed_123'
        })
        .expect(200); // webhook应该总是返回200以避免重试

      // 验证没有创建无效消息
      const savedMessage = await Message.findOne({ twilioSid: 'SM_malformed_123' });
      expect(savedMessage).toBeTruthy(); // 应该创建消息，即使某些字段为空
    });
  });
});
