# Slack 集成配置指南

本文档介绍如何配置 Slack 集成，使智能客服系统能够处理来自 Slack 的消息。

## 功能特性

- ✅ 接收 Slack 频道和私信消息
- ✅ 复用现有的 WhatsApp 业务逻辑
- ✅ 支持意图识别和知识搜索
- ✅ 自动回复 Slack 消息
- ✅ URL 验证挑战处理
- ✅ 用户会话管理

## 前置要求

1. 已有运行的智能客服系统
2. Slack 工作区管理员权限
3. ngrok 或其他公网隧道工具

## 步骤 1: 创建 Slack App

1. 访问 [Slack API](https://api.slack.com/apps)
2. 点击 "Create New App"
3. 选择 "From scratch"
4. 输入 App 名称（如：畅游网络智能客服）
5. 选择要安装的工作区

## 步骤 2: 配置 Bot Token

1. 在 App 设置页面，进入 "OAuth & Permissions"
2. 在 "Scopes" 部分添加以下 Bot Token Scopes：
   - `chat:write` - 发送消息
   - `channels:read` - 读取频道信息
   - `groups:read` - 读取私有频道信息
   - `im:read` - 读取私信信息
   - `users:read` - 读取用户信息

3. 点击 "Install to Workspace" 安装 App
4. 复制生成的 "Bot User OAuth Token"（以 `xoxb-` 开头）

## 步骤 3: 获取 Signing Secret

1. 在 App 设置页面，进入 "Basic Information"
2. 在 "App Credentials" 部分找到 "Signing Secret"
3. 点击 "Show" 并复制该值

## 步骤 4: 配置环境变量

在项目根目录的 `.env` 文件中添加：

```env
# Slack Configuration
SLACK_BOT_TOKEN=xoxb-your-bot-token-here
SLACK_SIGNING_SECRET=your-signing-secret-here
SLACK_APP_TOKEN=xapp-your-app-token-here
```

## 步骤 5: 启动服务并暴露到公网

1. 启动智能客服系统：
```bash
npm start
```

2. 使用 ngrok 暴露到公网：
```bash
ngrok http 3003
```

3. 记录 ngrok 提供的 HTTPS URL（如：`https://abc123.ngrok.io`）

## 步骤 6: 配置 Event Subscriptions

1. 在 Slack App 设置页面，进入 "Event Subscriptions"
2. 启用 "Enable Events"
3. 在 "Request URL" 中输入：`https://your-ngrok-url.ngrok.io/slack/events`
4. Slack 会发送验证请求，系统会自动处理

## 步骤 7: 订阅事件

在 "Subscribe to bot events" 部分添加以下事件：

- `message.channels` - 公开频道消息
- `message.groups` - 私有频道消息  
- `message.im` - 私信消息
- `message.mpim` - 多人私信消息

## 步骤 8: 重新安装 App

配置完成后，需要重新安装 App：
1. 点击页面顶部的 "Reinstall App"
2. 确认权限并安装

## 步骤 9: 邀请 Bot 到频道

1. 在需要使用智能客服的频道中输入：`/invite @your-bot-name`
2. 或者在频道设置中添加 Bot

## 测试集成

运行测试脚本验证配置：

```bash
node test-slack-integration.js
```

## 使用方法

1. 在配置好的 Slack 频道中 @mention Bot
2. 或者直接向 Bot 发送私信
3. Bot 会使用相同的业务逻辑处理消息并回复

## 消息处理流程

```
Slack 消息 → 事件验证 → 意图识别 → 知识搜索 → 生成回复 → 发送到 Slack
```

## 故障排除

### 1. URL 验证失败
- 确保服务器正在运行
- 检查 ngrok URL 是否正确
- 确认防火墙设置

### 2. Bot 无法发送消息
- 检查 Bot Token 是否正确
- 确认 Bot 有 `chat:write` 权限
- 验证 Bot 已被邀请到频道

### 3. 事件未接收
- 检查 Event Subscriptions 配置
- 确认订阅了正确的事件类型
- 查看服务器日志

## 安全注意事项

1. 保护好 Bot Token 和 Signing Secret
2. 在生产环境中启用签名验证
3. 定期轮换 Token
4. 限制 Bot 权限范围

## API 端点

- `POST /slack/events` - Slack 事件接收端点
- `GET /health` - 健康检查（包含 Slack 状态）

## 日志示例

```
💬 收到Slack事件!
==================================================
📋 Slack消息详情:
   用户ID: U**********
   频道ID: C**********
   内容: 你好，我想了解随身路由器
   时间戳: **********.123456
   接收时间: 2024-01-15 10:30:00

🧠 开始处理用户消息...
🎯 分析用户意图...
   意图: business_inquiry
   置信度: 0.95
   需要知识搜索: true

✅ Slack消息处理成功 - 意图: business_inquiry, 使用知识: true
```
