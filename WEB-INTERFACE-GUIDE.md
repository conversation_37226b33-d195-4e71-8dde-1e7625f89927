# 🌐 Facebook API Web测试界面使用指南

## 🚀 快速开始

### 1. 启动服务器
```bash
node test-send-facebook.js
```

### 2. 打开Web界面
在浏览器中访问: **http://localhost:3002**

## 📋 界面功能说明

### 🔍 服务器状态检查
- **功能**: 检查服务器运行状态和当前配置
- **操作**: 点击"检查状态"按钮
- **显示信息**:
  - 验证Token状态
  - 页面ID配置
  - App ID配置
  - 页面Token是否已获取
  - 所有可用API端点

### 🔑 Token管理

#### 获取长期Token
1. **输入短期Token**: 从Facebook Graph API Explorer获取
2. **点击"获取长期Token"**: 系统会自动转换并填充到下方输入框
3. **结果显示**: 成功后会显示完整的token信息

#### 获取页面Token
1. **确保已有长期用户Token**: 从上一步获取或手动输入
2. **点击"获取页面Token"**: 系统会获取页面访问权限
3. **自动更新**: 成功后服务器会自动更新内部token

### 📝 发布文章

#### 手动输入
1. **文章内容**: 在文本框中输入要发布的内容
2. **链接(可选)**: 添加相关链接
3. **发布**: 点击"发布文章"按钮

#### 快速测试
- **填充测试内容**: 点击按钮自动填充随机测试内容
- **快捷键**: 在文章内容框中按 `Ctrl+Enter` 快速发布

### 🔗 Webhook信息
- **验证URL**: `http://localhost:3002/webhook`
- **验证Token**: `jianglai`
- **支持方法**: GET(验证) / POST(接收事件)

## 🎨 界面特性

### 🎯 用户体验
- **响应式设计**: 适配不同屏幕尺寸
- **实时反馈**: 所有操作都有加载状态和结果显示
- **错误处理**: 清晰的错误信息和成功提示
- **自动填充**: Token获取后自动填充到相关输入框

### 🔄 状态指示
- **成功**: 绿色背景，显示详细结果
- **错误**: 红色背景，显示错误信息
- **信息**: 蓝色背景，显示一般信息
- **加载**: 旋转动画指示器

### ⌨️ 快捷操作
- **回车键**: 在Token输入框中按回车执行相应操作
- **Ctrl+Enter**: 在文章内容框中快速发布
- **自动检查**: 页面加载时自动检查服务器状态

## 🔧 配置要求

### 必须配置的信息
在 `test-send-facebook.js` 中修改:
```javascript
const APP_ID = "你的AppID";              // Facebook App ID
const APP_SECRET = "你的AppSecret";      // Facebook App Secret  
const PAGE_ID = "ccjr.tti";             // 你的页面ID
```

### Facebook权限要求
- `pages_manage_posts` - 发布内容到页面
- `pages_read_engagement` - 读取页面数据

## 🐛 故障排除

### 常见问题

#### 1. 服务器连接失败
- **检查**: 确保服务器正在运行
- **解决**: 运行 `node test-send-facebook.js`

#### 2. Token获取失败
- **检查**: APP_ID和APP_SECRET是否正确
- **检查**: 短期token是否有效
- **解决**: 重新从Graph API Explorer获取token

#### 3. 发布失败
- **检查**: 是否已获取有效的页面token
- **检查**: 页面ID是否正确
- **检查**: 应用是否有足够权限

#### 4. Webhook验证失败
- **检查**: 验证token是否匹配
- **检查**: URL是否可从外网访问
- **解决**: 使用ngrok等工具暴露本地端口

## 💡 使用技巧

### 开发调试
1. **打开浏览器开发者工具**: 查看网络请求和控制台日志
2. **检查服务器日志**: 在终端中查看详细的API调用信息
3. **使用状态检查**: 随时检查当前配置状态

### 批量测试
1. **使用测试内容**: 快速生成不同的测试文章
2. **保存Token**: 获取token后可以重复使用
3. **监控结果**: 观察发布结果和Facebook响应

### 生产部署
1. **HTTPS要求**: 生产环境需要HTTPS
2. **环境变量**: 使用环境变量存储敏感信息
3. **错误监控**: 添加日志和监控系统

## 🎉 完成！

现在您可以通过友好的Web界面轻松测试所有Facebook API功能了！

如有问题，请检查浏览器控制台和服务器终端的日志信息。
