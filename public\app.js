// Facebook API 测试工具 JavaScript

const API_BASE = 'http://localhost:3002';

// 工具函数
function showLoading(elementId, show = true) {
    const loading = document.getElementById(elementId);
    if (loading) {
        loading.style.display = show ? 'inline-block' : 'none';
    }
}

function showResult(elementId, content, type = 'info') {
    const resultDiv = document.getElementById(elementId);
    if (resultDiv) {
        resultDiv.style.display = 'block';
        resultDiv.className = `result ${type}`;
        resultDiv.textContent = typeof content === 'object' ? JSON.stringify(content, null, 2) : content;
    }
}

function makeRequest(url, options = {}) {
    return fetch(url, {
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        },
        ...options
    });
}

// 检查服务器状态
async function checkStatus() {
    showLoading('statusLoading', true);
    
    try {
        const response = await makeRequest(`${API_BASE}/status`);
        const data = await response.json();
        
        if (response.ok) {
            // 添加代理状态信息
            let statusText = JSON.stringify(data, null, 2);
            if (data.proxy_enabled) {
                statusText += '\n\n🌐 代理状态: 已启用 (' + data.proxy_url + ')';
            }
            showResult('statusResult', statusText, 'success');
        } else {
            showResult('statusResult', data, 'error');
        }
    } catch (error) {
        showResult('statusResult', `连接失败: ${error.message}`, 'error');
    } finally {
        showLoading('statusLoading', false);
    }
}

// 获取长期Token
async function getLongTermToken() {
    const shortToken = document.getElementById('shortToken').value.trim();
    
    if (!shortToken) {
        showResult('longTokenResult', '请输入短期用户Token', 'error');
        return;
    }
    
    showLoading('longTokenLoading', true);
    
    try {
        const response = await makeRequest(`${API_BASE}/get-long-term-token`, {
            method: 'POST',
            body: JSON.stringify({ short_token: shortToken })
        });
        
        const data = await response.json();
        
        if (response.ok && data.access_token) {
            // 自动填充到长期Token输入框
            document.getElementById('userToken').value = data.access_token;
            showResult('longTokenResult', data, 'success');
        } else {
            showResult('longTokenResult', data, 'error');
        }
    } catch (error) {
        showResult('longTokenResult', `请求失败: ${error.message}`, 'error');
    } finally {
        showLoading('longTokenLoading', false);
    }
}

// 获取页面Token
async function getPageToken() {
    const userToken = document.getElementById('userToken').value.trim();
    
    if (!userToken) {
        showResult('pageTokenResult', '请先获取长期用户Token', 'error');
        return;
    }
    
    showLoading('pageTokenLoading', true);
    
    try {
        const response = await makeRequest(`${API_BASE}/get-page-token`, {
            method: 'POST',
            body: JSON.stringify({ user_access_token: userToken })
        });
        
        const data = await response.json();
        
        if (response.ok) {
            showResult('pageTokenResult', data, 'success');
        } else {
            showResult('pageTokenResult', data, 'error');
        }
    } catch (error) {
        showResult('pageTokenResult', `请求失败: ${error.message}`, 'error');
    } finally {
        showLoading('pageTokenLoading', false);
    }
}

// 发布文章
async function publishPost() {
    const message = document.getElementById('postMessage').value.trim();
    const link = document.getElementById('postLink').value.trim();
    
    if (!message) {
        showResult('postResult', '请输入文章内容', 'error');
        return;
    }
    
    showLoading('postLoading', true);
    
    try {
        const postData = { message };
        if (link) {
            postData.link = link;
        }
        
        const response = await makeRequest(`${API_BASE}/post`, {
            method: 'POST',
            body: JSON.stringify(postData)
        });
        
        const data = await response.json();
        
        if (response.ok) {
            showResult('postResult', data, 'success');
            // 清空表单
            document.getElementById('postMessage').value = '';
            document.getElementById('postLink').value = '';
        } else {
            showResult('postResult', data, 'error');
        }
    } catch (error) {
        showResult('postResult', `请求失败: ${error.message}`, 'error');
    } finally {
        showLoading('postLoading', false);
    }
}

// 填充测试内容
function fillTestContent() {
    const testMessages = [
        '🎉 这是一条通过API发布的测试文章！\n\n测试时间: ' + new Date().toLocaleString('zh-CN'),
        '📱 Facebook API 测试成功！\n\n功能正常运行中...',
        '🚀 自动化发布系统测试\n\n#API #Facebook #自动化',
        '✨ 新功能上线啦！\n\n欢迎大家体验我们的最新功能。',
        '🌟 每日分享\n\n今天是美好的一天，分享一些正能量给大家！'
    ];
    
    const testLinks = [
        'https://developers.facebook.com/docs/graph-api',
        'https://github.com',
        'https://nodejs.org',
        '',
        'https://developer.mozilla.org'
    ];
    
    const randomIndex = Math.floor(Math.random() * testMessages.length);
    
    document.getElementById('postMessage').value = testMessages[randomIndex];
    document.getElementById('postLink').value = testLinks[randomIndex];
}

// 页面加载完成后自动检查状态
document.addEventListener('DOMContentLoaded', function() {
    // 自动检查服务器状态
    setTimeout(checkStatus, 500);
    
    // 添加回车键支持
    document.getElementById('shortToken').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            getLongTermToken();
        }
    });
    
    document.getElementById('userToken').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            getPageToken();
        }
    });
    
    document.getElementById('postMessage').addEventListener('keypress', function(e) {
        if (e.key === 'Enter' && e.ctrlKey) {
            publishPost();
        }
    });
    
    // 添加提示信息
    console.log('🚀 Facebook API 测试工具已加载');
    console.log('💡 提示: 在文章内容框中按 Ctrl+Enter 可快速发布');
});
