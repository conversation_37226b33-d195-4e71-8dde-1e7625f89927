// 调试火山引擎TOS SDK
const { TosClient } = require('@volcengine/tos-sdk');

async function debugTOSSDK() {
  console.log('🔍 调试火山引擎TOS SDK');
  console.log('=' .repeat(50));
  
  // 测试不同的配置方式
  const configs = [
    {
      name: '完整配置',
      config: {
        accessKeyId: 'AKLTODQxYzI1ZjhiYTFlNGQxOTliZWI5MGQxMDNlODA4YTk',
        accessKeySecret: 'TldJNU4yUmxNMlJpTUdVd05HTTVZV0ZsWkRBeE5HWmlOell4TmpsbU1tTQ==',
        region: 'cn-beijing',
        endpoint: 'https://tos-cn-beijing.volces.com'
      }
    },
    {
      name: '无协议端点',
      config: {
        accessKeyId: 'AKLTODQxYzI1ZjhiYTFlNGQxOTliZWI5MGQxMDNlODA4YTk',
        accessKeySecret: 'TldJNU4yUmxNMlJpTUdVd05HTTVZV0ZsWkRBeE5HWmlOell4TmpsbU1tTQ==',
        region: 'cn-beijing',
        endpoint: 'tos-cn-beijing.volces.com'
      }
    },
    {
      name: '仅区域配置',
      config: {
        accessKeyId: 'AKLTODQxYzI1ZjhiYTFlNGQxOTliZWI5MGQxMDNlODA4YTk',
        accessKeySecret: 'TldJNU4yUmxNMlJpTUdVd05HTTVZV0ZsWkRBeE5HWmlOell4TmpsbU1tTQ==',
        region: 'cn-beijing'
      }
    }
  ];
  
  for (const { name, config } of configs) {
    console.log(`\n🧪 测试${name}:`);
    console.log('配置:', JSON.stringify(config, null, 2));
    
    try {
      const tosClient = new TosClient(config);
      console.log('✅ 客户端创建成功');
      
      // 创建测试数据
      const testData = Buffer.from('Hello TOS Debug Test!', 'utf8');
      const testKey = `debug-test-${Date.now()}.txt`;
      
      console.log(`📤 尝试上传测试文件: ${testKey}`);
      
      // 尝试上传
      const uploadParams = {
        bucket: 'whatsapp',
        key: testKey,
        body: testData,
        contentType: 'text/plain'
      };
      
      console.log('上传参数:', uploadParams);
      
      const result = await tosClient.putObject(uploadParams);
      
      console.log('✅ 上传成功!');
      console.log('结果:', {
        ETag: result.ETag,
        RequestId: result.RequestId,
        VersionId: result.VersionId
      });
      
      // 构建访问URL
      const publicUrl = `https://whatsapp.tos-cn-beijing.volces.com/${testKey}`;
      console.log('公开URL:', publicUrl);
      
      return { success: true, config: name, url: publicUrl };
      
    } catch (error) {
      console.error('❌ 失败:', error.message);
      console.error('错误类型:', error.constructor.name);
      console.error('错误代码:', error.code);
      console.error('状态码:', error.statusCode);
      
      // 详细错误分析
      if (error.message.includes('ENOTFOUND')) {
        console.error('🔍 DNS解析失败 - 检查endpoint配置');
      } else if (error.message.includes('ECONNREFUSED')) {
        console.error('🔍 连接被拒绝 - 检查网络和端口');
      } else if (error.message.includes('timeout')) {
        console.error('🔍 连接超时 - 检查网络连接');
      }
    }
  }
  
  return { success: false };
}

// 测试基本的网络连接
async function testNetworkConnectivity() {
  console.log('\n🌐 测试网络连接');
  console.log('-'.repeat(30));
  
  const axios = require('axios');
  
  const testUrls = [
    'https://tos-cn-beijing.volces.com',
    'https://www.volcengine.com',
    'https://httpbin.org/get'
  ];
  
  for (const url of testUrls) {
    try {
      console.log(`🔗 测试: ${url}`);
      const response = await axios.get(url, { timeout: 10000 });
      console.log(`✅ 连接成功 (${response.status})`);
    } catch (error) {
      console.log(`❌ 连接失败: ${error.message}`);
    }
  }
}

// 测试DNS解析
async function testDNSResolution() {
  console.log('\n🔍 测试DNS解析');
  console.log('-'.repeat(30));
  
  const dns = require('dns').promises;
  
  const domains = [
    'tos-cn-beijing.volces.com',
    'whatsapp.tos-cn-beijing.volces.com',
    'volcengine.com'
  ];
  
  for (const domain of domains) {
    try {
      console.log(`🔍 解析: ${domain}`);
      const addresses = await dns.lookup(domain);
      console.log(`✅ 解析成功: ${addresses.address}`);
    } catch (error) {
      console.log(`❌ 解析失败: ${error.message}`);
    }
  }
}

// 主函数
async function main() {
  console.log('🎯 TOS SDK 深度调试');
  console.log('=' .repeat(60));
  
  // 测试网络连接
  await testNetworkConnectivity();
  
  // 测试DNS解析
  await testDNSResolution();
  
  // 调试TOS SDK
  const result = await debugTOSSDK();
  
  console.log('\n🎊 调试总结:');
  console.log('=' .repeat(30));
  
  if (result.success) {
    console.log('✅ 找到可用配置:', result.config);
    console.log('✅ 生成的URL:', result.url);
    console.log('\n🎉 TOS上传问题已解决！');
  } else {
    console.log('❌ 所有配置都失败');
    console.log('\n📝 建议检查:');
    console.log('1. 网络连接到火山引擎');
    console.log('2. 访问密钥是否正确');
    console.log('3. 存储桶名称和权限');
    console.log('4. 区域配置是否正确');
  }
}

main().catch(error => {
  console.error('💥 调试失败:', error);
  process.exit(1);
});
