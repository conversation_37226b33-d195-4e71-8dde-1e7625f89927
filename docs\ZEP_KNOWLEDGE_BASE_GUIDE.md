# Zep 知识库问答系统指南

## 概述

本指南展示如何使用Zep Cloud的知识图谱功能构建智能问答系统，实现基于知识库内容的准确回答。

## 系统架构

```
用户问题 → 知识图谱搜索 → 相关知识检索 → AI模型生成回答 → 记忆存储
    ↓           ↓              ↓              ↓            ↓
  问题理解   语义匹配        上下文构建      智能回答      知识积累
```

## 核心功能

### 🕸️ 知识图谱存储
- **结构化知识**: 将文档内容存储为知识图谱中的事实和关系
- **语义理解**: 支持自然语言的语义搜索
- **关联发现**: 自动发现知识点之间的关联关系

### 🔍 智能检索
- **相似度搜索**: 基于语义相似度检索相关知识
- **上下文感知**: 结合对话历史提供更准确的检索
- **多维度匹配**: 支持关键词、概念、实体等多维度匹配

### 🤖 智能问答
- **知识增强**: 基于检索到的知识生成准确回答
- **上下文连贯**: 保持多轮对话的连贯性
- **来源标注**: 可追溯回答的知识来源

## 快速开始

### 1. 环境配置

确保已配置必要的环境变量：

```env
ZEP_API_KEY=your_zep_api_key
ARK_API_KEY=your_volcengine_api_key
VOLCENGINE_MODEL_ENDPOINT=your_model_endpoint
```

### 2. 运行演示

```bash
# 完整的知识库问答演示
npm run demo:zep-knowledge

# 或直接运行
node zep-knowledge-base-demo.js
```

### 3. 基本使用示例

```javascript
const { ZepClient } = require('@getzep/zep-cloud');
const OpenAI = require('openai');

class KnowledgeQA {
  constructor() {
    this.zepClient = new ZepClient({
      apiKey: process.env.ZEP_API_KEY
    });
    
    this.openaiClient = new OpenAI({
      apiKey: process.env.ARK_API_KEY,
      baseURL: 'https://ark.cn-beijing.volces.com/api/v3',
    });
  }

  // 导入知识到图谱
  async importKnowledge(userId, knowledge) {
    await this.zepClient.graph.add({
      userId: userId,
      type: 'text',
      data: knowledge
    });
  }

  // 基于知识库的问答
  async answerQuestion(userId, sessionId, question) {
    // 1. 搜索相关知识
    const searchResults = await this.zepClient.graph.search({
      userId: userId,
      query: question
    });

    let knowledgeContext = '';
    if (searchResults.edges && searchResults.edges.length > 0) {
      knowledgeContext = searchResults.edges
        .slice(0, 3)
        .map(edge => edge.fact)
        .join('\n\n');
    }

    // 2. 添加问题到记忆
    const memoryResult = await this.zepClient.memory.add(sessionId, {
      messages: [{ roleType: 'user', content: question }],
      returnContext: true
    });

    // 3. 构建提示
    const systemPrompt = `基于以下知识库内容回答用户问题：

相关知识：
${knowledgeContext || '暂无相关知识'}

对话历史：
${memoryResult.context || ''}

请提供准确、有帮助的回答。`;

    // 4. 生成回答
    const completion = await this.openaiClient.chat.completions.create({
      model: process.env.VOLCENGINE_MODEL_ENDPOINT,
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: question }
      ],
      temperature: 0.3,
      max_tokens: 1000
    });

    const answer = completion.choices[0].message.content;

    // 5. 保存回答到记忆
    await this.zepClient.memory.add(sessionId, {
      messages: [{ roleType: 'assistant', content: answer }]
    });

    return answer;
  }
}
```

## 知识库内容示例

演示系统包含以下领域的知识：

### 1. 人工智能基础
- AI定义和分类
- 机器学习类型
- 深度学习概念
- 应用场景

### 2. 编程最佳实践
- 代码质量原则
- 版本控制
- 测试策略
- 代码审查

### 3. 产品管理
- 产品经理职责
- 用户研究方法
- 产品开发流程
- 数据驱动决策

### 4. Node.js开发
- 核心特性
- 常用框架
- 最佳实践
- 性能优化

## 高级功能

### 1. 批量知识导入

```javascript
async function importMultipleDocuments(userId, documents) {
  for (const doc of documents) {
    const formattedContent = `
文档标题: ${doc.title}
分类: ${doc.category}
标签: ${doc.tags.join(', ')}

${doc.content}
    `;
    
    await zepClient.graph.add({
      userId: userId,
      type: 'text',
      data: formattedContent
    });
  }
}
```

### 2. 知识检索优化

```javascript
async function enhancedKnowledgeSearch(userId, query) {
  // 搜索相关知识
  const searchResults = await zepClient.graph.search({
    userId: userId,
    query: query
  });

  if (searchResults.edges && searchResults.edges.length > 0) {
    // 按相关性排序并格式化
    return searchResults.edges
      .slice(0, 5) // 取前5个最相关的结果
      .map((edge, index) => ({
        rank: index + 1,
        content: edge.fact,
        relevance: edge.score || 'N/A'
      }));
  }

  return [];
}
```

### 3. 多轮对话优化

```javascript
async function contextAwareQA(userId, sessionId, question) {
  // 获取对话历史
  const memory = await zepClient.memory.get(sessionId);
  
  // 结合历史上下文优化搜索查询
  const enhancedQuery = memory.context 
    ? `${question} 上下文：${memory.context.substring(0, 200)}`
    : question;

  // 执行增强搜索
  const knowledge = await enhancedKnowledgeSearch(userId, enhancedQuery);
  
  // 生成回答...
}
```

## 最佳实践

### 1. 知识组织
- **结构化内容**: 使用标题、分类、标签等结构化信息
- **适当粒度**: 将大文档拆分为合适大小的知识片段
- **关键词优化**: 在内容中包含相关的关键词和同义词

### 2. 搜索优化
- **查询扩展**: 对用户问题进行同义词扩展
- **结果过滤**: 根据相关性分数过滤搜索结果
- **上下文利用**: 结合对话历史优化搜索效果

### 3. 回答质量
- **知识引用**: 在回答中明确引用知识来源
- **准确性检查**: 确保回答与知识库内容一致
- **完整性保证**: 提供完整、有用的回答

### 4. 性能优化
- **缓存策略**: 对频繁查询的结果进行缓存
- **批量操作**: 批量导入和更新知识内容
- **异步处理**: 使用异步操作避免阻塞

## 应用场景

### 1. 企业知识库
- 内部文档问答
- 政策制度查询
- 技术文档检索

### 2. 客户服务
- 产品信息咨询
- 常见问题解答
- 技术支持

### 3. 教育培训
- 课程内容问答
- 学习资料检索
- 知识点解释

### 4. 专业咨询
- 行业知识查询
- 专业术语解释
- 最佳实践分享

## 故障排除

### 常见问题

1. **知识检索无结果**
   - 检查知识是否正确导入
   - 优化搜索查询的表达方式
   - 等待知识图谱处理完成

2. **回答质量不佳**
   - 改进知识内容的结构和质量
   - 调整AI模型的温度参数
   - 优化系统提示词

3. **性能问题**
   - 减少单次搜索的结果数量
   - 优化知识内容的大小
   - 使用缓存机制

### 调试技巧

1. **查看搜索结果**：
```javascript
const results = await zepClient.graph.search({
  userId: userId,
  query: query
});
console.log('搜索结果:', results.edges?.length || 0);
```

2. **监控记忆状态**：
```javascript
const memory = await zepClient.memory.get(sessionId);
console.log('对话轮数:', memory.messages?.length || 0);
```

## 相关资源

- [Zep Cloud 官方文档](https://help.getzep.com/)
- [知识图谱最佳实践](https://help.getzep.com/understanding-the-graph)
- [火山方舟模型文档](https://www.volcengine.com/docs/82379)

## 总结

通过Zep的知识图谱功能，您可以构建强大的知识库问答系统，实现：

- ✅ 智能知识检索和匹配
- ✅ 上下文感知的多轮对话
- ✅ 基于知识库的准确回答
- ✅ 持续学习和知识积累

这为构建企业级智能问答助手提供了完整的解决方案。
