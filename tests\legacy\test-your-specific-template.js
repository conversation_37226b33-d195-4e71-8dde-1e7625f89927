// 专门测试您的特定模板的脚本
require('dotenv').config();
const twilio = require('twilio');

// 您的特定模板配置
const YOUR_TEMPLATE_CONFIG = {
  contentSid: 'HXb5b62575e6e4ff6129ad7c8efe1f983e',
  defaultVariables: '{"1":"12/1","2":"3pm"}',
  targetNumber: 'whatsapp:+***********',
  sandboxNumber: 'whatsapp:+***********'
};

class YourTemplateTest {
  constructor() {
    this.accountSid = process.env.TWILIO_ACCOUNT_SID;
    this.authToken = process.env.TWILIO_AUTH_TOKEN;
    this.client = twilio(this.accountSid, this.authToken);
  }

  // 发送您的原始模板消息
  async sendOriginalTemplate() {
    console.log('📱 发送您的原始模板消息...\n');
    console.log('📋 配置信息:');
    console.log(`   模板SID: ${YOUR_TEMPLATE_CONFIG.contentSid}`);
    console.log(`   发送方: ${YOUR_TEMPLATE_CONFIG.sandboxNumber}`);
    console.log(`   接收方: ${YOUR_TEMPLATE_CONFIG.targetNumber}`);
    console.log(`   变量: ${YOUR_TEMPLATE_CONFIG.defaultVariables}`);
    console.log('');

    try {
      const message = await this.client.messages.create({
        from: YOUR_TEMPLATE_CONFIG.sandboxNumber,
        contentSid: YOUR_TEMPLATE_CONFIG.contentSid,
        contentVariables: YOUR_TEMPLATE_CONFIG.defaultVariables,
        to: YOUR_TEMPLATE_CONFIG.targetNumber
      });

      console.log('✅ 原始模板消息发送成功!');
      console.log(`   消息SID: ${message.sid}`);
      console.log(`   状态: ${message.status}`);
      console.log(`   发送时间: ${new Date().toISOString()}`);

      // 监控状态变化
      await this.monitorMessageStatus(message.sid);

      return message;

    } catch (error) {
      console.error('❌ 原始模板消息发送失败:', error.message);
      this.analyzeTemplateError(error);
      throw error;
    }
  }

  // 测试不同的变量组合
  async testVariableCombinations() {
    console.log('🧪 测试不同的变量组合...\n');

    const variableTests = [
      {
        name: '原始变量',
        variables: '{"1":"12/1","2":"3pm"}',
        description: '您提供的原始变量'
      },
      {
        name: '不同日期时间',
        variables: '{"1":"12/15","2":"2:30pm"}',
        description: '测试不同的日期和时间格式'
      },
      {
        name: '今天明天',
        variables: '{"1":"今天","2":"下午"}',
        description: '测试中文日期时间'
      },
      {
        name: '数字格式',
        variables: '{"1":"01/01","2":"15:00"}',
        description: '测试24小时制时间'
      },
      {
        name: '长文本',
        variables: '{"1":"December 1st, 2024","2":"3:00 PM EST"}',
        description: '测试较长的文本变量'
      }
    ];

    const results = [];

    for (const test of variableTests) {
      console.log(`📝 测试: ${test.name}`);
      console.log(`   描述: ${test.description}`);
      console.log(`   变量: ${test.variables}`);

      try {
        const message = await this.client.messages.create({
          from: YOUR_TEMPLATE_CONFIG.sandboxNumber,
          contentSid: YOUR_TEMPLATE_CONFIG.contentSid,
          contentVariables: test.variables,
          to: YOUR_TEMPLATE_CONFIG.targetNumber
        });

        console.log(`   ✅ 发送成功: ${message.sid}`);
        
        results.push({
          test: test.name,
          success: true,
          messageSid: message.sid,
          variables: test.variables
        });

      } catch (error) {
        console.log(`   ❌ 发送失败: ${error.message}`);
        
        results.push({
          test: test.name,
          success: false,
          error: error.message,
          code: error.code,
          variables: test.variables
        });
      }

      console.log('');
      
      // 等待1秒避免发送过快
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    // 显示测试结果汇总
    this.displayTestResults(results);
    return results;
  }

  // 验证模板是否存在和有效
  async validateTemplate() {
    console.log('🔍 验证模板有效性...\n');

    try {
      // 尝试获取模板详情
      const content = await this.client.content.contents(YOUR_TEMPLATE_CONFIG.contentSid).fetch();
      
      console.log('✅ 模板验证成功!');
      console.log(`   模板名称: ${content.friendlyName || 'Unnamed'}`);
      console.log(`   模板SID: ${content.sid}`);
      console.log(`   语言: ${content.language || 'N/A'}`);
      console.log(`   创建时间: ${content.dateCreated}`);
      console.log(`   更新时间: ${content.dateUpdated}`);
      console.log('');

      // 获取批准状态
      try {
        const approvals = await this.client.content.contents(YOUR_TEMPLATE_CONFIG.contentSid).approvalRequests.list();
        
        if (approvals.length > 0) {
          console.log('📋 批准状态:');
          approvals.forEach((approval, index) => {
            console.log(`   ${index + 1}. 状态: ${approval.status}`);
            console.log(`      名称: ${approval.name || 'N/A'}`);
            console.log(`      类别: ${approval.category || 'N/A'}`);
          });
        } else {
          console.log('⚠️ 未找到批准记录');
        }
      } catch (approvalError) {
        console.log('⚠️ 无法获取批准状态:', approvalError.message);
      }

      return content;

    } catch (error) {
      console.error('❌ 模板验证失败:', error.message);
      
      if (error.code === 20404) {
        console.error('   原因: 模板不存在');
        console.error(`   检查模板SID: ${YOUR_TEMPLATE_CONFIG.contentSid}`);
      } else {
        console.error(`   错误代码: ${error.code}`);
        console.error(`   详细信息: ${error.moreInfo || 'N/A'}`);
      }
      
      throw error;
    }
  }

  // 检查接收号码状态
  async checkRecipientStatus() {
    console.log('📱 检查接收号码状态...\n');
    console.log(`   目标号码: ${YOUR_TEMPLATE_CONFIG.targetNumber}`);
    console.log('');

    // 发送一个简单的测试消息来验证号码
    try {
      const testMessage = await this.client.messages.create({
        from: YOUR_TEMPLATE_CONFIG.sandboxNumber,
        body: '🧪 这是一条测试消息，用于验证号码状态。如果您收到此消息，说明号码配置正确。',
        to: YOUR_TEMPLATE_CONFIG.targetNumber
      });

      console.log('✅ 接收号码验证成功!');
      console.log(`   测试消息SID: ${testMessage.sid}`);
      console.log('   号码已正确加入沙盒');
      
      return true;

    } catch (error) {
      console.error('❌ 接收号码验证失败:', error.message);
      
      if (error.code === 63016) {
        console.error('   原因: 号码未加入WhatsApp沙盒');
        console.error('   解决方案:');
        console.error('     1. 确保 +*********** 已发送 "join <keyword>" 到 +1 415 523 8886');
        console.error('     2. 等待确认消息');
        console.error('     3. 重新测试');
      }
      
      return false;
    }
  }

  // 监控消息状态变化
  async monitorMessageStatus(messageSid, maxWaitTime = 30000) {
    console.log(`⏳ 监控消息状态变化 (最多等待${maxWaitTime/1000}秒)...`);
    
    const startTime = Date.now();
    let lastStatus = null;

    const checkStatus = async () => {
      try {
        const message = await this.client.messages(messageSid).fetch();
        
        if (message.status !== lastStatus) {
          lastStatus = message.status;
          console.log(`📊 状态更新: ${message.status} (${new Date().toLocaleTimeString()})`);
          
          if (message.errorCode) {
            console.log(`   错误代码: ${message.errorCode}`);
            console.log(`   错误信息: ${message.errorMessage}`);
          }
          
          if (message.price) {
            console.log(`   价格: ${message.price} ${message.priceUnit}`);
          }
        }

        // 如果消息已送达或失败，停止监控
        if (['delivered', 'read', 'failed', 'undelivered'].includes(message.status)) {
          console.log('✅ 消息状态监控完成');
          return message;
        }

        // 如果还在等待且未超时，继续监控
        if (Date.now() - startTime < maxWaitTime) {
          setTimeout(checkStatus, 2000); // 每2秒检查一次
        } else {
          console.log('⏰ 监控超时，停止状态检查');
        }

      } catch (error) {
        console.log('⚠️ 状态检查失败:', error.message);
      }
    };

    // 开始监控
    setTimeout(checkStatus, 2000);
  }

  // 分析模板相关错误
  analyzeTemplateError(error) {
    console.log('\n🔧 模板错误分析:');
    
    switch (error.code) {
      case 63017:
        console.log('   问题: 内容模板相关错误');
        console.log('   可能原因:');
        console.log('     1. 模板ID无效或不存在');
        console.log('     2. 模板未获得批准');
        console.log('     3. 模板变量格式错误');
        console.log('     4. 模板变量数量不匹配');
        console.log('   建议检查:');
        console.log(`     - 模板SID: ${YOUR_TEMPLATE_CONFIG.contentSid}`);
        console.log(`     - 变量格式: ${YOUR_TEMPLATE_CONFIG.defaultVariables}`);
        break;
        
      case 63016:
        console.log('   问题: 接收号码相关错误');
        console.log('   可能原因:');
        console.log('     1. 号码未加入WhatsApp沙盒');
        console.log('     2. 号码格式错误');
        console.log('     3. 沙盒会话已过期');
        console.log('   建议操作:');
        console.log(`     - 确认 ${YOUR_TEMPLATE_CONFIG.targetNumber} 已正确加入沙盒`);
        break;
        
      default:
        console.log(`   错误代码: ${error.code}`);
        console.log(`   错误信息: ${error.message}`);
        console.log(`   详细信息: ${error.moreInfo || 'N/A'}`);
    }
  }

  // 显示测试结果
  displayTestResults(results) {
    console.log('📊 变量测试结果汇总:');
    console.log(`   总测试数: ${results.length}`);
    console.log(`   成功数: ${results.filter(r => r.success).length}`);
    console.log(`   失败数: ${results.filter(r => !r.success).length}`);
    console.log('');

    results.forEach((result, index) => {
      const status = result.success ? '✅' : '❌';
      console.log(`   ${index + 1}. ${status} ${result.test}`);
      console.log(`      变量: ${result.variables}`);
      if (result.success) {
        console.log(`      消息SID: ${result.messageSid}`);
      } else {
        console.log(`      错误: ${result.error}`);
      }
      console.log('');
    });
  }
}

// 主函数
async function main() {
  console.log('🎯 您的特定模板测试工具');
  console.log('=' .repeat(50));
  console.log(`📋 目标模板: ${YOUR_TEMPLATE_CONFIG.contentSid}`);
  console.log(`📱 目标号码: ${YOUR_TEMPLATE_CONFIG.targetNumber}`);
  console.log('');

  const tester = new YourTemplateTest();
  const command = process.argv[2];

  try {
    switch (command) {
      case 'send':
        await tester.sendOriginalTemplate();
        break;

      case 'validate':
        await tester.validateTemplate();
        break;

      case 'check':
        await tester.checkRecipientStatus();
        break;

      case 'test-vars':
        await tester.testVariableCombinations();
        break;

      case 'full':
        console.log('🔄 执行完整测试流程...\n');
        await tester.validateTemplate();
        console.log('\n' + '='.repeat(30) + '\n');
        await tester.checkRecipientStatus();
        console.log('\n' + '='.repeat(30) + '\n');
        await tester.sendOriginalTemplate();
        break;

      default:
        console.log('📋 可用命令:');
        console.log('   send      - 发送原始模板消息');
        console.log('   validate  - 验证模板有效性');
        console.log('   check     - 检查接收号码状态');
        console.log('   test-vars - 测试不同变量组合');
        console.log('   full      - 执行完整测试流程');
        console.log('');
        console.log('📝 使用示例:');
        console.log('   node test-your-specific-template.js send');
        console.log('   node test-your-specific-template.js full');
        console.log('');
        console.log('🎯 默认执行发送测试...');
        await tester.sendOriginalTemplate();
    }

  } catch (error) {
    console.log('\n💥 测试失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = YourTemplateTest;
