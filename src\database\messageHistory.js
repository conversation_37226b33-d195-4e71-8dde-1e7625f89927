// 消息历史管理数据库服务
const sqlite3 = require('sqlite3').verbose();
const path = require('path');

class MessageHistory {
  constructor() {
    // 使用与WebhookLogger相同的数据库
    this.dbPath = path.join(__dirname, '../../data/webhook_logs.db');
    this.db = null;
    this.init();
  }

  // 初始化数据库连接
  async init() {
    return new Promise((resolve, reject) => {
      this.db = new sqlite3.Database(this.dbPath, (err) => {
        if (err) {
          console.error('❌ 消息历史数据库连接失败:', err.message);
          reject(err);
        } else {
          console.log('✅ 消息历史数据库连接成功');
          resolve();
        }
      });
    });
  }

  // 保存消息
  async saveMessage(conversationId, webhookLogId, messageType, content, senderId, recipientId, messageSid = null, metadata = {}) {
    return new Promise((resolve, reject) => {
      const sql = `
        INSERT INTO messages (
          conversation_id, webhook_log_id, message_type, content, 
          sender_id, recipient_id, message_sid, metadata, timestamp
        )
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
      `;

      this.db.run(sql, [
        conversationId,
        webhookLogId,
        messageType,
        content,
        senderId,
        recipientId,
        messageSid,
        JSON.stringify(metadata)
      ], function(err) {
        if (err) {
          console.error('❌ 保存消息失败:', err.message);
          reject(err);
        } else {
          console.log(`✅ 消息保存成功 - ID: ${this.lastID}, 类型: ${messageType}`);
          resolve({
            id: this.lastID,
            conversation_id: conversationId,
            message_type: messageType,
            content: content.substring(0, 50) + (content.length > 50 ? '...' : '')
          });
        }
      });
    });
  }

  // 获取对话列表
  async getConversations(platform = null, limit = 50, offset = 0) {
    return new Promise((resolve, reject) => {
      let sql = `
        SELECT 
          c.id,
          c.platform,
          c.channel_id,
          c.created_at,
          c.updated_at,
          u.display_name,
          u.platform_user_id,
          u.phone_number,
          COUNT(m.id) as message_count,
          MAX(m.timestamp) as last_message_time,
          (SELECT content FROM messages WHERE conversation_id = c.id ORDER BY timestamp DESC LIMIT 1) as last_message
        FROM conversations c
        LEFT JOIN users u ON c.user_id = u.id
        LEFT JOIN messages m ON c.id = m.conversation_id
      `;

      const params = [];
      if (platform) {
        sql += ' WHERE c.platform = ?';
        params.push(platform);
      }

      sql += `
        GROUP BY c.id
        ORDER BY last_message_time DESC
        LIMIT ? OFFSET ?
      `;
      params.push(limit, offset);

      this.db.all(sql, params, (err, rows) => {
        if (err) {
          console.error('❌ 获取对话列表失败:', err.message);
          reject(err);
        } else {
          console.log(`✅ 获取对话列表成功 - 数量: ${rows.length}`);
          resolve(rows);
        }
      });
    });
  }

  // 获取对话中的消息
  async getConversationMessages(conversationId, limit = 100, offset = 0) {
    return new Promise((resolve, reject) => {
      const sql = `
        SELECT 
          m.*,
          u.display_name as sender_name,
          u.platform_user_id as sender_platform_id
        FROM messages m
        LEFT JOIN conversations c ON m.conversation_id = c.id
        LEFT JOIN users u ON c.user_id = u.id
        WHERE m.conversation_id = ?
        ORDER BY m.timestamp ASC
        LIMIT ? OFFSET ?
      `;

      this.db.all(sql, [conversationId, limit, offset], (err, rows) => {
        if (err) {
          console.error('❌ 获取对话消息失败:', err.message);
          reject(err);
        } else {
          console.log(`✅ 获取对话消息成功 - 对话ID: ${conversationId}, 消息数: ${rows.length}`);
          resolve(rows);
        }
      });
    });
  }

  // 搜索消息
  async searchMessages(query, platform = null, startDate = null, endDate = null, limit = 100) {
    return new Promise((resolve, reject) => {
      let sql = `
        SELECT 
          m.*,
          c.platform,
          u.display_name,
          u.platform_user_id
        FROM messages m
        JOIN conversations c ON m.conversation_id = c.id
        LEFT JOIN users u ON c.user_id = u.id
        WHERE m.content LIKE ?
      `;

      const params = [`%${query}%`];

      if (platform) {
        sql += ' AND c.platform = ?';
        params.push(platform);
      }

      if (startDate) {
        sql += ' AND m.timestamp >= ?';
        params.push(startDate);
      }

      if (endDate) {
        sql += ' AND m.timestamp <= ?';
        params.push(endDate);
      }

      sql += ' ORDER BY m.timestamp DESC LIMIT ?';
      params.push(limit);

      this.db.all(sql, params, (err, rows) => {
        if (err) {
          console.error('❌ 搜索消息失败:', err.message);
          reject(err);
        } else {
          console.log(`✅ 搜索消息成功 - 查询: "${query}", 结果数: ${rows.length}`);
          resolve(rows);
        }
      });
    });
  }

  // 获取用户列表
  async getUsers(platform = null, limit = 100, offset = 0) {
    return new Promise((resolve, reject) => {
      let sql = `
        SELECT 
          u.*,
          COUNT(DISTINCT c.id) as conversation_count,
          COUNT(m.id) as message_count,
          MAX(m.timestamp) as last_activity
        FROM users u
        LEFT JOIN conversations c ON u.id = c.user_id
        LEFT JOIN messages m ON c.id = m.conversation_id
      `;

      const params = [];
      if (platform) {
        sql += ' WHERE u.platform = ?';
        params.push(platform);
      }

      sql += `
        GROUP BY u.id
        ORDER BY last_activity DESC
        LIMIT ? OFFSET ?
      `;
      params.push(limit, offset);

      this.db.all(sql, params, (err, rows) => {
        if (err) {
          console.error('❌ 获取用户列表失败:', err.message);
          reject(err);
        } else {
          console.log(`✅ 获取用户列表成功 - 数量: ${rows.length}`);
          resolve(rows);
        }
      });
    });
  }

  // 获取统计信息
  async getStatistics(platform = null, days = 30) {
    return new Promise((resolve, reject) => {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);
      const startDateStr = startDate.toISOString();

      let platformFilter = '';
      const params = [startDateStr];
      
      if (platform) {
        platformFilter = ' AND c.platform = ?';
        params.push(platform);
      }

      const sql = `
        SELECT 
          COUNT(DISTINCT u.id) as total_users,
          COUNT(DISTINCT c.id) as total_conversations,
          COUNT(m.id) as total_messages,
          COUNT(CASE WHEN m.message_type = 'incoming' THEN 1 END) as incoming_messages,
          COUNT(CASE WHEN m.message_type = 'outgoing' THEN 1 END) as outgoing_messages,
          COUNT(CASE WHEN m.timestamp >= ? THEN 1 END) as recent_messages
        FROM messages m
        JOIN conversations c ON m.conversation_id = c.id
        JOIN users u ON c.user_id = u.id
        WHERE 1=1 ${platformFilter}
      `;

      this.db.get(sql, params, (err, row) => {
        if (err) {
          console.error('❌ 获取统计信息失败:', err.message);
          reject(err);
        } else {
          console.log('✅ 获取统计信息成功');
          resolve(row);
        }
      });
    });
  }

  // 获取每日消息统计
  async getDailyMessageStats(platform = null, days = 30) {
    return new Promise((resolve, reject) => {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);
      const startDateStr = startDate.toISOString().split('T')[0];

      let platformFilter = '';
      const params = [startDateStr];
      
      if (platform) {
        platformFilter = ' AND c.platform = ?';
        params.push(platform);
      }

      const sql = `
        SELECT 
          DATE(m.timestamp) as date,
          COUNT(m.id) as total_messages,
          COUNT(CASE WHEN m.message_type = 'incoming' THEN 1 END) as incoming_messages,
          COUNT(CASE WHEN m.message_type = 'outgoing' THEN 1 END) as outgoing_messages,
          COUNT(DISTINCT c.id) as active_conversations
        FROM messages m
        JOIN conversations c ON m.conversation_id = c.id
        WHERE DATE(m.timestamp) >= ? ${platformFilter}
        GROUP BY DATE(m.timestamp)
        ORDER BY date DESC
      `;

      this.db.all(sql, params, (err, rows) => {
        if (err) {
          console.error('❌ 获取每日消息统计失败:', err.message);
          reject(err);
        } else {
          console.log(`✅ 获取每日消息统计成功 - 天数: ${rows.length}`);
          resolve(rows);
        }
      });
    });
  }

  // 获取平台统计
  async getPlatformStats() {
    return new Promise((resolve, reject) => {
      const sql = `
        SELECT 
          c.platform,
          COUNT(DISTINCT u.id) as user_count,
          COUNT(DISTINCT c.id) as conversation_count,
          COUNT(m.id) as message_count,
          MAX(m.timestamp) as last_activity
        FROM conversations c
        LEFT JOIN users u ON c.user_id = u.id
        LEFT JOIN messages m ON c.id = m.conversation_id
        GROUP BY c.platform
        ORDER BY message_count DESC
      `;

      this.db.all(sql, [], (err, rows) => {
        if (err) {
          console.error('❌ 获取平台统计失败:', err.message);
          reject(err);
        } else {
          console.log('✅ 获取平台统计成功');
          resolve(rows);
        }
      });
    });
  }

  // 关闭数据库连接
  close() {
    if (this.db) {
      this.db.close((err) => {
        if (err) {
          console.error('❌ 关闭消息历史数据库连接失败:', err.message);
        } else {
          console.log('✅ 消息历史数据库连接已关闭');
        }
      });
    }
  }
}

module.exports = MessageHistory;
