const Joi = require('joi');

/**
 * 电话号码验证
 */
const phoneNumberSchema = Joi.string().pattern(/^\+[1-9]\d{1,14}$/);

/**
 * 消息ID验证
 */
const messageIdSchema = Joi.string().length(24).hex();

/**
 * 会话ID验证
 */
const conversationIdSchema = Joi.string().min(1).max(100);

/**
 * 日期验证
 */
const dateSchema = Joi.date().iso();

/**
 * 验证电话号码格式
 * @param {string} phoneNumber - 电话号码
 * @returns {boolean} 是否有效
 */
const isValidPhoneNumber = (phoneNumber) => {
  const { error } = phoneNumberSchema.validate(phoneNumber);
  return !error;
};

/**
 * 验证消息ID格式
 * @param {string} messageId - 消息ID
 * @returns {boolean} 是否有效
 */
const isValidMessageId = (messageId) => {
  const { error } = messageIdSchema.validate(messageId);
  return !error;
};

/**
 * 验证会话ID格式
 * @param {string} conversationId - 会话ID
 * @returns {boolean} 是否有效
 */
const isValidConversationId = (conversationId) => {
  const { error } = conversationIdSchema.validate(conversationId);
  return !error;
};

/**
 * 清理电话号码（移除非数字字符，添加+号）
 * @param {string} phoneNumber - 原始电话号码
 * @returns {string} 清理后的电话号码
 */
const cleanPhoneNumber = (phoneNumber) => {
  if (!phoneNumber) return '';
  
  // 移除所有非数字字符
  const cleaned = phoneNumber.replace(/\D/g, '');
  
  // 如果没有+号，添加+号
  return cleaned.startsWith('+') ? cleaned : `+${cleaned}`;
};

/**
 * 验证URL格式
 * @param {string} url - URL
 * @returns {boolean} 是否有效
 */
const isValidUrl = (url) => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

/**
 * 验证媒体URL数组
 * @param {Array} mediaUrls - 媒体URL数组
 * @returns {boolean} 是否有效
 */
const isValidMediaUrls = (mediaUrls) => {
  if (!Array.isArray(mediaUrls)) return false;
  if (mediaUrls.length === 0) return true;
  if (mediaUrls.length > 10) return false; // Twilio限制
  
  return mediaUrls.every(url => isValidUrl(url));
};

/**
 * 验证消息内容
 * @param {string} body - 消息内容
 * @param {Array} mediaUrls - 媒体URL数组
 * @returns {Object} 验证结果
 */
const validateMessageContent = (body, mediaUrls = []) => {
  const errors = [];
  
  // 检查是否有内容
  if (!body && (!mediaUrls || mediaUrls.length === 0)) {
    errors.push('Message must have either body text or media');
  }
  
  // 检查消息长度
  if (body && body.length > 1600) {
    errors.push('Message body cannot exceed 1600 characters');
  }
  
  // 检查媒体URL
  if (mediaUrls && !isValidMediaUrls(mediaUrls)) {
    errors.push('Invalid media URLs');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

module.exports = {
  phoneNumberSchema,
  messageIdSchema,
  conversationIdSchema,
  dateSchema,
  isValidPhoneNumber,
  isValidMessageId,
  isValidConversationId,
  cleanPhoneNumber,
  isValidUrl,
  isValidMediaUrls,
  validateMessageContent,
};
