const { ZepClient } = require('@getzep/zep-cloud');
const OpenAI = require('openai');
require('dotenv').config();

describe('Zep + 火山方舟大模型集成测试', () => {
  let zepClient;
  let openaiClient;
  let testUserId;
  let testSessionId;

  beforeAll(() => {
    // 检查必要的环境变量
    if (!process.env.ZEP_API_KEY) {
      throw new Error('缺少 ZEP_API_KEY 环境变量');
    }
    
    if (!process.env.ARK_API_KEY) {
      console.warn('⚠️ 缺少 ARK_API_KEY 环境变量，将跳过火山方舟相关测试');
    }

    // 初始化Zep客户端
    zepClient = new ZepClient({
      apiKey: process.env.ZEP_API_KEY
    });

    // 初始化火山方舟客户端
    if (process.env.ARK_API_KEY) {
      openaiClient = new OpenAI({
        apiKey: process.env.ARK_API_KEY,
        baseURL: 'https://ark.cn-beijing.volces.com/api/v3',
      });
    }

    // 生成测试用的唯一ID
    const timestamp = Date.now();
    testUserId = `volcengine-user-${timestamp}`;
    testSessionId = `volcengine-session-${timestamp}`;
  });

  afterAll(async () => {
    // 清理测试数据
    try {
      await zepClient.user.delete(testUserId);
    } catch (error) {
      console.log('清理用户时出错:', error.message);
    }
  });

  describe('基础设置', () => {
    test('应该能够初始化Zep和火山方舟客户端', () => {
      expect(zepClient).toBeDefined();
      
      if (process.env.ARK_API_KEY) {
        expect(openaiClient).toBeDefined();
      } else {
        console.log('跳过火山方舟客户端测试 - 缺少API密钥');
      }
    });

    test('应该能够创建测试用户和会话', async () => {
      // 创建用户
      const user = await zepClient.user.add({
        userId: testUserId,
        email: '<EMAIL>',
        firstName: '火山',
        lastName: '测试',
        metadata: {
          source: 'volcengine-integration-test',
          model: 'doubao'
        }
      });

      expect(user).toBeDefined();
      expect(user.userId).toBe(testUserId);

      // 创建会话
      const session = await zepClient.memory.addSession({
        sessionId: testSessionId,
        userId: testUserId,
        metadata: {
          model: 'volcengine-doubao',
          type: 'ai-chat'
        }
      });

      expect(session).toBeDefined();
      expect(session.sessionId).toBe(testSessionId);
    });
  });

  describe('智能对话流程', () => {
    test('应该能够进行完整的AI对话流程', async () => {
      if (!process.env.ARK_API_KEY) {
        console.log('跳过AI对话测试 - 缺少ARK_API_KEY');
        return;
      }

      // 模拟用户输入
      const userMessage = '你好，我想了解一下人工智能的发展历史。';
      
      // 1. 添加用户消息到Zep记忆
      console.log('📝 添加用户消息到Zep记忆...');
      const memoryResult = await zepClient.memory.add(testSessionId, {
        messages: [{ roleType: 'user', content: userMessage }],
        returnContext: true
      });

      expect(memoryResult).toBeDefined();

      // 2. 构建包含上下文的提示
      const systemPrompt = `你是一个专业的AI助手。请基于以下上下文信息回答用户问题：

${memoryResult.context || '暂无历史上下文'}

请用简洁、专业的语言回答用户的问题。`;

      // 3. 调用火山方舟大模型
      console.log('🤖 调用火山方舟大模型生成回复...');
      
      // 注意：您需要替换为实际的模型端点ID
      const modelEndpoint = process.env.VOLCENGINE_MODEL_ENDPOINT || 'ep-20241221105607-2w8zx';
      
      const completion = await openaiClient.chat.completions.create({
        model: modelEndpoint,
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userMessage }
        ],
        temperature: 0.7,
        max_tokens: 1000
      });

      const aiResponse = completion.choices[0].message.content;
      expect(aiResponse).toBeDefined();
      expect(aiResponse.length).toBeGreaterThan(0);

      console.log('✅ AI回复生成成功:', aiResponse.substring(0, 100) + '...');

      // 4. 添加AI回复到Zep记忆
      console.log('💾 添加AI回复到Zep记忆...');
      await zepClient.memory.add(testSessionId, {
        messages: [{ roleType: 'assistant', content: aiResponse }]
      });

      // 5. 验证记忆已更新
      const updatedMemory = await zepClient.memory.get(testSessionId);
      expect(updatedMemory.messages.length).toBeGreaterThanOrEqual(2);
      
      const lastUserMessage = updatedMemory.messages.find(m => m.roleType === 'user');
      const lastAssistantMessage = updatedMemory.messages.find(m => m.roleType === 'assistant');
      
      expect(lastUserMessage.content).toBe(userMessage);
      expect(lastAssistantMessage.content).toBe(aiResponse);

      console.log('✅ 完整对话流程测试成功');
    }, 30000); // 增加超时时间

    test('应该能够进行多轮对话并保持上下文', async () => {
      if (!process.env.ARK_API_KEY) {
        console.log('跳过多轮对话测试 - 缺少ARK_API_KEY');
        return;
      }

      const conversations = [
        '请简单介绍一下机器学习。',
        '那深度学习和机器学习有什么区别？',
        '能举个深度学习在实际应用中的例子吗？'
      ];

      for (let i = 0; i < conversations.length; i++) {
        const userMessage = conversations[i];
        console.log(`\n🔄 进行第 ${i + 1} 轮对话...`);
        console.log(`👤 用户: ${userMessage}`);

        // 添加用户消息并获取上下文
        const memoryResult = await zepClient.memory.add(testSessionId, {
          messages: [{ roleType: 'user', content: userMessage }],
          returnContext: true
        });

        // 构建包含历史上下文的提示
        const systemPrompt = `你是一个专业的AI助手。请基于以下对话历史和上下文信息回答用户问题：

${memoryResult.context || '暂无历史上下文'}

请保持回答的连贯性，并参考之前的对话内容。用简洁、专业的语言回答。`;

        // 调用火山方舟大模型
        const modelEndpoint = process.env.VOLCENGINE_MODEL_ENDPOINT || 'ep-20241221105607-2w8zx';
        
        const completion = await openaiClient.chat.completions.create({
          model: modelEndpoint,
          messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: userMessage }
          ],
          temperature: 0.7,
          max_tokens: 800
        });

        const aiResponse = completion.choices[0].message.content;
        console.log(`🤖 AI: ${aiResponse.substring(0, 100)}...`);

        // 添加AI回复到记忆
        await zepClient.memory.add(testSessionId, {
          messages: [{ roleType: 'assistant', content: aiResponse }]
        });

        expect(aiResponse).toBeDefined();
        expect(aiResponse.length).toBeGreaterThan(0);
      }

      // 验证最终记忆状态
      const finalMemory = await zepClient.memory.get(testSessionId);
      expect(finalMemory.messages.length).toBeGreaterThanOrEqual(6); // 3轮对话 = 6条消息
      
      console.log('✅ 多轮对话测试成功');
      console.log(`📊 总消息数: ${finalMemory.messages.length}`);
      console.log(`📋 有上下文: ${finalMemory.context ? '是' : '否'}`);
      console.log(`📝 有摘要: ${finalMemory.summary ? '是' : '否'}`);
    }, 60000); // 增加超时时间
  });

  describe('知识图谱增强', () => {
    test('应该能够结合知识图谱进行智能问答', async () => {
      if (!process.env.ARK_API_KEY) {
        console.log('跳过知识图谱增强测试 - 缺少ARK_API_KEY');
        return;
      }

      // 1. 添加用户档案到知识图谱
      console.log('🕸️ 添加用户档案到知识图谱...');
      const userProfile = {
        name: '火山测试用户',
        occupation: 'AI研究员',
        interests: ['机器学习', '深度学习', '自然语言处理', '计算机视觉'],
        experience: '5年AI研发经验',
        specialization: '大语言模型',
        current_project: '多模态AI系统开发',
        location: '北京',
        education: '计算机科学博士'
      };

      await zepClient.graph.add({
        userId: testUserId,
        type: 'json',
        data: JSON.stringify(userProfile)
      });

      // 等待知识图谱处理
      await new Promise(resolve => setTimeout(resolve, 3000));

      // 2. 进行个性化对话
      const personalizedQuery = '基于我的背景，你能推荐一些适合我的AI研究方向吗？';
      
      const memoryResult = await zepClient.memory.add(testSessionId, {
        messages: [{ roleType: 'user', content: personalizedQuery }],
        returnContext: true
      });

      // 3. 搜索知识图谱获取用户信息
      let userInfo = '';
      try {
        const graphSearch = await zepClient.graph.search({
          userId: testUserId,
          query: '用户的专业背景和兴趣是什么？'
        });

        if (graphSearch.edges && graphSearch.edges.length > 0) {
          userInfo = graphSearch.edges.map(edge => edge.fact).join('\n');
        }
      } catch (error) {
        console.log('知识图谱搜索可能需要更多时间处理');
      }

      // 4. 构建增强的提示
      const enhancedPrompt = `你是一个专业的AI研究顾问。请基于以下信息为用户提供个性化建议：

用户档案信息：
${userInfo || '用户是一名AI研究员，专注于大语言模型研究'}

对话历史：
${memoryResult.context || ''}

请根据用户的专业背景和兴趣，提供具体、实用的研究方向建议。`;

      // 5. 调用火山方舟大模型
      const modelEndpoint = process.env.VOLCENGINE_MODEL_ENDPOINT || 'ep-20241221105607-2w8zx';
      
      const completion = await openaiClient.chat.completions.create({
        model: modelEndpoint,
        messages: [
          { role: 'system', content: enhancedPrompt },
          { role: 'user', content: personalizedQuery }
        ],
        temperature: 0.8,
        max_tokens: 1200
      });

      const personalizedResponse = completion.choices[0].message.content;
      
      // 6. 保存回复到记忆
      await zepClient.memory.add(testSessionId, {
        messages: [{ roleType: 'assistant', content: personalizedResponse }]
      });

      expect(personalizedResponse).toBeDefined();
      expect(personalizedResponse.length).toBeGreaterThan(0);
      
      console.log('✅ 个性化AI回复生成成功');
      console.log('🎯 回复预览:', personalizedResponse.substring(0, 150) + '...');
    }, 45000);
  });

  describe('错误处理和边界情况', () => {
    test('应该能够处理API调用失败的情况', async () => {
      if (!process.env.ARK_API_KEY) {
        console.log('跳过错误处理测试 - 缺少ARK_API_KEY');
        return;
      }

      // 测试无效的模型端点
      const invalidClient = new OpenAI({
        apiKey: process.env.ARK_API_KEY,
        baseURL: 'https://ark.cn-beijing.volces.com/api/v3',
      });

      await expect(
        invalidClient.chat.completions.create({
          model: 'invalid-model-endpoint',
          messages: [{ role: 'user', content: '测试消息' }]
        })
      ).rejects.toThrow();
    });

    test('应该能够处理空消息和异常输入', async () => {
      // 测试空消息
      await expect(
        zepClient.memory.add(testSessionId, {
          messages: [{ roleType: 'user', content: '' }]
        })
      ).resolves.toBeDefined();

      // 测试特殊字符
      const specialMessage = '测试特殊字符：@#$%^&*()_+{}|:"<>?[]\\;\',./ 🚀🤖💡';
      
      const result = await zepClient.memory.add(testSessionId, {
        messages: [{ roleType: 'user', content: specialMessage }]
      });

      expect(result).toBeDefined();
    });
  });
});
