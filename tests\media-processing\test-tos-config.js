// 测试火山引擎TOS配置
const { TosClient } = require('@volcengine/tos-sdk');

async function testTOSConfiguration() {
  console.log('🧪 测试火山引擎TOS配置');
  console.log('=' .repeat(50));
  
  // 测试不同的配置方式
  const configs = [
    {
      name: '当前配置',
      config: {
        accessKeyId: 'AKLTODQxYzI1ZjhiYTFlNGQxOTliZWI5MGQxMDNlODA4YTk',
        accessKeySecret: 'TldJNU4yUmxNMlJpTUdVd05HTTVZV0ZsWkRBeE5HWmlOell4TmpsbU1tTQ==',
        region: 'cn-beijing',
        endpoint: 'https://tos-cn-beijing.volces.com'
      }
    },
    {
      name: '标准配置1',
      config: {
        accessKeyId: 'AKLTODQxYzI1ZjhiYTFlNGQxOTliZWI5MGQxMDNlODA4YTk',
        accessKeySecret: 'TldJNU4yUmxNMlJpTUdVd05HTTVZV0ZsWkRBeE5HWmlOell4TmpsbU1tTQ==',
        region: 'cn-beijing',
        endpoint: 'tos-cn-beijing.volces.com'
      }
    },
    {
      name: '标准配置2',
      config: {
        accessKeyId: 'AKLTODQxYzI1ZjhiYTFlNGQxOTliZWI5MGQxMDNlODA4YTk',
        accessKeySecret: 'TldJNU4yUmxNMlJpTUdVd05HTTVZV0ZsWkRBeE5HWmlOell4TmpsbU1tTQ==',
        region: 'cn-beijing'
        // 不指定endpoint，让SDK自动构建
      }
    }
  ];
  
  for (const { name, config } of configs) {
    console.log(`\n🔧 测试${name}:`);
    console.log('   配置:', JSON.stringify(config, null, 2));
    
    try {
      const tosClient = new TosClient(config);
      console.log('✅ TOS客户端创建成功');
      
      // 测试存储桶操作
      try {
        const bucketResult = await tosClient.headBucket({ bucket: 'whatsapp' });
        console.log('✅ 存储桶访问成功');
        console.log('   结果:', bucketResult);
      } catch (bucketError) {
        console.log('⚠️ 存储桶访问失败:', bucketError.message);
        console.log('   状态码:', bucketError.statusCode);
        console.log('   错误代码:', bucketError.code);
      }
      
    } catch (clientError) {
      console.log('❌ TOS客户端创建失败:', clientError.message);
    }
  }
}

// 测试URL构建
function testURLConstruction() {
  console.log('\n🧪 测试URL构建');
  console.log('-'.repeat(30));
  
  const bucketName = 'whatsapp';
  const region = 'cn-beijing';
  const fileName = 'test-image.jpg';
  
  const urlFormats = [
    `https://${bucketName}.tos-${region}.volces.com/${fileName}`,
    `https://${bucketName}.${region}.volces.com/${fileName}`,
    `https://tos-${region}.volces.com/${bucketName}/${fileName}`,
    `https://${bucketName}.tos-${region}.volcengine.com/${fileName}`
  ];
  
  console.log('📋 可能的URL格式:');
  urlFormats.forEach((url, index) => {
    console.log(`   ${index + 1}. ${url}`);
  });
}

// 测试简单上传
async function testSimpleUpload() {
  console.log('\n🧪 测试简单上传');
  console.log('-'.repeat(30));
  
  try {
    const tosClient = new TosClient({
      accessKeyId: 'AKLTODQxYzI1ZjhiYTFlNGQxOTliZWI5MGQxMDNlODA4YTk',
      accessKeySecret: 'TldJNU4yUmxNMlJpTUdVd05HTTVZV0ZsWkRBeE5HWmlOell4TmpsbU1tTQ==',
      region: 'cn-beijing'
    });
    
    console.log('📤 尝试上传测试文件...');
    
    // 创建一个小的测试文件
    const testData = Buffer.from('Hello TOS Test!', 'utf8');
    
    const uploadParams = {
      bucket: 'whatsapp',
      key: 'test/config-test.txt',
      body: testData,
      contentType: 'text/plain'
    };
    
    console.log('📋 上传参数:', uploadParams);
    
    const result = await tosClient.putObject(uploadParams);
    
    console.log('✅ 上传成功!');
    console.log('   ETag:', result.ETag);
    console.log('   RequestId:', result.RequestId);
    
    // 构建访问URL
    const publicUrl = `https://whatsapp.tos-cn-beijing.volces.com/test/config-test.txt`;
    console.log('   公开URL:', publicUrl);
    
    return true;
    
  } catch (error) {
    console.error('❌ 上传失败:', error.message);
    console.error('   错误详情:', error);
    return false;
  }
}

// 主函数
async function main() {
  console.log('🎯 火山引擎TOS配置测试套件');
  console.log('=' .repeat(60));
  
  // 测试配置
  await testTOSConfiguration();
  
  // 测试URL构建
  testURLConstruction();
  
  // 测试简单上传
  const uploadSuccess = await testSimpleUpload();
  
  console.log('\n🎊 测试总结:');
  console.log('=' .repeat(30));
  console.log('1. 配置测试: 查看上面的结果');
  console.log('2. URL构建: 已显示可能的格式');
  console.log('3. 简单上传:', uploadSuccess ? '✅ 成功' : '❌ 失败');
  
  console.log('\n📝 建议:');
  console.log('- 检查访问密钥是否正确');
  console.log('- 确认存储桶名称和区域');
  console.log('- 验证网络连接到火山引擎');
  console.log('- 检查存储桶权限设置');
}

main().catch(error => {
  console.error('💥 测试执行失败:', error);
  process.exit(1);
});
