// 测试WhatsApp媒体集成
const axios = require('axios');

async function testWhatsAppMediaIntegration() {
  console.log('🧪 测试WhatsApp媒体集成');
  console.log('=' .repeat(50));
  
  const webhookUrl = 'http://localhost:3002/whatsapp-webhook';
  
  try {
    // 1. 测试普通文本消息
    console.log('\n1️⃣ 测试普通文本消息...');
    const textMessage = {
      From: 'whatsapp:+***********',
      To: 'whatsapp:+***********',
      Body: '你好，我想了解一下产品',
      ProfileName: 'TestUser',
      NumMedia: '0'
    };
    
    try {
      const textResponse = await axios.post(webhookUrl, textMessage, {
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        timeout: 30000
      });
      console.log('✅ 文本消息测试成功');
      console.log('   状态码:', textResponse.status);
    } catch (textError) {
      console.log('⚠️ 文本消息测试失败:', textError.message);
    }
    
    // 等待3秒
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // 2. 测试图片消息
    console.log('\n2️⃣ 测试图片消息...');
    const imageMessage = {
      From: 'whatsapp:+***********',
      To: 'whatsapp:+***********',
      Body: '这是什么产品？',
      ProfileName: 'TestUser',
      NumMedia: '1',
      MediaUrl0: 'https://api.twilio.com/2010-04-01/Accounts/AC7657552c992e2a3737961532e7e609d1/Messages/MMaa6c44a7ff79f4ad4ce1f49781ddfdbf/Media/ME10a86f9b7de3168ed95543d5f99ea554',
      MediaContentType0: 'image/jpeg'
    };
    
    try {
      const imageResponse = await axios.post(webhookUrl, imageMessage, {
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        timeout: 60000 // 图片处理可能需要更长时间
      });
      console.log('✅ 图片消息测试成功');
      console.log('   状态码:', imageResponse.status);
    } catch (imageError) {
      console.log('⚠️ 图片消息测试失败:', imageError.message);
    }
    
    // 等待3秒
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // 3. 测试纯图片消息（无文字）
    console.log('\n3️⃣ 测试纯图片消息...');
    const pureImageMessage = {
      From: 'whatsapp:+***********',
      To: 'whatsapp:+***********',
      Body: '',
      ProfileName: 'TestUser',
      NumMedia: '1',
      MediaUrl0: 'https://httpbin.org/image/png',
      MediaContentType0: 'image/png'
    };
    
    try {
      const pureImageResponse = await axios.post(webhookUrl, pureImageMessage, {
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        timeout: 60000
      });
      console.log('✅ 纯图片消息测试成功');
      console.log('   状态码:', pureImageResponse.status);
    } catch (pureImageError) {
      console.log('⚠️ 纯图片消息测试失败:', pureImageError.message);
    }
    
    console.log('\n✅ WhatsApp媒体集成测试完成');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

// 测试服务器健康状态
async function testServerHealth() {
  console.log('\n🧪 测试服务器健康状态');
  console.log('-'.repeat(30));
  
  try {
    const healthResponse = await axios.get('http://localhost:3002/health', {
      timeout: 5000
    });
    
    console.log('✅ 服务器健康检查通过');
    console.log('   状态码:', healthResponse.status);
    console.log('   响应:', healthResponse.data);
    
  } catch (healthError) {
    console.log('❌ 服务器健康检查失败:', healthError.message);
    console.log('   请确保WhatsApp机器人服务正在运行');
  }
}

// 测试会话管理
async function testSessionManagement() {
  console.log('\n🧪 测试会话管理');
  console.log('-'.repeat(30));
  
  try {
    const sessionsResponse = await axios.get('http://localhost:3002/sessions', {
      timeout: 5000
    });
    
    console.log('✅ 会话查看成功');
    console.log('   活跃会话数:', sessionsResponse.data.sessions?.length || 0);
    
    if (sessionsResponse.data.sessions && sessionsResponse.data.sessions.length > 0) {
      console.log('📋 会话详情:');
      sessionsResponse.data.sessions.forEach((session, index) => {
        console.log(`   ${index + 1}. 用户: ${session.phoneNumber}`);
        console.log(`      姓名: ${session.name}`);
        console.log(`      消息数: ${session.messageCount}`);
        console.log(`      最后活动: ${session.lastActivity}`);
      });
    }
    
  } catch (sessionError) {
    console.log('⚠️ 会话查看失败:', sessionError.message);
  }
}

// 主函数
async function main() {
  console.log('🎯 WhatsApp媒体集成测试套件');
  console.log('=' .repeat(60));
  
  // 首先检查服务器是否运行
  await testServerHealth();
  
  // 等待2秒
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // 测试消息处理
  await testWhatsAppMediaIntegration();
  
  // 等待2秒
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // 测试会话管理
  await testSessionManagement();
  
  console.log('\n🎊 测试总结:');
  console.log('1. 服务器健康检查 - 验证服务运行状态');
  console.log('2. 文本消息处理 - 验证基本功能');
  console.log('3. 图片消息处理 - 验证媒体处理流程');
  console.log('4. 纯图片消息 - 验证无文字图片处理');
  console.log('5. 会话管理 - 验证用户会话状态');
  
  console.log('\n📝 注意事项:');
  console.log('- 确保WhatsApp机器人服务在端口3002运行');
  console.log('- 图片处理可能需要较长时间');
  console.log('- 检查服务器日志查看详细处理过程');
}

main();
