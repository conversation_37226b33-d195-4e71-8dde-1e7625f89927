# WhatsApp模板消息测试完整指南

## 🎯 您的模板配置

根据您提供的代码，我们将测试以下配置：

```javascript
{
  contentSid: 'HXb5b62575e6e4ff6129ad7c8efe1f983e',
  variables: '{"1":"12/1","2":"3pm"}',
  targetNumber: 'whatsapp:+***********',
  sandboxNumber: 'whatsapp:+14155238886'
}
```

## 🚀 快速测试步骤

### 第1步：系统诊断
```bash
# 检查整个系统状态
node whatsapp-debug-tool.js full
```

### 第2步：验证您的特定模板
```bash
# 验证模板是否存在和有效
node test-your-specific-template.js validate
```

### 第3步：检查接收号码
```bash
# 检查目标号码是否已正确配置
node test-your-specific-template.js check
```

### 第4步：发送您的原始模板消息
```bash
# 发送您提供的确切配置
node test-your-specific-template.js send
```

### 第5步：测试不同变量组合
```bash
# 测试多种变量组合
node test-your-specific-template.js test-vars
```

## 📋 详细测试命令

### 🔍 系统诊断工具
```bash
# 完整系统诊断
node whatsapp-debug-tool.js full

# 单独检查组件
node whatsapp-debug-tool.js account    # 账户状态
node whatsapp-debug-tool.js templates  # 内容模板
node whatsapp-debug-tool.js messages   # 最近消息
node whatsapp-debug-tool.js test whatsapp:+***********  # 测试特定号码
```

### 🎯 您的特定模板测试
```bash
# 完整测试流程
node test-your-specific-template.js full

# 单独测试步骤
node test-your-specific-template.js validate   # 验证模板
node test-your-specific-template.js check      # 检查号码
node test-your-specific-template.js send       # 发送消息
node test-your-specific-template.js test-vars  # 测试变量
```

### 🛠️ 模板管理工具
```bash
# 列出所有模板
node template-manager.js list

# 获取特定模板详情
node template-manager.js details HXb5b62575e6e4ff6129ad7c8efe1f983e

# 测试特定模板
node template-manager.js test HXb5b62575e6e4ff6129ad7c8efe1f983e whatsapp:+*********** '{"1":"12/1","2":"3pm"}'

# 验证变量格式
node template-manager.js validate '{"1":"12/1","2":"3pm"}'
```

### 📱 原始代码测试
```bash
# 使用您的原始代码结构
node your-template-message.js send

# 测试不同变量
node your-template-message.js test
```

## 📊 预期测试结果

### ✅ 成功的测试应该显示：

1. **系统诊断成功**
```
✅ 账户状态: 正常
✅ 电话号码: X 个可用
✅ 内容模板: X 个可用
✅ 沙盒状态: 正常
🎯 系统整体状态: ✅ 健康
```

2. **模板验证成功**
```
✅ 模板验证成功!
   模板名称: Your Template Name
   模板SID: HXb5b62575e6e4ff6129ad7c8efe1f983e
   语言: en
```

3. **号码检查成功**
```
✅ 接收号码验证成功!
   测试消息SID: SM1234567890abcdef
   号码已正确加入沙盒
```

4. **消息发送成功**
```
✅ 模板消息发送成功!
   消息SID: SM1234567890abcdef
   状态: queued
📊 状态更新: delivered
```

### ❌ 常见错误和解决方案

#### 错误63016 - 号码未加入沙盒
```
❌ 接收号码验证失败: The number +*********** is not a valid WhatsApp number
```
**解决方案：**
1. 确保 +*********** 已发送 "join <keyword>" 到 +1 415 523 8886
2. 等待确认消息
3. 重新测试

#### 错误63017 - 模板问题
```
❌ 模板消息发送失败: Content template not found or not approved
```
**解决方案：**
1. 检查模板ID是否正确
2. 确认模板已获得批准
3. 验证变量格式

#### 错误20003 - 认证失败
```
❌ 账户验证失败: Authentication Error
```
**解决方案：**
1. 检查 .env 文件中的 TWILIO_AUTH_TOKEN
2. 确认 Account SID 正确

## 🔧 故障排除步骤

### 1. 基础检查
```bash
# 检查环境变量
echo $TWILIO_ACCOUNT_SID
echo $TWILIO_AUTH_TOKEN

# 检查网络连接
ping api.twilio.com
```

### 2. 逐步诊断
```bash
# 步骤1：账户验证
node whatsapp-debug-tool.js account

# 步骤2：模板检查
node template-manager.js list

# 步骤3：号码测试
node whatsapp-debug-tool.js test whatsapp:+***********

# 步骤4：发送测试
node test-your-specific-template.js send
```

### 3. 详细日志
所有脚本都会提供详细的错误信息和建议解决方案。

## 📈 测试进度跟踪

- [ ] ✅ 系统诊断通过
- [ ] ✅ 模板验证成功
- [ ] ✅ 接收号码验证成功
- [ ] ✅ 原始模板消息发送成功
- [ ] ✅ 变量组合测试成功
- [ ] ✅ 在WhatsApp中收到消息

## 🎉 成功标志

当所有测试通过时，您应该：
1. 看到所有 ✅ 成功标志
2. 在 +*********** 的WhatsApp中收到测试消息
3. 消息状态显示为 "delivered" 或 "read"

## 💡 下一步建议

测试成功后，您可以：
1. 自定义模板内容和变量
2. 添加更多接收号码
3. 集成到您的业务系统
4. 设置自动化消息发送

---

**准备开始测试？** 运行第一个命令：`node whatsapp-debug-tool.js full`
