const express = require('express');
const webhookController = require('../controllers/webhookController');

const router = express.Router();

/**
 * @route POST /webhook/message-status
 * @desc 处理Twilio消息状态回调
 * @access Public (Twilio webhook)
 */
router.post('/message-status', webhookController.handleMessageStatus);

/**
 * @route POST /webhook/incoming-message
 * @desc 处理Twilio入站消息
 * @access Public (Twilio webhook)
 */
router.post('/incoming-message', webhookController.handleIncomingMessage);

/**
 * @route POST /webhook/generic
 * @desc 通用webhook处理器（用于调试）
 * @access Public (Twilio webhook)
 */
router.post('/generic', webhookController.handleGenericWebhook);

/**
 * @route GET /webhook/health
 * @desc Webhook健康检查
 * @access Public
 */
router.get('/health', webhookController.healthCheck);

module.exports = router;
