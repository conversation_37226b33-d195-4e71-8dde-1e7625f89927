# Slack OAuth 配置指南

本文档介绍如何配置 Slack OAuth 授权流程，使用户能够轻松地将智能客服 Bot 添加到他们的 Slack 工作区。

## 功能特性

- ✅ OAuth 2.0 授权流程
- ✅ 自动获取访问令牌
- ✅ 用户友好的授权页面
- ✅ 错误处理和状态验证
- ✅ 安全的令牌交换

## OAuth 端点

### 1. 授权启动端点
- **路径**: `GET /slack/oauth/start`
- **功能**: 重定向用户到 Slack 授权页面
- **示例**: `http://localhost:3002/slack/oauth/start`

### 2. 授权回调端点
- **路径**: `GET /slack/oauth/callback`
- **功能**: 处理 Slack 授权回调，交换访问令牌
- **参数**: `code`, `state`, `error`

## 配置步骤

### 步骤 1: 配置 Slack App OAuth 设置

1. 访问 [Slack API Apps](https://api.slack.com/apps)
2. 选择您的 App
3. 进入 "OAuth & Permissions"

### 步骤 2: 设置 Redirect URLs

在 "Redirect URLs" 部分添加：

**开发环境**:
```
http://localhost:3002/slack/oauth/callback
```

**生产环境**:
```
https://your-domain.com/slack/oauth/callback
```

### 步骤 3: 配置 Bot Token Scopes

在 "Scopes" → "Bot Token Scopes" 部分添加：

- `chat:write` - 发送消息
- `channels:read` - 读取频道信息
- `groups:read` - 读取私有频道信息
- `im:read` - 读取私信信息
- `users:read` - 读取用户信息

### 步骤 4: 获取 OAuth 凭据

在 "Basic Information" 页面获取：

1. **Client ID** - 在 "App Credentials" 部分
2. **Client Secret** - 在 "App Credentials" 部分（点击 "Show"）

### 步骤 5: 配置环境变量

在 `.env` 文件中添加：

```env
# Slack OAuth Configuration
SLACK_CLIENT_ID=your-client-id-here
SLACK_CLIENT_SECRET=your-client-secret-here
SLACK_REDIRECT_URI=http://localhost:3002/slack/oauth/callback
```

**生产环境示例**:
```env
SLACK_REDIRECT_URI=https://your-domain.com/slack/oauth/callback
```

## OAuth 流程

### 1. 用户授权流程

```
用户访问 → /slack/oauth/start → Slack授权页面 → 用户授权 → /slack/oauth/callback → 成功页面
```

### 2. 详细步骤

1. **启动授权**: 用户访问 `/slack/oauth/start`
2. **重定向**: 系统重定向到 Slack 授权页面
3. **用户授权**: 用户在 Slack 页面确认授权
4. **回调处理**: Slack 重定向到 `/slack/oauth/callback`
5. **令牌交换**: 系统使用授权码换取访问令牌
6. **完成**: 显示成功页面

## 测试 OAuth 流程

### 1. 运行测试脚本

```bash
node test-slack-oauth.js
```

### 2. 手动测试

1. 启动服务器: `npm start`
2. 访问: `http://localhost:3002/slack/oauth/start`
3. 完成 Slack 授权流程

### 3. 验证结果

成功授权后会看到包含以下信息的页面：
- 团队名称
- Bot ID
- 授权权限范围

## 安全考虑

### 1. State 参数
- 系统自动生成随机 state 参数
- 用于防止 CSRF 攻击
- 在回调中验证 state 值

### 2. HTTPS 要求
- 生产环境必须使用 HTTPS
- Slack 要求 OAuth 回调使用安全连接

### 3. 凭据保护
- Client Secret 必须保密
- 不要在前端代码中暴露凭据
- 定期轮换 OAuth 凭据

## 错误处理

### 常见错误

1. **invalid_client_id**
   - 检查 SLACK_CLIENT_ID 配置
   - 确认 Client ID 正确

2. **invalid_redirect_uri**
   - 检查 SLACK_REDIRECT_URI 配置
   - 确认 URL 在 Slack App 中已配置

3. **access_denied**
   - 用户拒绝授权
   - 引导用户重新授权

### 调试技巧

1. 检查服务器日志
2. 验证环境变量配置
3. 确认 Slack App 设置
4. 使用测试脚本验证端点

## 令牌管理

### 当前实现
- 令牌信息记录到控制台日志
- 适用于开发和测试环境

### 生产环境建议
- 将令牌保存到数据库
- 实现令牌刷新机制
- 添加令牌过期处理

### 示例数据库结构
```sql
CREATE TABLE slack_installations (
  id SERIAL PRIMARY KEY,
  team_id VARCHAR(255) NOT NULL,
  team_name VARCHAR(255),
  bot_user_id VARCHAR(255),
  bot_access_token TEXT,
  user_id VARCHAR(255),
  access_token TEXT,
  scope TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 分发应用

### Slack App Directory
1. 完善应用信息和图标
2. 通过 Slack 审核流程
3. 发布到 App Directory

### 自定义分发
1. 提供 OAuth 授权链接
2. 用户点击链接完成授权
3. 应用自动安装到工作区

## 监控和分析

### 关键指标
- 授权成功率
- 授权失败原因
- 用户留存率
- 使用频率

### 日志记录
- OAuth 流程开始
- 授权成功/失败
- 令牌交换结果
- 错误详情

## 故障排除

### 1. 授权页面无法访问
- 检查服务器是否运行
- 验证端口配置
- 确认防火墙设置

### 2. 回调失败
- 检查 Redirect URI 配置
- 验证 Client Secret
- 查看服务器错误日志

### 3. 令牌无效
- 检查权限范围配置
- 验证令牌格式
- 确认应用未被撤销授权
