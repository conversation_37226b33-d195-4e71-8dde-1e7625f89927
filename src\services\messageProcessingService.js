// 消息处理服务 - 核心业务逻辑
require('dotenv').config();
const { ZepClient } = require('@getzep/zep-cloud');
const OpenAI = require('openai');
const KnowledgeGraphService = require('./knowledgeGraphService');
const IntentRecognitionService = require('./intentRecognitionService');

class MessageProcessingService {
  constructor() {
    this.zepClient = new ZepClient({
      apiKey: process.env.ZEP_API_KEY
    });
    
    this.openaiClient = new OpenAI({
      apiKey: process.env.ARK_API_KEY,
      baseURL: 'https://ark.cn-beijing.volces.com/api/v3',
    });
    
    this.knowledgeService = new KnowledgeGraphService();
    this.intentService = new IntentRecognitionService();
    
    // 用户会话存储
    this.userSessions = {};
  }

  // 处理用户消息的主要方法
  async processMessage(from, message, userName) {
    try {
      console.log('\n🧠 开始处理用户消息...');
      console.log(`📝 用户: ${userName || 'Unknown'}`);
      console.log(`💬 消息: ${message}`);
      
      // 1. 获取或创建用户会话
      const userId = from.replace('whatsapp:', '');
      const session = await this.getOrCreateUserSession(userId, userName);
      
      // 2. 意图识别（现在是异步的）
      console.log('🎯 分析用户意图...');
      const intentResult = await this.intentService.recognizeIntent(message);
      console.log(`   意图: ${intentResult.intent}`);
      console.log(`   置信度: ${intentResult.confidence.toFixed(2)}`);
      console.log(`   需要知识搜索: ${intentResult.needsKnowledgeSearch}`);
      if (intentResult.reasoning) {
        console.log(`   推理: ${intentResult.reasoning}`);
      }
      
      // 3. 根据意图决定处理方式
      let response;
      switch (intentResult.intent) {
        case 'business_inquiry':
        case 'business_related':
          response = await this.handleBusinessInquiry(message, intentResult, session);
          break;
        case 'possible_business':
          response = await this.handlePossibleBusinessInquiry(message, intentResult, session);
          break;
        case 'greeting':
          response = await this.handleGreeting(message, session);
          break;
        case 'chitchat':
          response = await this.handleChitChat(message, session);
          break;
        default:
          response = await this.handleGeneralInquiry(message, session);
      }
      
      // 4. 保存对话到记忆
      await this.saveConversationToMemory(session, message, response.text);
      
      // 5. 更新会话信息
      this.updateSessionInfo(session);
      
      console.log(`✅ 消息处理完成: ${response.text.substring(0, 100)}...`);
      
      return {
        success: true,
        response: response.text,
        intent: intentResult.intent,
        confidence: intentResult.confidence,
        usedKnowledge: response.usedKnowledge || false
      };
      
    } catch (error) {
      console.error('❌ 消息处理失败:', error.message);
      return {
        success: false,
        response: '很抱歉，处理您的消息时出现了问题。请稍后再试或联系客服。',
        error: error.message
      };
    }
  }

  // 处理业务咨询
  async handleBusinessInquiry(message, intentResult, session) {
    console.log('💼 处理业务咨询...');
    
    // 搜索知识图谱
    const searchQuery = this.intentService.generateSearchQuery(message, intentResult);
    const knowledgeResult = await this.knowledgeService.searchKnowledge(searchQuery);
    
    // 构建提示词
    const prompt = await this.buildBusinessPrompt(message, knowledgeResult, session, intentResult);
    console.log('提示词:',prompt);
    // 调用 ARK API
    const response = await this.callARKAPI(prompt);
    
    return {
      text: response,
      usedKnowledge: knowledgeResult.found,
      knowledgeCount: knowledgeResult.count
    };
  }

  // 处理可能的业务咨询
  async handlePossibleBusinessInquiry(message, intentResult, session) {
    console.log('🤔 处理可能的业务咨询...');
    
    // 尝试搜索知识图谱
    const searchQuery = this.intentService.generateSearchQuery(message, intentResult);
    const knowledgeResult = await this.knowledgeService.searchKnowledge(searchQuery);
    
    if (knowledgeResult.found) {
      // 找到相关知识，按业务咨询处理
      return await this.handleBusinessInquiry(message, intentResult, session);
    } else {
      // 没找到相关知识，提供通用回复并引导
      const prompt = this.buildClarificationPrompt(message, session);
      const response = await this.callARKAPI(prompt);
      
      return {
        text: response,
        usedKnowledge: false
      };
    }
  }

  // 处理问候
  async handleGreeting(message, session) {
    console.log('👋 处理问候...');
    
    const greetingPrompt = `你是畅游网络的专业客服。用户向你问候："${message}"。

请给出友好、专业的问候回复，并简要介绍畅游网络的随身路由器业务。回复要简洁、热情。

用户信息：
- 姓名：${session.name}
- 消息次数：${session.messageCount + 1}`;

    const response = await this.callARKAPI(greetingPrompt);
    
    return {
      text: response,
      usedKnowledge: false
    };
  }

  // 处理闲聊
  async handleChitChat(message, session) {
    console.log('💭 处理闲聊...');
    
    const chitChatPrompt = `你是畅游网络的专业客服。用户说："${message}"。

这似乎不是业务相关的问题。请礼貌地回应，然后引导用户了解我们的随身路由器产品和服务。保持友好但专业。`;

    const response = await this.callARKAPI(chitChatPrompt);
    
    return {
      text: response,
      usedKnowledge: false
    };
  }

  // 处理一般咨询
  async handleGeneralInquiry(message, session) {
    console.log('❓ 处理一般咨询...');
    
    // 尝试搜索知识图谱
    const knowledgeResult = await this.knowledgeService.searchKnowledge(message);
    
    const prompt = knowledgeResult.found
      ? await this.buildBusinessPrompt(message, knowledgeResult, session)
      : this.buildGeneralPrompt(message, session);
    
    const response = await this.callARKAPI(prompt);
    
    return {
      text: response,
      usedKnowledge: knowledgeResult.found
    };
  }

  // 构建业务提示词
  async buildBusinessPrompt(message, knowledgeResult, session, intentResult = null) {
    // 获取用户偏好档案
    const userProfile = await this.getUserProfile(session.userId);
    const basePrompt = `你是畅游网络的专业客服代表，专门为客户提供随身路由器产品和服务咨询。

## 你的身份和职责：
- 畅游网络随身路由器业务的专业客服
- 为客户提供专业、友好、准确的咨询服务
- 根据客户需求推荐最适合的产品和套餐

## 产品型号与定价策略：
**畅游 Mini (¥299):**
- 核心特点：极致便携，适合个人短期出行
- 电池续航：8-10小时
- 最多连接设备：5台
- 网络技术：4G LTE
- 目标用户：个人旅行者、背包客（1-2人使用）

**畅游 Pro (¥599):**
- 核心特点：平衡性能与续航，适合商务差旅
- 电池续航：15-18小时
- 最多连接设备：10台
- 网络技术：5G/4G LTE智能切换
- 目标用户：商务人士、留学生（3-6人使用）

**畅游 Max (¥999):**
- 核心特点：旗舰性能，多设备支持，适合家庭或团队出游
- 电池续航：20-24小时
- 最多连接设备：16台
- 网络技术：全球主流5G/4G LTE频段
- 目标用户：家庭出游、小型商务团队（7人以上使用）

## 流量套餐定价策略：
**按量计费套餐（适合低频用户）：**
- ¥80/10GB（有效期30天）
- ¥150/25GB（有效期60天）

**月度套餐（适合常规用户）：**
- ¥128/50GB
- ¥198/100GB
- ¥288/200GB（适合重度用户或多设备分享）

**年度套餐（性价比之选）：**
- ¥1299/600GB（相当于每月108元/50GB）
- ¥1999/1200GB（相当于每月166元/100GB）

**地区性特惠套餐：**
- 东南亚畅游包：¥99/30GB（15天有效）
- 欧美商务通：¥188/50GB（30天有效）

**促销策略：**
- 首次购机优惠：购买任意型号设备，即可获赠价值¥80的10GB基础流量包
- 套餐叠加优惠：一次性购买多个相同套餐，可享受9折优惠
- 老用户续费折扣：老用户续费年度套餐，可享受85折优惠

## 售前政策：
1. 免费咨询服务：在线客服、微信公众号、电话热线提供一对一专业咨询
2. 需求评估：主动询问出行目的地、使用人数、设备数量及上网习惯
3. 透明资费说明：明确告知所有费用，无隐藏消费
4. 7天无理由试用：设备完好情况下可申请无理由退货
5. 发货与配送：付款后24小时内发货，提供物流追踪

## 售后政策：
1. 一年硬件保修：非人为损坏免费维修或更换
2. 终身技术支持：7x12小时在线技术支持
3. 流量管理与提醒：实时查询使用情况，80%和100%时自动提醒
4. 套餐续订与升级：随时续订升级，新套餐即时生效
5. 设备丢失或损坏：可暂停服务，提供成本价维修
6. 用户反馈渠道：专门团队跟进处理用户建议

## 推荐原则：
- 根据人数选择：1-2人选Mini，3-6人选Pro，7人以上选Max
- 商务出差场景优先推荐Pro（商务功能强，续航适中）
- 考虑使用场景：个人旅行选Mini，商务差旅选Pro，家庭团队选Max
- 流量套餐根据使用频率和时长推荐：低频选按量计费，常规选月度，长期选年度

## 个性化推荐指导：
- 根据用户偏好档案中的信息进行个性化推荐
- 如果用户有明确的使用场景偏好，优先推荐匹配的产品
- 如果用户有地区偏好，推荐相应的地区特惠套餐
- 如果用户有价格敏感度信息，调整推荐策略（预算导向推荐Mini+基础套餐，高端导向推荐Max+年度套餐）
- 如果用户有技术偏好，在介绍产品时重点突出相关特性
- 参考用户的关注点，在回复中重点解答相关问题

## 相关知识信息：
${knowledgeResult.found ? knowledgeResult.knowledge : '暂无特定相关知识，请基于基础产品知识回答。'}

## 用户信息：
- 姓名：${session.name}
- 消息次数：${session.messageCount + 1}

## 用户偏好档案：
${this.formatUserProfile(userProfile)}

## 用户问题：
${message}

请基于以上信息，作为畅游网络的专业客服，为用户提供准确、友好的回复。

## 重要回复原则：
1. **绝对诚实原则**：
   - 如果知识信息中没有相关内容，必须明确说"我不确定"、"我不知道"或"这个信息我这边没有"
   - 绝对不要说"可能"、"应该"、"一般来说"等模糊表述
   - 不要基于常识或推测给出任何肯定或否定的答案

2. **严禁编造信息**：
   - 对于技术规格、兼容性、具体参数等问题，如果知识库中没有，就直接承认不知道
   - 不要使用"通常"、"大部分"、"一般情况下"等词语来掩盖不确定性
   - 宁可承认不知道，也不要给出可能误导用户的信息

3. **明确表达不确定性**：
   - 使用明确的表述："很抱歉，这个具体信息我不清楚"
   - 避免模糊表述："目前我这边暂时没有详细了解"可能被理解为将来会有
   - 直接说："我无法确认这个信息"

4. **提供建议和替代方案**：
   - 建议查看产品包装、说明书或设备标识
   - 提供技术支持联系方式
   - 建议咨询专业技术人员

5. **保持专业和友好**：
   - 承认不知道时要保持专业态度
   - 表达歉意但不要过度道歉
   - 积极提供获取准确信息的途径

## 重要格式要求：
- 回复将通过WhatsApp发送，请使用纯文本格式
- 不要使用Markdown语法（如**粗体**、*斜体*、#标题等）
- 不要使用特殊符号来强调（如***、###、---等）
- 可以使用表情符号增加亲和力
- 产品名称和价格用普通文字表达
- 回复要专业但易懂，语调友好自然`;

    return basePrompt;
  }

  // 构建澄清提示词
  buildClarificationPrompt(message, session) {
    return `你是畅游网络的专业客服。用户说："${message}"。

这个问题可能与我们的随身路由器业务相关，但不太确定。请礼貌地询问用户的具体需求，并介绍我们的主要产品和服务，帮助用户找到他们需要的信息。

重要格式要求：
- 回复将通过WhatsApp发送，请使用纯文本格式
- 不要使用Markdown语法或特殊符号强调
- 可以使用表情符号增加亲和力
- 保持友好和专业的语调`;
  }

  // 构建通用提示词
  buildGeneralPrompt(message, session) {
    return `你是畅游网络的专业客服。用户问："${message}"。

请尽力回答用户的问题，如果不确定答案，请诚实说明，并引导用户联系我们获取更多帮助。

重要格式要求：
- 回复将通过WhatsApp发送，请使用纯文本格式
- 不要使用Markdown语法或特殊符号强调
- 可以使用表情符号增加亲和力
- 保持专业和友好的语调`;
  }

  // 调用 ARK API
  async callARKAPI(prompt) {
    try {
      const response = await this.openaiClient.chat.completions.create({
        model: process.env.VOLCENGINE_MODEL_ENDPOINT || 'doubao-seed-1-6-flash-250615',
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.7,
        max_tokens: 1000
      });

      return response.choices[0].message.content;
    } catch (error) {
      console.error('❌ ARK API 调用失败:', error.message);
      throw new Error('AI服务暂时不可用，请稍后再试。');
    }
  }

  // 获取或创建用户会话
  async getOrCreateUserSession(userId, userName) {
    if (!this.userSessions[userId]) {
      const sessionId = `session_${userId}_${Date.now()}`;
      
      this.userSessions[userId] = {
        userId: userId,
        sessionId: sessionId,
        name: userName || 'User',
        lastActivity: new Date(),
        messageCount: 0,
        created: new Date()
      };
      
      // 为用户创建Zep会话
      try {
        await this.zepClient.memory.addSession({
          sessionId: sessionId,
          userId: userId,
          metadata: {
            source: 'whatsapp-changyou',
            userName: userName || 'User',
            created: new Date().toISOString()
          }
        });
      } catch (sessionError) {
        console.log('⚠️ Zep会话创建失败，继续处理:', sessionError.message);
      }
    }
    
    return this.userSessions[userId];
  }

  // 保存对话到记忆
  async saveConversationToMemory(session, userMessage, aiResponse) {
    try {
      // 分析用户消息中的特征偏好
      const userPreferences = await this.extractUserPreferences(userMessage, session);

      // 保存对话消息
      await this.zepClient.memory.add(session.sessionId, {
        messages: [
          { roleType: 'user', content: userMessage },
          { roleType: 'assistant', content: aiResponse }
        ],
        metadata: {
          timestamp: new Date().toISOString(),
          preferences: userPreferences
        }
      });

      // 如果发现新的用户偏好，更新用户档案
      if (userPreferences && Object.keys(userPreferences).length > 0) {
        await this.updateUserProfile(session, userPreferences);
      }

    } catch (memoryError) {
      console.log('⚠️ 记忆保存失败:', memoryError.message);
    }
  }

  // 提取用户偏好特征
  async extractUserPreferences(message, session) {
    try {
      const extractionPrompt = `你是一个专业的用户偏好分析师。请分析用户消息，提取其中的偏好信息。

用户消息："${message}"

请仔细分析消息中的关键信息，提取以下偏好（如果存在）：

示例分析：
- "出差去美国" → regions: ["美国"], usage_scenario: "商务出差"
- "3-4个人" → group_size: "3-6人"
- "关注性价比" → price_sensitivity: "性价比导向"
- "续航重要" → concerns: ["续航"], tech_preferences: ["长续航"]

请以JSON格式返回提取的偏好：
{
  "usage_scenario": "商务出差/个人旅行/家庭出游/学习工作",
  "group_size": "1-2人/3-6人/7人以上",
  "regions": ["具体国家或地区"],
  "duration": "短期/中期/长期",
  "price_sensitivity": "预算导向/性价比导向/高端导向",
  "tech_preferences": ["5G", "长续航", "多设备连接"],
  "concerns": ["价格", "续航", "便携性", "性能"]
}

重要：
1. 只包含能从消息中明确推断的偏好
2. 如果某个偏好不存在，不要包含该字段
3. 如果完全没有偏好信息，返回 {}
4. 必须返回有效的JSON格式`;

      const response = await this.openaiClient.chat.completions.create({
        model: process.env.VOLCENGINE_MODEL_ENDPOINT || 'doubao-seed-1-6-flash-250615',
        messages: [{ role: 'user', content: extractionPrompt }],
        temperature: 0.3,
        max_tokens: 300
      });

      const aiResponse = response.choices[0].message.content;
      console.log(`🤖 偏好提取LLM原始回复: ${aiResponse}`);

      // 尝试解析JSON
      try {
        const jsonMatch = aiResponse.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          console.log(`📝 匹配到的JSON: ${jsonMatch[0]}`);
          const preferences = JSON.parse(jsonMatch[0]);
          console.log(`🧠 提取用户偏好:`, preferences);
          return preferences;
        } else {
          console.log('⚠️ 未找到JSON格式的回复');
        }
      } catch (parseError) {
        console.log('⚠️ 偏好解析失败:', parseError.message);
      }

      return {};
    } catch (error) {
      console.log('⚠️ 用户偏好提取失败:', error.message);
      return {};
    }
  }

  // 更新用户档案
  async updateUserProfile(session, newPreferences) {
    try {
      // 获取现有用户档案
      const existingProfile = await this.getUserProfile(session.userId);

      // 合并偏好信息
      const updatedProfile = this.mergePreferences(existingProfile, newPreferences);

      // 尝试更新现有用户档案
      try {
        await this.zepClient.user.update(session.userId, {
          email: `${session.userId}@whatsapp.user`,
          firstName: session.name,
          metadata: {
            preferences: updatedProfile,
            lastUpdated: new Date().toISOString(),
            source: 'whatsapp-changyou'
          }
        });
        console.log(`👤 用户档案已更新:`, updatedProfile);
      } catch (updateError) {
        // 如果更新失败，尝试创建新用户
        if (updateError.message.includes('not found')) {
          await this.zepClient.user.add({
            userId: session.userId,
            email: `${session.userId}@whatsapp.user`,
            firstName: session.name,
            metadata: {
              preferences: updatedProfile,
              lastUpdated: new Date().toISOString(),
              source: 'whatsapp-changyou'
            }
          });
          console.log(`👤 新用户档案已创建:`, updatedProfile);
        } else {
          throw updateError;
        }
      }

    } catch (error) {
      console.log('⚠️ 用户档案更新失败:', error.message);
    }
  }

  // 获取用户档案
  async getUserProfile(userId) {
    try {
      const user = await this.zepClient.user.get(userId);
      return user.metadata?.preferences || {};
    } catch (error) {
      console.log(`⚠️ 获取用户档案失败: ${error.message}`);
      return {};
    }
  }

  // 合并偏好信息
  mergePreferences(existing, newPrefs) {
    const merged = { ...existing };

    // 合并数组类型的偏好（如地区、技术偏好）
    ['regions', 'tech_preferences', 'concerns'].forEach(key => {
      if (newPrefs[key] && Array.isArray(newPrefs[key])) {
        merged[key] = [...(merged[key] || []), ...newPrefs[key]];
        // 去重
        merged[key] = [...new Set(merged[key])];
      }
    });

    // 更新单值偏好（保留最新的）
    ['usage_scenario', 'group_size', 'duration', 'price_sensitivity'].forEach(key => {
      if (newPrefs[key]) {
        merged[key] = newPrefs[key];
      }
    });

    return merged;
  }

  // 格式化用户档案为提示词
  formatUserProfile(profile) {
    if (!profile || Object.keys(profile).length === 0) {
      return '暂无用户偏好记录，这是新用户或首次咨询。';
    }

    const sections = [];

    if (profile.usage_scenario) {
      sections.push(`- 使用场景偏好：${profile.usage_scenario}`);
    }

    if (profile.group_size) {
      sections.push(`- 人数偏好：${profile.group_size}`);
    }

    if (profile.regions && profile.regions.length > 0) {
      sections.push(`- 常去地区：${profile.regions.join('、')}`);
    }

    if (profile.duration) {
      sections.push(`- 使用时长偏好：${profile.duration}`);
    }

    if (profile.price_sensitivity) {
      sections.push(`- 价格敏感度：${profile.price_sensitivity}`);
    }

    if (profile.tech_preferences && profile.tech_preferences.length > 0) {
      sections.push(`- 技术偏好：${profile.tech_preferences.join('、')}`);
    }

    if (profile.concerns && profile.concerns.length > 0) {
      sections.push(`- 关注点：${profile.concerns.join('、')}`);
    }

    return sections.length > 0 ? sections.join('\n') : '暂无详细偏好记录。';
  }

  // 更新会话信息
  updateSessionInfo(session) {
    session.lastActivity = new Date();
    session.messageCount += 1;
  }

  // 获取用户会话信息
  getUserSessions() {
    return {
      total: Object.keys(this.userSessions).length,
      sessions: this.userSessions,
      timestamp: new Date().toISOString()
    };
  }

  // 清除用户会话
  clearUserSession(userId) {
    if (this.userSessions[userId]) {
      delete this.userSessions[userId];
      return true;
    }
    return false;
  }
}

module.exports = MessageProcessingService;
