// 简化版Coze API客户端
const axios = require('axios');

class SimpleCozeClient {
  constructor() {
    this.botId = '7528309468237529127';
    this.accessToken = 'pat_FSEGBGcfbYwabmxELRPZYAReTrVaWIMMPBwlyIfUeXnqaJsBTcbbIZrpNyEAZwLR';
    this.baseUrl = 'https://api.coze.cn';
    this.conversations = {};
  }

  // 创建请求头
  getHeaders() {
    return {
      'Authorization': `Bearer ${this.accessToken}`,
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    };
  }

  // 发送消息（简化版本）
  async sendMessage(message, userId) {
    try {
      console.log(`🤖 发送消息到Coze (用户: ${userId}):`);
      console.log(`   消息: ${message}`);

      // 使用简化的请求格式
      const url = `${this.baseUrl}/v3/chat`;
      const payload = {
        bot_id: this.botId,
        user_id: userId,
        query: message,
        stream: false
      };

      console.log('📤 发送请求:', JSON.stringify(payload, null, 2));

      const response = await axios.post(url, payload, {
        headers: this.getHeaders(),
        timeout: 30000
      });

      console.log('📥 收到响应:', JSON.stringify(response.data, null, 2));

      // 如果是异步响应，等待结果
      if (response.data.data && response.data.data.status === 'in_progress') {
        const chatId = response.data.data.id;
        const conversationId = response.data.data.conversation_id;
        
        console.log(`⏳ 等待处理完成 (Chat ID: ${chatId})...`);
        
        // 简化的轮询
        const result = await this.waitForResult(chatId, conversationId);
        return result;
      }

      // 直接返回结果
      return this.extractReply(response.data);

    } catch (error) {
      console.error('❌ Coze API调用失败:', error.message);
      if (error.response) {
        console.error('   状态码:', error.response.status);
        console.error('   响应数据:', JSON.stringify(error.response.data, null, 2));
      }
      
      // 返回默认回复
      return {
        text: `很抱歉，我现在无法处理您的消息。请稍后再试。(错误: ${error.message})`,
        conversation_id: null,
        success: false
      };
    }
  }

  // 等待异步结果
  async waitForResult(chatId, conversationId, maxAttempts = 10) {
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        console.log(`   检查结果 (第${attempt}次)...`);
        
        const url = `${this.baseUrl}/v3/chat/retrieve?chat_id=${chatId}&conversation_id=${conversationId}`;
        
        const response = await axios.get(url, {
          headers: this.getHeaders(),
          timeout: 10000
        });

        console.log(`📊 状态检查 (第${attempt}次):`, response.data.data?.status);

        if (response.data.data?.status === 'completed') {
          console.log('✅ 处理完成');
          return this.extractReply(response.data, conversationId);
        }
        
        if (response.data.data?.status === 'failed') {
          console.log('❌ 处理失败');
          return {
            text: '很抱歉，处理您的消息时出现了问题。',
            conversation_id: conversationId,
            success: false
          };
        }
        
        // 等待2秒后继续
        if (attempt < maxAttempts) {
          await new Promise(resolve => setTimeout(resolve, 2000));
        }
        
      } catch (error) {
        console.log(`   第${attempt}次检查失败:`, error.message);
        if (attempt < maxAttempts) {
          await new Promise(resolve => setTimeout(resolve, 2000));
        }
      }
    }
    
    // 超时返回默认回复
    return {
      text: '处理时间较长，请稍后再试。',
      conversation_id: conversationId,
      success: false
    };
  }

  // 提取回复内容
  extractReply(responseData, conversationId = null) {
    let replyText = '';
    
    try {
      // 尝试从不同的响应结构中提取回复
      if (responseData.data?.messages && responseData.data.messages.length > 0) {
        // 查找助手的回复
        const assistantMessage = responseData.data.messages
          .reverse()
          .find(msg => msg.role === 'assistant' && msg.content);
        
        if (assistantMessage) {
          replyText = assistantMessage.content;
        }
      }
      
      // 如果没有找到回复，使用默认消息
      if (!replyText) {
        replyText = '我收到了您的消息，但暂时无法生成回复。';
      }
      
      console.log(`✅ 提取回复: ${replyText.substring(0, 50)}...`);
      
      return {
        text: replyText,
        conversation_id: conversationId || responseData.data?.conversation_id,
        success: true
      };
      
    } catch (error) {
      console.error('❌ 提取回复失败:', error.message);
      return {
        text: '很抱歉，我现在无法正确处理您的消息。',
        conversation_id: conversationId,
        success: false
      };
    }
  }

  // 测试连接
  async testConnection() {
    console.log('🔄 测试Coze API连接...');
    
    try {
      const result = await this.sendMessage('Hello, this is a test message.', 'test_user');
      
      if (result.success) {
        console.log('✅ Coze API连接成功!');
        console.log(`   测试回复: ${result.text}`);
        return { success: true, reply: result.text };
      } else {
        console.log('⚠️ Coze API连接有问题，但服务可用');
        return { success: false, error: 'API响应异常' };
      }
      
    } catch (error) {
      console.error('❌ Coze API连接失败:', error.message);
      return { success: false, error: error.message };
    }
  }

  // 生成智能回复（主要方法）
  async generateReply(userMessage, userId) {
    console.log(`💬 为用户 ${userId} 生成回复...`);
    console.log(`   用户消息: ${userMessage}`);
    
    try {
      const result = await this.sendMessage(userMessage, userId);
      
      // 保存会话ID
      if (result.conversation_id) {
        this.conversations[userId] = result.conversation_id;
      }
      
      return result.text;
      
    } catch (error) {
      console.error('❌ 生成回复失败:', error.message);
      
      // 返回友好的错误消息
      const errorReplies = [
        '很抱歉，我现在有点忙，请稍后再试。',
        '抱歉，我需要一点时间来思考您的问题。',
        '系统暂时繁忙，请您稍等片刻。',
        '很抱歉，我现在无法正确理解您的消息。'
      ];
      
      const randomReply = errorReplies[Math.floor(Math.random() * errorReplies.length)];
      return randomReply;
    }
  }
}

// 创建全局实例
const cozeClient = new SimpleCozeClient();

module.exports = cozeClient;
