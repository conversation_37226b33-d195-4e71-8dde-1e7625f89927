import React, { useState, useEffect } from 'react';
import { useSearchParams, Link } from 'react-router-dom';
import { 
  Search, 
  Phone, 
  Hash, 
  MessageCircle, 
  MessageSquare,
  Filter,
  Clock,
  User,
  Bot,
  ExternalLink
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { Input } from './ui/input';
import { apiService } from '../services/api';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';

export function SearchResults() {
  const [searchParams, setSearchParams] = useSearchParams();
  const [results, setResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [searchQuery, setSearchQuery] = useState(searchParams.get('q') || '');
  const [filters, setFilters] = useState({
    platform: searchParams.get('platform') || '',
    startDate: searchParams.get('startDate') || '',
    endDate: searchParams.get('endDate') || ''
  });

  useEffect(() => {
    const query = searchParams.get('q');
    if (query) {
      setSearchQuery(query);
      performSearch(query);
    }
  }, [searchParams]);

  const performSearch = async (query = searchQuery) => {
    if (!query || query.trim().length < 2) {
      setError('搜索关键词至少需要2个字符');
      return;
    }

    try {
      setLoading(true);
      setError(null);
      const response = await apiService.searchMessages(query.trim(), {
        ...filters,
        limit: 100
      });
      setResults(response.data || []);
    } catch (err) {
      setError(err.message);
      console.error('搜索消息失败:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (e) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      const newParams = new URLSearchParams();
      newParams.set('q', searchQuery.trim());
      if (filters.platform) newParams.set('platform', filters.platform);
      if (filters.startDate) newParams.set('startDate', filters.startDate);
      if (filters.endDate) newParams.set('endDate', filters.endDate);
      setSearchParams(newParams);
    }
  };

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const getPlatformIcon = (platform) => {
    switch (platform) {
      case 'whatsapp':
        return <Phone className="h-4 w-4" />;
      case 'slack':
        return <Hash className="h-4 w-4" />;
      case 'line':
        return <MessageCircle className="h-4 w-4" />;
      default:
        return <MessageSquare className="h-4 w-4" />;
    }
  };

  const getPlatformColor = (platform) => {
    switch (platform) {
      case 'whatsapp':
        return 'bg-green-100 text-green-800';
      case 'slack':
        return 'bg-purple-100 text-purple-800';
      case 'line':
        return 'bg-green-50 text-green-700';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatMessageTime = (timestamp) => {
    if (!timestamp) return '';
    try {
      return format(new Date(timestamp), 'yyyy-MM-dd HH:mm', { locale: zhCN });
    } catch {
      return '时间格式错误';
    }
  };

  const isIncomingMessage = (message) => {
    return message.messageType === 'incoming';
  };

  const highlightSearchTerm = (text, searchTerm) => {
    if (!searchTerm || !text) return text;
    
    const regex = new RegExp(`(${searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
    const parts = text.split(regex);
    
    return parts.map((part, index) => 
      regex.test(part) ? (
        <mark key={index} className="bg-yellow-200 px-1 rounded">
          {part}
        </mark>
      ) : part
    );
  };

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center space-x-3">
        <Search className="h-8 w-8 text-gray-600" />
        <h1 className="text-3xl font-bold text-gray-900">消息搜索</h1>
      </div>

      {/* 搜索表单 */}
      <Card>
        <CardHeader>
          <CardTitle>搜索条件</CardTitle>
          <CardDescription>在所有平台的消息中搜索关键词</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <form onSubmit={handleSearch} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                搜索关键词
              </label>
              <div className="flex space-x-2">
                <Input
                  type="text"
                  placeholder="输入搜索关键词..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="flex-1"
                />
                <Button type="submit" disabled={loading}>
                  <Search className="h-4 w-4 mr-2" />
                  {loading ? '搜索中...' : '搜索'}
                </Button>
              </div>
            </div>

            {/* 过滤器 */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  平台筛选
                </label>
                <select
                  value={filters.platform}
                  onChange={(e) => handleFilterChange('platform', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">全部平台</option>
                  <option value="whatsapp">WhatsApp</option>
                  <option value="slack">Slack</option>
                  <option value="line">LINE</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  开始日期
                </label>
                <Input
                  type="date"
                  value={filters.startDate}
                  onChange={(e) => handleFilterChange('startDate', e.target.value)}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  结束日期
                </label>
                <Input
                  type="date"
                  value={filters.endDate}
                  onChange={(e) => handleFilterChange('endDate', e.target.value)}
                />
              </div>
            </div>
          </form>
        </CardContent>
      </Card>

      {/* 搜索结果 */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="text-red-600">
              搜索失败: {error}
            </div>
          </CardContent>
        </Card>
      )}

      {searchParams.get('q') && (
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">
              搜索结果: "{searchParams.get('q')}"
            </h2>
            <p className="text-sm text-gray-500">
              找到 {results.length} 条相关消息
            </p>
          </div>
        </div>
      )}

      {/* 结果列表 */}
      <div className="space-y-4">
        {loading ? (
          [...Array(3)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 bg-gray-200 rounded"></div>
                    <div className="h-4 bg-gray-200 rounded w-24"></div>
                    <div className="h-4 bg-gray-200 rounded w-32"></div>
                  </div>
                  <div className="h-16 bg-gray-200 rounded"></div>
                </div>
              </CardContent>
            </Card>
          ))
        ) : results.length === 0 && searchParams.get('q') ? (
          <Card>
            <CardContent className="pt-6">
              <div className="text-center text-gray-500 py-8">
                <Search className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p className="text-lg font-medium">未找到相关消息</p>
                <p className="text-sm">尝试使用不同的关键词或调整搜索条件</p>
              </div>
            </CardContent>
          </Card>
        ) : (
          results.map((message) => {
            const isIncoming = isIncomingMessage(message);
            
            return (
              <Card key={message.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-6">
                  <div className="space-y-3">
                    {/* 消息头部信息 */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        {isIncoming ? (
                          <User className="h-4 w-4 text-gray-600" />
                        ) : (
                          <Bot className="h-4 w-4 text-blue-600" />
                        )}
                        <Badge variant="outline" className="capitalize">
                          {getPlatformIcon(message.platform)}
                          <span className="ml-1">{message.platform}</span>
                        </Badge>
                        <Badge variant={isIncoming ? "secondary" : "default"}>
                          {isIncoming ? '接收' : '发送'}
                        </Badge>
                        <span className="text-sm text-gray-600">
                          {message.user?.displayName || message.user?.platformUserId || '未知用户'}
                        </span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className="flex items-center text-sm text-gray-500">
                          <Clock className="h-4 w-4 mr-1" />
                          {formatMessageTime(message.timestamp)}
                        </div>
                        <Link 
                          to={`/conversations/${message.conversationId}`}
                          className="text-blue-600 hover:text-blue-800"
                        >
                          <ExternalLink className="h-4 w-4" />
                        </Link>
                      </div>
                    </div>

                    {/* 消息内容 */}
                    <div className="bg-gray-50 rounded-lg p-4">
                      <p className="text-gray-900 whitespace-pre-wrap">
                        {highlightSearchTerm(message.content, searchParams.get('q'))}
                      </p>
                    </div>

                    {/* 消息元数据 */}
                    {message.metadata && Object.keys(message.metadata).length > 0 && (
                      <div className="text-xs text-gray-500 space-y-1">
                        {message.messageSid && (
                          <div>消息ID: {message.messageSid}</div>
                        )}
                        {message.metadata.intent && (
                          <div>意图: {message.metadata.intent}</div>
                        )}
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            );
          })
        )}
      </div>
    </div>
  );
}
