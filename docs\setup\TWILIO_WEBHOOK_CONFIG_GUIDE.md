# Twilio WhatsApp沙盒Webhook配置详细指南

## 🎯 配置目标

在Twilio控制台配置两个重要的Webhook URL：
1. **When a message comes in** - 处理用户发送的消息
2. **Status callback URL** - 接收消息状态更新

## 📍 配置页面位置

### 方法1：直接链接
```
https://console.twilio.com/us1/develop/sms/try-it-out/whatsapp-learn
```

### 方法2：导航路径
```
Twilio控制台 → Messaging → Try it out → WhatsApp sandbox
```

## 🔧 配置步骤详解

### 步骤1：准备本地环境

```bash
# 1. 启动Webhook接收服务器
node webhook-receiver.js
# 输出：🚀 WhatsApp Webhook接收服务器启动成功!
#       📡 监听端口: 3002

# 2. 启动ngrok隧道（新终端）
ngrok http 3002
# 输出：Forwarding https://abc123.ngrok.io -> http://localhost:3002
```

### 步骤2：复制ngrok URL

从ngrok输出中复制HTTPS URL：
```
✅ 正确格式：https://abc123.ngrok.io
❌ 错误格式：http://abc123.ngrok.io  (不是HTTPS)
❌ 错误格式：abc123.ngrok.io       (缺少协议)
```

### 步骤3：在Twilio控制台配置

#### 🔍 找到配置区域
在沙盒页面找到 **"Sandbox Configuration"** 部分，通常在页面中下部。

#### 📝 填写配置字段

| 字段名称 | 配置值 | 示例 |
|---------|--------|------|
| **When a message comes in** | `https://your-ngrok-url.ngrok.io/whatsapp-webhook` | `https://abc123.ngrok.io/whatsapp-webhook` |
| **HTTP Method** | `POST` | `POST` (默认) |
| **Status callback URL** | `https://your-ngrok-url.ngrok.io/whatsapp-status` | `https://abc123.ngrok.io/whatsapp-status` |
| **HTTP Method** | `POST` | `POST` (默认) |

#### 💾 保存配置
点击 **"Save Configuration"** 按钮

## 🧪 验证配置

### 1. 检查Webhook服务器状态
```bash
# 访问健康检查端点
curl http://localhost:3002/health
```

### 2. 测试ngrok连接
```bash
# 测试ngrok隧道
curl https://your-ngrok-url.ngrok.io/health
```

### 3. 发送测试消息
从您的WhatsApp发送消息到：`+1 ************`

## 📊 预期结果

### 控制台输出示例

**接收消息时：**
```
📱 收到WhatsApp消息!
==================================================
📋 消息详情:
   消息ID: SM1234567890abcdef
   发送方: whatsapp:+8613800138000
   接收方: whatsapp:+***********
   内容: hello
   发送者姓名: Your Name
   接收时间: 2024-01-20 15:30:25

🤖 准备发送自动回复: 👋 你好！欢迎使用Twilio WhatsApp Business API！
✅ 自动回复发送成功: SM0987654321fedcba
```

**状态更新时：**
```
📊 收到消息状态更新!
==================================================
📋 状态更新详情:
   消息ID: SM0987654321fedcba
   状态: delivered
   发送方: whatsapp:+***********
   接收方: whatsapp:+8613800138000
   更新时间: 2024-01-20 15:30:27
```

## 🔧 常见配置问题

### 问题1：URL格式错误
```
❌ 错误：http://abc123.ngrok.io/whatsapp-webhook
✅ 正确：https://abc123.ngrok.io/whatsapp-webhook
```
**解决方案：** 必须使用HTTPS协议

### 问题2：端点路径错误
```
❌ 错误：https://abc123.ngrok.io/webhook
✅ 正确：https://abc123.ngrok.io/whatsapp-webhook
```
**解决方案：** 确保路径与代码中定义的一致

### 问题3：ngrok隧道过期
```
❌ 错误：tunnel not found
```
**解决方案：** 重启ngrok并更新Twilio配置

### 问题4：端口冲突
```
❌ 错误：Port 3002 already in use
```
**解决方案：** 
```bash
# 查找占用进程
netstat -ano | findstr :3002
# 或使用不同端口
WEBHOOK_PORT=3003 node webhook-receiver.js
```

## 🛠️ 高级配置

### 1. 使用自定义域名
如果您有自己的域名和服务器：
```
When a message comes in: https://yourdomain.com/whatsapp-webhook
Status callback URL: https://yourdomain.com/whatsapp-status
```

### 2. 添加认证参数
```
https://abc123.ngrok.io/whatsapp-webhook?auth=your-secret-key
```

### 3. 使用不同端点
您可以自定义端点路径，只需确保代码中的路由匹配：
```javascript
app.post('/custom-webhook-path', (req, res) => {
    // 处理逻辑
});
```

## 📋 配置检查清单

- [ ] ✅ Webhook服务器已启动 (`node webhook-receiver.js`)
- [ ] ✅ ngrok隧道已建立 (`ngrok http 3002`)
- [ ] ✅ 复制了正确的HTTPS URL
- [ ] ✅ 在Twilio控制台填入了两个URL
- [ ] ✅ 点击了"Save Configuration"
- [ ] ✅ 从WhatsApp发送了测试消息
- [ ] ✅ 控制台显示了消息接收日志
- [ ] ✅ 收到了自动回复消息

## 🎉 配置成功标志

当配置正确时，您应该看到：
1. ✅ Webhook服务器接收到消息
2. ✅ 控制台显示详细的消息信息
3. ✅ 自动回复消息发送成功
4. ✅ 在WhatsApp中收到回复
5. ✅ 状态更新被正确接收

---

**需要帮助？** 如果遇到问题，请检查控制台错误信息或运行 `node setup-webhook.js check` 进行诊断。
