const jwt = require('jsonwebtoken');
const logger = require('../config/logger');
const config = require('../config');

/**
 * API Key认证中间件
 */
const apiKeyAuth = (req, res, next) => {
  try {
    const apiKey = req.header('X-API-Key') || req.query.apiKey;
    
    if (!apiKey) {
      return res.status(401).json({
        success: false,
        error: 'API key is required',
      });
    }

    if (apiKey !== config.api.key) {
      logger.warn('Invalid API key attempt', { 
        providedKey: apiKey.substring(0, 8) + '...',
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      });
      
      return res.status(401).json({
        success: false,
        error: 'Invalid API key',
      });
    }

    // API key验证成功
    req.auth = {
      type: 'api-key',
      authenticated: true,
    };

    next();
  } catch (error) {
    logger.error('API key authentication error:', error);
    res.status(500).json({
      success: false,
      error: 'Authentication error',
    });
  }
};

/**
 * JWT认证中间件
 */
const jwtAuth = (req, res, next) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({
        success: false,
        error: 'Access token is required',
      });
    }

    const decoded = jwt.verify(token, config.jwt.secret);
    
    req.auth = {
      type: 'jwt',
      authenticated: true,
      user: decoded,
    };

    next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        error: 'Invalid token',
      });
    }
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        error: 'Token expired',
      });
    }

    logger.error('JWT authentication error:', error);
    res.status(500).json({
      success: false,
      error: 'Authentication error',
    });
  }
};

/**
 * 灵活认证中间件 - 支持API Key或JWT
 */
const flexibleAuth = (req, res, next) => {
  const apiKey = req.header('X-API-Key') || req.query.apiKey;
  const token = req.header('Authorization')?.replace('Bearer ', '');

  if (apiKey) {
    return apiKeyAuth(req, res, next);
  } else if (token) {
    return jwtAuth(req, res, next);
  } else {
    return res.status(401).json({
      success: false,
      error: 'Authentication required. Provide either API key or JWT token',
    });
  }
};

/**
 * 可选认证中间件 - 如果提供了认证信息则验证，否则继续
 */
const optionalAuth = (req, res, next) => {
  const apiKey = req.header('X-API-Key') || req.query.apiKey;
  const token = req.header('Authorization')?.replace('Bearer ', '');

  if (!apiKey && !token) {
    req.auth = {
      authenticated: false,
    };
    return next();
  }

  return flexibleAuth(req, res, next);
};

module.exports = flexibleAuth;
module.exports.apiKeyAuth = apiKeyAuth;
module.exports.jwtAuth = jwtAuth;
module.exports.flexibleAuth = flexibleAuth;
module.exports.optionalAuth = optionalAuth;
