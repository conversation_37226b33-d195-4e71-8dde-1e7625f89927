const { ZepClient } = require('@getzep/zep-cloud');
const OpenAI = require('openai');
require('dotenv').config();

/**
 * Zep 知识库问答演示
 * 展示如何使用Zep导入知识库并在对话中使用知识库内容进行智能问答
 */

class ZepKnowledgeBaseDemo {
  constructor() {
    // 检查必要的环境变量
    if (!process.env.ZEP_API_KEY) {
      throw new Error('❌ 缺少 ZEP_API_KEY 环境变量');
    }
    
    if (!process.env.ARK_API_KEY) {
      throw new Error('❌ 缺少 ARK_API_KEY 环境变量');
    }

    // 初始化客户端
    this.zepClient = new ZepClient({
      apiKey: process.env.ZEP_API_KEY
    });

    this.openaiClient = new OpenAI({
      apiKey: process.env.ARK_API_KEY,
      baseURL: 'https://ark.cn-beijing.volces.com/api/v3',
    });

    // 配置
    this.userId = `kb-demo-user-${Date.now()}`;
    this.sessionId = `kb-session-${Date.now()}`;
    this.collectionName = `knowledge-base-${Date.now()}`;
    this.modelEndpoint = process.env.VOLCENGINE_MODEL_ENDPOINT || 'doubao-seed-1-6-flash-250615';
  }

  async initialize() {
    console.log('🚀 初始化Zep知识库问答演示...\n');

    // 创建用户
    console.log('👤 创建演示用户...');
    const user = await this.zepClient.user.add({
      userId: this.userId,
      email: '<EMAIL>',
      firstName: '知识库',
      lastName: '用户',
      metadata: {
        source: 'knowledge-base-demo',
        created: new Date().toISOString()
      }
    });
    console.log(`✅ 用户创建成功: ${user.firstName} ${user.lastName}\n`);

    // 创建会话
    console.log('💬 创建问答会话...');
    const session = await this.zepClient.memory.addSession({
      sessionId: this.sessionId,
      userId: this.userId,
      metadata: {
        type: 'knowledge-qa',
        collection: this.collectionName,
        created: new Date().toISOString()
      }
    });
    console.log(`✅ 会话创建成功: ${session.sessionId}\n`);
  }

  async createKnowledgeBase() {
    console.log('📚 创建并导入知识库...\n');

    // 注意：Zep的文档集合API已弃用，我们使用图谱功能来存储知识
    console.log('🕸️ 使用知识图谱存储知识库内容...');

    // 准备知识库文档
    const knowledgeDocuments = [
      {
        documentId: 'ai-basics',
        content: `# 人工智能基础知识

## 什么是人工智能
人工智能（Artificial Intelligence，AI）是指由机器展现出的智能行为，特别是计算机系统。AI的目标是创建能够执行通常需要人类智能的任务的系统。

## AI的主要分类
1. **弱人工智能（ANI）**: 专门设计用于执行特定任务的AI系统
2. **强人工智能（AGI）**: 具有与人类相当的认知能力的AI系统
3. **超人工智能（ASI）**: 在所有领域都超越人类智能的AI系统

## 机器学习
机器学习是AI的一个子领域，它使计算机能够在没有明确编程的情况下学习和改进。主要类型包括：
- 监督学习：使用标记数据进行训练
- 无监督学习：从未标记数据中发现模式
- 强化学习：通过与环境交互来学习

## 深度学习
深度学习是机器学习的一个分支，使用多层神经网络来模拟人脑的工作方式。它在图像识别、自然语言处理等领域取得了突破性进展。`,
        metadata: {
          category: 'AI基础',
          tags: ['人工智能', '机器学习', '深度学习'],
          difficulty: '入门'
        }
      },
      {
        documentId: 'programming-best-practices',
        content: `# 编程最佳实践

## 代码质量原则
1. **可读性**: 代码应该易于理解和维护
2. **简洁性**: 避免不必要的复杂性
3. **一致性**: 遵循统一的编码风格
4. **模块化**: 将代码分解为可重用的组件

## 版本控制
使用Git进行版本控制的最佳实践：
- 频繁提交，每次提交都有明确的目的
- 编写清晰的提交信息
- 使用分支进行功能开发
- 定期合并和解决冲突

## 测试策略
1. **单元测试**: 测试单个函数或方法
2. **集成测试**: 测试组件之间的交互
3. **端到端测试**: 测试完整的用户流程
4. **测试驱动开发（TDD）**: 先写测试，再写代码

## 代码审查
代码审查的好处：
- 提高代码质量
- 知识分享
- 减少bug
- 保持团队编码标准的一致性`,
        metadata: {
          category: '编程',
          tags: ['最佳实践', '代码质量', '测试', 'Git'],
          difficulty: '中级'
        }
      },
      {
        documentId: 'product-management',
        content: `# 产品管理指南

## 产品经理的核心职责
1. **市场研究**: 了解用户需求和市场趋势
2. **产品规划**: 制定产品路线图和优先级
3. **需求管理**: 收集、分析和优先排序产品需求
4. **跨团队协作**: 与设计、开发、营销等团队合作

## 用户研究方法
- **用户访谈**: 深入了解用户需求和痛点
- **问卷调查**: 收集大量用户反馈
- **A/B测试**: 比较不同方案的效果
- **用户行为分析**: 通过数据了解用户使用模式

## 产品开发流程
1. **需求分析**: 明确产品目标和用户需求
2. **原型设计**: 创建产品原型和线框图
3. **开发实现**: 与技术团队合作实现功能
4. **测试验证**: 确保产品质量和用户体验
5. **发布上线**: 产品发布和用户反馈收集
6. **迭代优化**: 基于反馈持续改进产品

## 数据驱动决策
- 设定关键指标（KPI）
- 建立数据收集和分析体系
- 基于数据而非直觉做决策
- 持续监控和优化产品表现`,
        metadata: {
          category: '产品管理',
          tags: ['产品经理', '用户研究', '数据分析', '产品开发'],
          difficulty: '中级'
        }
      },
      {
        documentId: 'nodejs-development',
        content: `# Node.js 开发指南

## Node.js 简介
Node.js是一个基于Chrome V8引擎的JavaScript运行时环境，允许在服务器端运行JavaScript代码。

## 核心特性
1. **事件驱动**: 基于事件循环的非阻塞I/O模型
2. **单线程**: 主线程是单线程，但可以创建子进程
3. **跨平台**: 支持Windows、macOS、Linux等操作系统
4. **丰富的生态系统**: NPM包管理器提供大量第三方模块

## 常用框架
- **Express.js**: 轻量级Web应用框架
- **Koa.js**: 下一代Web框架，支持async/await
- **NestJS**: 企业级应用框架，支持TypeScript
- **Fastify**: 高性能Web框架

## 最佳实践
1. **错误处理**: 使用try-catch和Promise.catch处理错误
2. **异步编程**: 使用async/await替代回调函数
3. **环境变量**: 使用dotenv管理配置
4. **日志记录**: 使用winston等日志库
5. **安全性**: 使用helmet等中间件增强安全性

## 性能优化
- 使用缓存减少数据库查询
- 实现连接池管理数据库连接
- 使用集群模式充分利用多核CPU
- 监控内存使用和垃圾回收`,
        metadata: {
          category: '编程',
          tags: ['Node.js', 'JavaScript', 'Web开发', '后端'],
          difficulty: '中级'
        }
      }
    ];

    // 导入文档到知识图谱
    console.log('📄 导入知识文档到图谱...');
    for (let i = 0; i < knowledgeDocuments.length; i++) {
      const doc = knowledgeDocuments[i];
      console.log(`   导入文档 ${i + 1}/${knowledgeDocuments.length}: ${doc.documentId}`);

      try {
        // 将文档内容添加到用户的知识图谱
        await this.zepClient.graph.add({
          userId: this.userId,
          type: 'text',
          data: `文档标题: ${doc.documentId}\n分类: ${doc.metadata.category}\n标签: ${doc.metadata.tags.join(', ')}\n\n${doc.content}`
        });
        console.log(`   ✅ ${doc.documentId} 导入成功`);
      } catch (error) {
        console.log(`   ⚠️ ${doc.documentId} 导入失败: ${error.message}`);
      }
    }

    console.log('\n✅ 知识库创建和导入完成！');
    console.log(`📊 知识库统计:`);
    console.log(`   - 文档数量: ${knowledgeDocuments.length}`);
    console.log(`   - 涵盖领域: AI基础、编程实践、产品管理、Node.js开发`);
    console.log(`   - 存储方式: 知识图谱\n`);
  }

  async searchKnowledge(query) {
    console.log(`🔍 搜索知识图谱: "${query}"`);

    try {
      const searchResults = await this.zepClient.graph.search({
        userId: this.userId,
        query: query
      });

      if (searchResults.edges && searchResults.edges.length > 0) {
        console.log(`   找到 ${searchResults.edges.length} 个相关知识片段:`);

        let knowledgeContext = '';
        searchResults.edges.slice(0, 3).forEach((edge, index) => {
          console.log(`   ${index + 1}. 事实: ${edge.fact.substring(0, 100)}...`);
          knowledgeContext += `\n相关知识 ${index + 1}:\n${edge.fact}\n`;
        });

        return knowledgeContext;
      } else {
        console.log('   未找到相关知识');
        return '';
      }
    } catch (error) {
      console.log(`   搜索出错: ${error.message}`);
      return '';
    }
  }

  async knowledgeBasedChat(userQuestion) {
    console.log(`\n👤 用户问题: ${userQuestion}`);

    // 1. 搜索相关知识
    const relevantKnowledge = await this.searchKnowledge(userQuestion);

    // 2. 添加用户问题到记忆
    const memoryResult = await this.zepClient.memory.add(this.sessionId, {
      messages: [{ roleType: 'user', content: userQuestion }],
      returnContext: true
    });

    // 3. 构建包含知识库内容的提示
    const systemPrompt = `你是一个专业的知识问答助手。请基于以下信息回答用户问题：

## 相关知识库内容：
${relevantKnowledge || '暂无直接相关的知识库内容'}

## 对话历史上下文：
${memoryResult.context || '这是对话的开始'}

## 回答要求：
1. 优先使用知识库中的准确信息
2. 如果知识库中没有相关信息，请诚实说明并提供一般性建议
3. 回答要专业、准确、有条理
4. 适当引用知识库中的具体内容
5. 保持友好和有帮助的语调`;

    // 4. 调用火山方舟大模型生成回答
    console.log('   🤖 AI助手思考中...');
    
    const completion = await this.openaiClient.chat.completions.create({
      model: this.modelEndpoint,
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: userQuestion }
      ],
      temperature: 0.3, // 较低温度确保回答准确性
      max_tokens: 1000
    });

    const aiResponse = completion.choices[0].message.content;
    console.log(`🤖 AI助手: ${aiResponse}\n`);

    // 5. 保存AI回答到记忆
    await this.zepClient.memory.add(this.sessionId, {
      messages: [{ roleType: 'assistant', content: aiResponse }]
    });

    return aiResponse;
  }

  async demonstrateKnowledgeQA() {
    console.log('💭 开始知识库问答演示...\n');

    const questions = [
      '什么是人工智能？它有哪些主要分类？',
      '在Node.js开发中有哪些最佳实践？',
      '产品经理的核心职责是什么？',
      '机器学习和深度学习有什么区别？',
      '如何进行有效的代码审查？',
      '什么是测试驱动开发？',
      '如何优化Node.js应用的性能？'
    ];

    for (let i = 0; i < questions.length; i++) {
      console.log(`🔄 第 ${i + 1} 个问题:`);
      await this.knowledgeBasedChat(questions[i]);
      
      // 短暂停顿
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }

  async showKnowledgeBaseStats() {
    console.log('📊 知识库统计信息...\n');

    try {
      // 获取会话记忆统计
      const memory = await this.zepClient.memory.get(this.sessionId);
      console.log('🧠 对话记忆统计:');
      console.log(`   - 总消息数: ${memory.messages?.length || 0}`);
      console.log(`   - 用户问题: ${memory.messages?.filter(m => m.roleType === 'user').length || 0}`);
      console.log(`   - AI回答: ${memory.messages?.filter(m => m.roleType === 'assistant').length || 0}`);
      console.log(`   - 有对话摘要: ${memory.summary ? '是' : '否'}`);

      // 测试知识图谱搜索
      console.log('\n🕸️ 知识图谱测试搜索:');
      const testQueries = ['人工智能', '编程最佳实践', '产品管理'];
      for (const query of testQueries) {
        try {
          const results = await this.zepClient.graph.search({
            userId: this.userId,
            query: query
          });
          console.log(`   - "${query}": ${results.edges?.length || 0} 个相关结果`);
        } catch (error) {
          console.log(`   - "${query}": 搜索失败`);
        }
      }

    } catch (error) {
      console.log(`获取统计信息时出错: ${error.message}`);
    }
  }

  async cleanup() {
    console.log('\n🧹 清理演示数据...');
    try {
      // 删除用户（会自动删除相关会话和知识图谱数据）
      await this.zepClient.user.delete(this.userId);
      console.log('✅ 用户、会话和知识图谱数据已删除');

      console.log('✅ 演示数据清理完成');
    } catch (error) {
      console.log(`⚠️ 清理时出错: ${error.message}`);
    }
  }

  async run() {
    try {
      await this.initialize();
      await this.createKnowledgeBase();
      await this.demonstrateKnowledgeQA();
      await this.showKnowledgeBaseStats();
      
      console.log('\n🎉 Zep知识库问答演示完成！');
      console.log('\n📝 演示总结:');
      console.log('✅ 创建并导入知识库文档');
      console.log('✅ 基于知识库内容的智能问答');
      console.log('✅ 知识检索与对话记忆结合');
      console.log('✅ 多领域知识的准确回答');
      console.log('\n💡 这个系统展示了如何构建基于知识库的智能问答助手！');
      
    } catch (error) {
      console.error('\n❌ 演示过程中出错:', error.message);
      if (error.response) {
        console.error('API响应错误:', error.response.status, error.response.data);
      }
    } finally {
      await this.cleanup();
    }
  }
}

// 运行演示
if (require.main === module) {
  const demo = new ZepKnowledgeBaseDemo();
  demo.run().catch(console.error);
}

module.exports = ZepKnowledgeBaseDemo;
