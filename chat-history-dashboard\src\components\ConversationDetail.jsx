import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import { 
  ArrowLeft, 
  Phone, 
  Hash, 
  MessageCircle, 
  MessageSquare,
  Clock,
  User,
  Bot,
  RefreshCw
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { apiService } from '../services/api';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';

export function ConversationDetail() {
  const { id } = useParams();
  const [messages, setMessages] = useState([]);
  const [conversation, setConversation] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (id) {
      loadMessages();
    }
  }, [id]);

  const loadMessages = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiService.getConversationMessages(id, { limit: 100 });
      setMessages(response.data || []);
      
      // 从第一条消息推断对话信息
      if (response.data && response.data.length > 0) {
        const firstMessage = response.data[0];
        setConversation({
          id: firstMessage.conversationId,
          platform: firstMessage.senderId?.split(':')[0] || 'unknown'
        });
      }
    } catch (err) {
      setError(err.message);
      console.error('加载对话消息失败:', err);
    } finally {
      setLoading(false);
    }
  };

  const getPlatformIcon = (platform) => {
    switch (platform) {
      case 'whatsapp':
        return <Phone className="h-4 w-4" />;
      case 'slack':
        return <Hash className="h-4 w-4" />;
      case 'line':
        return <MessageCircle className="h-4 w-4" />;
      default:
        return <MessageSquare className="h-4 w-4" />;
    }
  };

  const getPlatformColor = (platform) => {
    switch (platform) {
      case 'whatsapp':
        return 'bg-green-100 text-green-800';
      case 'slack':
        return 'bg-purple-100 text-purple-800';
      case 'line':
        return 'bg-green-50 text-green-700';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatMessageTime = (timestamp) => {
    if (!timestamp) return '';
    try {
      return format(new Date(timestamp), 'MM-dd HH:mm', { locale: zhCN });
    } catch {
      return '时间格式错误';
    }
  };

  const isIncomingMessage = (message) => {
    return message.messageType === 'incoming';
  };

  const getMessageSender = (message) => {
    if (isIncomingMessage(message)) {
      return message.sender?.name || message.senderId || '用户';
    }
    return 'Bot';
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Link to="/conversations">
            <Button variant="outline" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <h1 className="text-3xl font-bold text-gray-900">加载中...</h1>
        </div>
        <div className="space-y-4">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="animate-pulse">
              <div className="flex space-x-4 mb-4">
                <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                  <div className="h-16 bg-gray-200 rounded"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Link to="/conversations">
            <Button variant="outline" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <h1 className="text-3xl font-bold text-gray-900">对话详情</h1>
        </div>
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="text-red-600">
              加载对话消息失败: {error}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面标题和导航 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link to="/conversations">
            <Button variant="outline" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">对话详情</h1>
            {conversation && (
              <div className="flex items-center space-x-2 mt-1">
                <Badge variant="outline" className="capitalize">
                  {getPlatformIcon(conversation.platform)}
                  <span className="ml-1">{conversation.platform}</span>
                </Badge>
                <span className="text-sm text-gray-500">对话 ID: {id}</span>
              </div>
            )}
          </div>
        </div>
        <Button variant="outline" onClick={loadMessages}>
          <RefreshCw className="h-4 w-4 mr-2" />
          刷新
        </Button>
      </div>

      {/* 消息列表 */}
      {messages.length === 0 ? (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center text-gray-500 py-8">
              <MessageSquare className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p className="text-lg font-medium">暂无消息记录</p>
              <p className="text-sm">这个对话还没有任何消息</p>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {messages.map((message, index) => {
            const isIncoming = isIncomingMessage(message);
            const sender = getMessageSender(message);
            
            return (
              <div
                key={message.id}
                className={`flex ${isIncoming ? 'justify-start' : 'justify-end'}`}
              >
                <div className={`max-w-2xl ${isIncoming ? 'mr-auto' : 'ml-auto'}`}>
                  <Card className={`${
                    isIncoming 
                      ? 'bg-white border-gray-200' 
                      : 'bg-blue-50 border-blue-200'
                  }`}>
                    <CardHeader className="pb-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          {isIncoming ? (
                            <User className="h-4 w-4 text-gray-600" />
                          ) : (
                            <Bot className="h-4 w-4 text-blue-600" />
                          )}
                          <span className="text-sm font-medium text-gray-900">
                            {sender}
                          </span>
                          <Badge 
                            variant={isIncoming ? "secondary" : "default"}
                            className="text-xs"
                          >
                            {isIncoming ? '接收' : '发送'}
                          </Badge>
                        </div>
                        <div className="flex items-center text-xs text-gray-500">
                          <Clock className="h-3 w-3 mr-1" />
                          {formatMessageTime(message.timestamp)}
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <p className="text-gray-900 whitespace-pre-wrap">
                        {message.content}
                      </p>
                      
                      {/* 消息元数据 */}
                      {message.metadata && Object.keys(message.metadata).length > 0 && (
                        <div className="mt-3 pt-3 border-t border-gray-100">
                          <details className="text-xs text-gray-500">
                            <summary className="cursor-pointer hover:text-gray-700">
                              消息详情
                            </summary>
                            <div className="mt-2 space-y-1">
                              {message.messageSid && (
                                <div>消息ID: {message.messageSid}</div>
                              )}
                              {message.metadata.intent && (
                                <div>意图: {message.metadata.intent}</div>
                              )}
                              {message.metadata.usedKnowledge && (
                                <div>使用知识: {message.metadata.usedKnowledge}</div>
                              )}
                              {message.metadata.error && (
                                <div className="text-red-500">错误: {message.metadata.error}</div>
                              )}
                            </div>
                          </details>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </div>
              </div>
            );
          })}
        </div>
      )}

      {/* 对话统计 */}
      {messages.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>对话统计</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold text-gray-900">
                  {messages.length}
                </div>
                <div className="text-sm text-gray-500">总消息数</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-blue-600">
                  {messages.filter(m => m.messageType === 'incoming').length}
                </div>
                <div className="text-sm text-gray-500">接收消息</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-green-600">
                  {messages.filter(m => m.messageType === 'outgoing').length}
                </div>
                <div className="text-sm text-gray-500">发送消息</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-purple-600">
                  {messages.length > 0 ? formatMessageTime(messages[0].timestamp) : '-'}
                </div>
                <div className="text-sm text-gray-500">开始时间</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
