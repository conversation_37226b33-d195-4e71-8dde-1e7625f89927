const mongoose = require('mongoose');

const messageSchema = new mongoose.Schema({
  // Twilio消息ID
  twilioSid: {
    type: String,
    sparse: true, // 允许null值，但如果有值必须唯一
  },

  // 消息方向
  direction: {
    type: String,
    enum: ['outbound', 'inbound'],
    required: true,
  },

  // 发送方和接收方
  from: {
    type: String,
    required: true,
  },
  to: {
    type: String,
    required: true,
  },

  // 消息内容
  body: {
    type: String,
    default: '',
  },

  // 媒体文件
  mediaUrls: [{
    type: String,
  }],

  // 消息类型
  messageType: {
    type: String,
    enum: ['text', 'media', 'template'],
    default: 'text',
  },

  // 消息状态
  status: {
    type: String,
    enum: [
      'queued',      // 排队中
      'sending',     // 发送中
      'sent',        // 已发送
      'delivered',   // 已送达
      'undelivered', // 未送达
      'failed',      // 发送失败
      'received',    // 已接收（入站消息）
      'read'         // 已读
    ],
    default: 'queued',
  },

  // 错误信息
  errorCode: {
    type: String,
  },
  errorMessage: {
    type: String,
  },

  // 价格信息
  price: {
    type: String,
  },
  priceUnit: {
    type: String,
  },

  // 时间戳
  sentAt: {
    type: Date,
  },
  deliveredAt: {
    type: Date,
  },
  readAt: {
    type: Date,
  },

  // 元数据
  metadata: {
    type: mongoose.Schema.Types.Mixed,
    default: {},
  },

  // 会话ID（用于关联对话）
  conversationId: {
    type: String,
  },

  // 业务相关字段
  campaignId: {
    type: String,
  },
  templateId: {
    type: String,
  },

}, {
  timestamps: true, // 自动添加createdAt和updatedAt
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// 索引
messageSchema.index({ twilioSid: 1 }, { unique: true, sparse: true });
messageSchema.index({ from: 1, to: 1 });
messageSchema.index({ conversationId: 1 });
messageSchema.index({ status: 1 });
messageSchema.index({ direction: 1 });
messageSchema.index({ createdAt: -1 });
messageSchema.index({ campaignId: 1 });

// 虚拟字段
messageSchema.virtual('isOutbound').get(function() {
  return this.direction === 'outbound';
});

messageSchema.virtual('isInbound').get(function() {
  return this.direction === 'inbound';
});

// 实例方法
messageSchema.methods.updateStatus = function(status, additionalData = {}) {
  this.status = status;
  
  // 根据状态更新时间戳
  switch (status) {
    case 'sent':
      this.sentAt = new Date();
      break;
    case 'delivered':
      this.deliveredAt = new Date();
      break;
    case 'read':
      this.readAt = new Date();
      break;
  }

  // 更新其他数据
  Object.assign(this, additionalData);
  
  return this.save();
};

// 静态方法
messageSchema.statics.findByConversation = function(conversationId) {
  return this.find({ conversationId }).sort({ createdAt: 1 });
};

messageSchema.statics.findByPhoneNumber = function(phoneNumber) {
  return this.find({
    $or: [
      { from: phoneNumber },
      { to: phoneNumber }
    ]
  }).sort({ createdAt: -1 });
};

module.exports = mongoose.model('Message', messageSchema);
