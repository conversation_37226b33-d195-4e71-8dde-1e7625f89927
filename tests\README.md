# 🧪 WhatsApp机器人测试套件

## 📁 测试目录结构

```
tests/
├── README.md                    # 测试说明文档
├── run-all-tests.js            # 运行所有测试的主入口
├── media-processing/            # 媒体处理相关测试
│   ├── test-twilio-download.js  # Twilio媒体下载测试
│   ├── test-tos-config.js       # TOS配置测试
│   ├── test-tos-upload-fix.js   # TOS上传修复验证
│   ├── test-tos-client.js       # TOS客户端测试
│   ├── test-media-processing.js # 媒体处理测试
│   ├── test-image-message.js    # 图片消息测试
│   ├── test-image-format-v2.js  # 图片格式测试v2
│   └── debug-tos-sdk.js         # TOS SDK调试工具
├── unit/                        # 单元测试
│   └── twilio-media-download.test.js  # Jest单元测试
├── integration/                 # 集成测试
│   └── test-whatsapp-media-integration.js  # WhatsApp媒体集成测试
├── coze-api/                    # Coze API相关测试
│   ├── test-coze-api.js         # Coze API基础测试
│   ├── test-coze-v3.js          # Coze API v3测试
│   ├── test-coze-raw.js         # Coze原始API测试
│   └── test-single-coze.js      # 单一Coze测试
├── whatsapp/                    # WhatsApp相关测试
│   ├── test-whatsapp-message.js # WhatsApp消息测试
│   ├── test-sandbox-message.js  # 沙盒消息测试
│   ├── test-simple-message.js   # 简单消息测试
│   └── test-twilio-connection.js # Twilio连接测试
└── legacy/                      # 历史测试文件
    ├── test-chat-history.js     # 聊天历史测试
    ├── test-error-handling.js   # 错误处理测试
    ├── test-fixed-error-handling.js # 修复的错误处理测试
    ├── test-reply-messages.js   # 回复消息测试
    ├── test-server.js           # 服务器测试
    └── test-your-specific-template.js # 特定模板测试
```

## 🚀 快速开始

### 📋 环境准备

1. **设置环境变量**：
   ```bash
   TWILIO_ACCOUNT_SID=your_account_sid
   TWILIO_AUTH_TOKEN=your_auth_token
   ```

2. **安装依赖**：
   ```bash
   npm install
   ```

### 🎯 运行测试

#### 运行所有测试：
```bash
npm run test:all
```

#### 运行特定类型的测试：

**媒体处理测试**：
```bash
# Twilio下载和TOS上传完整流程
npm run test:media-processing

# 或直接运行
node tests/media-processing/test-twilio-download.js
```

**单元测试**：
```bash
# Jest单元测试套件
npm run test:unit

# 或直接运行
npm run test:media
```

**集成测试**：
```bash
# WhatsApp媒体集成测试
npm run test:integration

# 或直接运行
node tests/integration/test-whatsapp-media-integration.js
```

## 📊 测试覆盖范围

### 🔸 媒体处理测试 (`media-processing/`)

#### 1. **Twilio媒体下载测试** (`test-twilio-download.js`)
- ✅ HTTP Basic Auth认证
- ✅ 媒体文件下载
- ✅ TOS上传功能
- ✅ 本地文件清理
- ✅ 完整流程验证

#### 2. **TOS配置测试** (`test-tos-config.js`)
- ✅ 多种配置方式验证
- ✅ 存储桶访问测试
- ✅ URL构建验证
- ✅ 简单上传测试

#### 3. **TOS上传修复验证** (`test-tos-upload-fix.js`)
- ✅ 修复后的上传功能
- ✅ URL可访问性验证
- ✅ 完整媒体处理流程

#### 4. **TOS SDK调试工具** (`debug-tos-sdk.js`)
- ✅ 网络连接测试
- ✅ DNS解析验证
- ✅ 配置问题诊断

### 🔸 单元测试 (`unit/`)

#### **Jest测试套件** (`twilio-media-download.test.js`)
- ✅ Twilio媒体下载
- ✅ 认证失败处理
- ✅ 网络超时处理
- ✅ 文件完整性验证
- ✅ 完整处理流程
- ✅ TOS上传失败处理

### 🔸 集成测试 (`integration/`)

#### **WhatsApp媒体集成测试** (`test-whatsapp-media-integration.js`)
- ✅ 服务器健康检查
- ✅ 文本消息处理
- ✅ 图片消息处理
- ✅ 纯图片消息处理
- ✅ 会话管理验证

## 🛠️ 测试工具和配置

### 📝 **测试框架**：
- **Jest** - 单元测试框架
- **Axios** - HTTP请求测试
- **Node.js** - 独立测试脚本

### 🔧 **测试配置**：
- **超时设置** - 适应网络操作
- **自动清理** - 测试后清理临时文件
- **错误处理** - 完善的异常处理机制

### 📊 **测试数据**：
- **真实Twilio媒体URL** - 实际的媒体文件测试
- **模拟数据** - 用于单元测试的模拟数据
- **错误场景** - 各种异常情况的测试

## 🎊 测试结果示例

### ✅ **成功的测试输出**：
```
🎯 Twilio媒体下载和TOS上传测试套件
============================================================
1. 基本媒体下载: ✅ 通过
2. 认证失败处理: ✅ 通过  
3. 完整处理流程: ✅ 通过

📊 完整流程统计:
   下载成功: ✅
   TOS上传: ✅
   本地清理: ✅
   文件大小: 18.29 KB
   文件类型: image/jpeg
   公开URL: https://whatsapp.tos-cn-beijing.volces.com/...
```

## 📝 注意事项

### ⚠️ **环境要求**：
- 需要有效的Twilio凭据
- 需要火山引擎TOS访问权限
- 需要网络连接到相关服务

### 🔍 **故障排除**：
- 检查环境变量设置
- 验证网络连接
- 查看详细的错误日志
- 确认服务权限配置

### 🚀 **持续集成**：
- 所有测试都支持自动化运行
- 包含完整的错误处理和报告
- 支持并行测试执行

---

*测试套件版本: 1.0.0*  
*最后更新: 2025年7月21日*  
*状态: ✅ 完整可用*
