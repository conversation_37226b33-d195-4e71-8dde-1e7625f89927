const request = require('supertest');
const app = require('../src/app');
const { Message, Conversation } = require('../src/models');
const database = require('../src/config/database');

// 测试配置
const API_KEY = process.env.API_KEY || 'test-api-key';

describe('Status API Tests', () => {
  let testMessage;
  let testConversation;

  beforeAll(async () => {
    // 连接测试数据库
    process.env.MONGODB_URI = process.env.MONGODB_TEST_URI || 'mongodb://localhost:27017/twilio_messaging_test';
    await database.connect();
  });

  afterAll(async () => {
    // 清理测试数据并断开连接
    await Message.deleteMany({});
    await Conversation.deleteMany({});
    await database.disconnect();
  });

  beforeEach(async () => {
    // 每个测试前清理数据
    await Message.deleteMany({});
    await Conversation.deleteMany({});

    // 创建测试消息
    testMessage = await Message.create({
      twilioSid: 'SM_test_123456',
      direction: 'outbound',
      from: '+0987654321',
      to: '+1234567890',
      body: 'Test message',
      status: 'sent',
      conversationId: 'test-conv-456',
      campaignId: 'test-campaign'
    });

    // 创建测试会话
    testConversation = await Conversation.create({
      conversationId: 'test-conv-456',
      participants: [
        { phoneNumber: '+0987654321', role: 'business' },
        { phoneNumber: '+1234567890', role: 'customer' }
      ],
      messageCount: {
        total: 1,
        outbound: 1,
        inbound: 0,
        unread: 0
      }
    });
  });

  describe('GET /api/status/message/:messageId', () => {
    it('should get message status successfully', async () => {
      const response = await request(app)
        .get(`/api/status/message/${testMessage._id}`)
        .set('X-API-Key', API_KEY)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('messageId');
      expect(response.body.data).toHaveProperty('twilioSid');
      expect(response.body.data).toHaveProperty('status');
      expect(response.body.data.status).toBe('sent');
    });

    it('should return 404 for non-existent message', async () => {
      const fakeId = '507f1f77bcf86cd799439011';
      const response = await request(app)
        .get(`/api/status/message/${fakeId}`)
        .set('X-API-Key', API_KEY)
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Message not found');
    });

    it('should fail with invalid message ID format', async () => {
      const response = await request(app)
        .get('/api/status/message/invalid-id')
        .set('X-API-Key', API_KEY)
        .expect(400);

      expect(response.body.success).toBe(false);
    });
  });

  describe('POST /api/status/sync', () => {
    it('should sync message status from Twilio', async () => {
      const syncData = {
        twilioSid: testMessage.twilioSid
      };

      // 注意：这个测试在没有真实Twilio连接时会失败
      // 在实际环境中需要mock Twilio API
      const response = await request(app)
        .post('/api/status/sync')
        .set('X-API-Key', API_KEY)
        .send(syncData)
        .expect(500); // 预期失败，因为没有真实的Twilio连接

      expect(response.body.success).toBe(false);
    });

    it('should fail with missing twilioSid', async () => {
      const response = await request(app)
        .post('/api/status/sync')
        .set('X-API-Key', API_KEY)
        .send({})
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Validation error');
    });
  });

  describe('GET /api/status/conversation/:conversationId', () => {
    it('should get conversation status successfully', async () => {
      const response = await request(app)
        .get(`/api/status/conversation/${testConversation.conversationId}`)
        .set('X-API-Key', API_KEY)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('conversationId');
      expect(response.body.data).toHaveProperty('messageCount');
      expect(response.body.data).toHaveProperty('statusBreakdown');
      expect(response.body.data.messageCount.total).toBe(1);
    });

    it('should return 404 for non-existent conversation', async () => {
      const response = await request(app)
        .get('/api/status/conversation/non-existent-conv')
        .set('X-API-Key', API_KEY)
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Conversation not found');
    });
  });

  describe('GET /api/status/delivery-report', () => {
    beforeEach(async () => {
      // 创建更多测试消息用于报告
      await Message.create({
        direction: 'outbound',
        from: '+0987654321',
        to: '+1111111111',
        body: 'Delivered message',
        status: 'delivered',
        conversationId: 'test-conv-789',
        price: '0.0075',
        priceUnit: 'USD'
      });

      await Message.create({
        direction: 'outbound',
        from: '+0987654321',
        to: '+2222222222',
        body: 'Failed message',
        status: 'failed',
        conversationId: 'test-conv-890',
        errorCode: '30008',
        errorMessage: 'Unknown error'
      });
    });

    it('should generate delivery report successfully', async () => {
      const response = await request(app)
        .get('/api/status/delivery-report?direction=outbound')
        .set('X-API-Key', API_KEY)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('summary');
      expect(response.body.data).toHaveProperty('statusBreakdown');
      expect(response.body.data.summary).toHaveProperty('totalMessages');
      expect(response.body.data.summary).toHaveProperty('deliveryRate');
      expect(response.body.data.summary).toHaveProperty('failureRate');
    });

    it('should filter by date range', async () => {
      const startDate = new Date().toISOString().split('T')[0];
      const endDate = new Date().toISOString().split('T')[0];

      const response = await request(app)
        .get(`/api/status/delivery-report?startDate=${startDate}&endDate=${endDate}`)
        .set('X-API-Key', API_KEY)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.filters).toHaveProperty('startDate');
      expect(response.body.data.filters).toHaveProperty('endDate');
    });

    it('should filter by phone number', async () => {
      const response = await request(app)
        .get('/api/status/delivery-report?phoneNumber=+1111111111')
        .set('X-API-Key', API_KEY)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.filters.phoneNumber).toBe('+1111111111');
    });
  });

  describe('POST /api/status/real-time', () => {
    it('should get real-time status for multiple messages', async () => {
      const requestData = {
        messageIds: [testMessage._id.toString()]
      };

      const response = await request(app)
        .post('/api/status/real-time')
        .set('X-API-Key', API_KEY)
        .send(requestData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveLength(1);
      expect(response.body.data[0]).toHaveProperty('messageId');
      expect(response.body.data[0]).toHaveProperty('status');
      expect(response.body.data[0]).toHaveProperty('hasError');
    });

    it('should handle empty message IDs array', async () => {
      const requestData = {
        messageIds: []
      };

      const response = await request(app)
        .post('/api/status/real-time')
        .set('X-API-Key', API_KEY)
        .send(requestData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Validation error');
    });

    it('should handle non-existent message IDs', async () => {
      const fakeId = '507f1f77bcf86cd799439011';
      const requestData = {
        messageIds: [fakeId]
      };

      const response = await request(app)
        .post('/api/status/real-time')
        .set('X-API-Key', API_KEY)
        .send(requestData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveLength(0);
    });
  });

  describe('POST /api/status/batch-sync', () => {
    it('should handle batch sync request', async () => {
      const requestData = {
        twilioSids: [testMessage.twilioSid]
      };

      // 注意：这个测试在没有真实Twilio连接时会失败
      const response = await request(app)
        .post('/api/status/batch-sync')
        .set('X-API-Key', API_KEY)
        .send(requestData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('success');
      expect(response.body.data).toHaveProperty('failed');
      expect(response.body.data).toHaveProperty('results');
      expect(response.body.data).toHaveProperty('errors');
    });

    it('should fail with empty twilioSids array', async () => {
      const requestData = {
        twilioSids: []
      };

      const response = await request(app)
        .post('/api/status/batch-sync')
        .set('X-API-Key', API_KEY)
        .send(requestData)
        .expect(400);

      expect(response.body.success).toBe(false);
    });
  });
});
