// 运行所有测试的主入口
const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

class TestRunner {
  constructor() {
    this.results = {
      total: 0,
      passed: 0,
      failed: 0,
      skipped: 0,
      details: []
    };
  }

  // 运行单个测试文件
  async runTest(testFile, testName, timeout = 120000) {
    console.log(`\n🧪 运行测试: ${testName}`);
    console.log(`📁 文件: ${testFile}`);
    console.log('-'.repeat(60));

    return new Promise((resolve) => {
      const startTime = Date.now();
      
      // 检查文件是否存在
      if (!fs.existsSync(testFile)) {
        console.log(`❌ 测试文件不存在: ${testFile}`);
        this.results.total++;
        this.results.failed++;
        this.results.details.push({
          name: testName,
          status: 'failed',
          error: '文件不存在',
          duration: 0
        });
        resolve(false);
        return;
      }

      const process = spawn('node', [testFile], {
        cwd: path.dirname(path.dirname(__filename)),
        stdio: 'pipe'
      });

      let output = '';
      let errorOutput = '';

      process.stdout.on('data', (data) => {
        const text = data.toString();
        output += text;
        console.log(text.trim());
      });

      process.stderr.on('data', (data) => {
        const text = data.toString();
        errorOutput += text;
        console.error(text.trim());
      });

      // 设置超时
      const timer = setTimeout(() => {
        process.kill('SIGTERM');
        console.log(`⏰ 测试超时: ${testName}`);
      }, timeout);

      process.on('close', (code) => {
        clearTimeout(timer);
        const duration = Date.now() - startTime;
        
        this.results.total++;
        
        if (code === 0) {
          console.log(`✅ 测试通过: ${testName} (${duration}ms)`);
          this.results.passed++;
          this.results.details.push({
            name: testName,
            status: 'passed',
            duration: duration
          });
          resolve(true);
        } else {
          console.log(`❌ 测试失败: ${testName} (退出码: ${code})`);
          this.results.failed++;
          this.results.details.push({
            name: testName,
            status: 'failed',
            error: errorOutput || `退出码: ${code}`,
            duration: duration
          });
          resolve(false);
        }
      });

      process.on('error', (error) => {
        clearTimeout(timer);
        console.error(`💥 测试执行错误: ${testName}`, error.message);
        this.results.total++;
        this.results.failed++;
        this.results.details.push({
          name: testName,
          status: 'failed',
          error: error.message,
          duration: Date.now() - startTime
        });
        resolve(false);
      });
    });
  }

  // 运行Jest测试
  async runJestTest(testPattern, testName) {
    console.log(`\n🧪 运行Jest测试: ${testName}`);
    console.log(`📁 模式: ${testPattern}`);
    console.log('-'.repeat(60));

    return new Promise((resolve) => {
      const startTime = Date.now();
      
      const process = spawn('npm', ['run', 'test:media'], {
        cwd: path.dirname(path.dirname(__filename)),
        stdio: 'pipe',
        shell: true
      });

      let output = '';
      let errorOutput = '';

      process.stdout.on('data', (data) => {
        const text = data.toString();
        output += text;
        console.log(text.trim());
      });

      process.stderr.on('data', (data) => {
        const text = data.toString();
        errorOutput += text;
        console.error(text.trim());
      });

      process.on('close', (code) => {
        const duration = Date.now() - startTime;
        
        this.results.total++;
        
        // Jest可能返回非0退出码但仍有部分测试通过
        const hasPassedTests = output.includes('passed') || output.includes('✓');
        
        if (code === 0 || hasPassedTests) {
          console.log(`✅ Jest测试完成: ${testName} (${duration}ms)`);
          this.results.passed++;
          this.results.details.push({
            name: testName,
            status: 'passed',
            duration: duration
          });
          resolve(true);
        } else {
          console.log(`❌ Jest测试失败: ${testName} (退出码: ${code})`);
          this.results.failed++;
          this.results.details.push({
            name: testName,
            status: 'failed',
            error: errorOutput || `退出码: ${code}`,
            duration: duration
          });
          resolve(false);
        }
      });
    });
  }

  // 打印测试总结
  printSummary() {
    console.log('\n🎊 测试总结');
    console.log('=' .repeat(80));
    
    console.log(`📊 总体统计:`);
    console.log(`   总测试数: ${this.results.total}`);
    console.log(`   通过: ${this.results.passed} ✅`);
    console.log(`   失败: ${this.results.failed} ❌`);
    console.log(`   跳过: ${this.results.skipped} ⚠️`);
    
    const successRate = this.results.total > 0 ? 
      ((this.results.passed / this.results.total) * 100).toFixed(1) : 0;
    console.log(`   成功率: ${successRate}%`);
    
    console.log(`\n📋 详细结果:`);
    this.results.details.forEach((test, index) => {
      const status = test.status === 'passed' ? '✅' : '❌';
      const duration = `${test.duration}ms`;
      console.log(`   ${index + 1}. ${status} ${test.name} (${duration})`);
      
      if (test.error) {
        console.log(`      错误: ${test.error}`);
      }
    });
    
    if (this.results.failed === 0) {
      console.log('\n🎉 所有测试都通过了！');
    } else {
      console.log(`\n⚠️ 有 ${this.results.failed} 个测试失败，请检查上面的错误信息。`);
    }
  }
}

// 主函数
async function main() {
  console.log('🎯 WhatsApp机器人测试套件 - 完整测试运行');
  console.log('=' .repeat(80));
  
  const runner = new TestRunner();
  const baseDir = path.dirname(__filename);
  
  // 定义测试列表
  const tests = [
    // 媒体处理测试
    {
      file: path.join(baseDir, 'media-processing', 'test-twilio-download.js'),
      name: 'Twilio媒体下载和TOS上传',
      timeout: 180000
    },
    {
      file: path.join(baseDir, 'media-processing', 'test-tos-upload-fix.js'),
      name: 'TOS上传修复验证',
      timeout: 120000
    },
    {
      file: path.join(baseDir, 'media-processing', 'test-tos-config.js'),
      name: 'TOS配置测试',
      timeout: 120000
    },

    // 集成测试
    {
      file: path.join(baseDir, 'integration', 'test-whatsapp-media-integration.js'),
      name: 'WhatsApp媒体集成测试',
      timeout: 120000
    },

    // Coze API测试
    {
      file: path.join(baseDir, 'coze-api', 'test-coze-v3.js'),
      name: 'Coze API v3测试',
      timeout: 120000
    },

    // WhatsApp测试
    {
      file: path.join(baseDir, 'whatsapp', 'test-whatsapp-message.js'),
      name: 'WhatsApp消息测试',
      timeout: 120000
    }
  ];
  
  console.log(`📋 计划运行 ${tests.length + 1} 个测试套件...\n`);
  
  // 运行独立测试
  for (const test of tests) {
    await runner.runTest(test.file, test.name, test.timeout);
    
    // 测试间等待
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
  
  // 运行Jest单元测试
  await runner.runJestTest('tests/unit/*.test.js', 'Jest单元测试套件');
  
  // 打印总结
  runner.printSummary();
  
  // 返回退出码
  process.exit(runner.results.failed === 0 ? 0 : 1);
}

// 错误处理
process.on('uncaughtException', (error) => {
  console.error('💥 未捕获的异常:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('💥 未处理的Promise拒绝:', reason);
  process.exit(1);
});

// 运行测试
main().catch(error => {
  console.error('💥 测试运行失败:', error);
  process.exit(1);
});
