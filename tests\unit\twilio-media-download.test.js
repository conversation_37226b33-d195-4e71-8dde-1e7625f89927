// Twilio媒体文件下载和TOS上传单元测试
const axios = require('axios');
const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const VolcengineTOSClient = require('../volcengine-tos-client');
require('dotenv').config();

describe('Twilio Media Download and TOS Upload Tests', () => {
  const mediaUrl = 'https://api.twilio.com/2010-04-01/Accounts/AC7657552c992e2a3737961532e7e609d1/Messages/MM21994ae953abd5c3485519e155f56a7d/Media/ME5959d96509ce669c358cafa8643ee2d9';
  const accountSid = process.env.TWILIO_ACCOUNT_SID;
  const authToken = process.env.TWILIO_AUTH_TOKEN;

  // 初始化TOS客户端
  let tosClient;

  // 跳过测试如果没有环境变量
  const skipIfNoCredentials = () => {
    if (!accountSid || !authToken) {
      console.warn('⚠️ 跳过Twilio测试: 缺少TWILIO_ACCOUNT_SID或TWILIO_AUTH_TOKEN环境变量');
      return true;
    }
    return false;
  };

  // 测试下载目录
  const downloadDir = path.join(__dirname, 'downloads');

  // 存储测试过程中创建的文件路径，用于清理
  const testFiles = [];
  
  beforeAll(async () => {
    // 创建下载目录
    if (!fs.existsSync(downloadDir)) {
      fs.mkdirSync(downloadDir, { recursive: true });
    }

    // 初始化TOS客户端
    tosClient = new VolcengineTOSClient();
    try {
      await tosClient.initialize();
      console.log('✅ TOS客户端初始化成功');
    } catch (error) {
      console.warn('⚠️ TOS客户端初始化失败:', error.message);
    }
  });

  afterAll(() => {
    // 清理下载的测试文件
    console.log('🧹 清理测试文件...');

    testFiles.forEach(filePath => {
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
        console.log('🗑️ 已删除:', filePath);
      }
    });

    if (fs.existsSync(downloadDir)) {
      const files = fs.readdirSync(downloadDir);
      files.forEach(file => {
        const filePath = path.join(downloadDir, file);
        if (fs.existsSync(filePath)) {
          fs.unlinkSync(filePath);
        }
      });

      // 只有目录为空时才删除
      try {
        fs.rmdirSync(downloadDir);
        console.log('🗑️ 已删除下载目录');
      } catch (error) {
        // 目录不为空或其他错误，忽略
      }
    }

    console.log('✅ 测试清理完成');
  });

  test('应该能够使用Basic Auth下载Twilio媒体文件', async () => {
    // 跳过测试如果没有凭据
    if (skipIfNoCredentials()) {
      return;
    }

    // 检查环境变量
    expect(accountSid).toBeDefined();
    expect(authToken).toBeDefined();
    expect(accountSid).toMatch(/^AC[a-f0-9]{32}$/);

    console.log('🧪 开始测试Twilio媒体下载...');
    console.log('📋 测试配置:');
    console.log('   Account SID:', accountSid);
    console.log('   媒体URL:', mediaUrl);
    
    try {
      // 使用HTTP Basic Auth下载文件
      const response = await axios.get(mediaUrl, {
        auth: {
          username: accountSid,
          password: authToken
        },
        responseType: 'arraybuffer',
        timeout: 30000,
        headers: {
          'User-Agent': 'WhatsApp-Bot-Test/1.0'
        }
      });
      
      // 验证响应
      expect(response.status).toBe(200);
      expect(response.data).toBeDefined();
      expect(response.data.length).toBeGreaterThan(0);
      
      console.log('✅ 下载成功:');
      console.log('   状态码:', response.status);
      console.log('   文件大小:', response.data.length, 'bytes');
      console.log('   内容类型:', response.headers['content-type']);
      
      // 生成文件名
      const contentType = response.headers['content-type'] || 'application/octet-stream';
      let extension = '.bin';
      
      if (contentType.includes('image/jpeg')) extension = '.jpg';
      else if (contentType.includes('image/png')) extension = '.png';
      else if (contentType.includes('image/gif')) extension = '.gif';
      else if (contentType.includes('image/webp')) extension = '.webp';
      else if (contentType.includes('audio/')) extension = '.mp3';
      else if (contentType.includes('video/')) extension = '.mp4';
      
      const timestamp = Date.now();
      const fileName = `twilio-media-${timestamp}${extension}`;
      const filePath = path.join(downloadDir, fileName);
      
      // 保存文件到本地
      fs.writeFileSync(filePath, response.data);
      
      // 验证文件已保存
      expect(fs.existsSync(filePath)).toBe(true);
      const fileStats = fs.statSync(filePath);
      expect(fileStats.size).toBe(response.data.length);
      
      console.log('💾 文件保存成功:');
      console.log('   文件路径:', filePath);
      console.log('   文件大小:', fileStats.size, 'bytes');
      
      // 计算文件哈希值（用于验证完整性）
      const fileBuffer = fs.readFileSync(filePath);
      const hash = crypto.createHash('md5').update(fileBuffer).digest('hex');
      
      console.log('🔍 文件验证:');
      console.log('   MD5哈希:', hash);
      console.log('   文件类型:', contentType);

      // 记录文件路径用于清理
      testFiles.push(filePath);

      // 返回测试结果
      return {
        success: true,
        filePath: filePath,
        fileSize: fileStats.size,
        contentType: contentType,
        hash: hash
      };
      
    } catch (error) {
      console.error('❌ 下载失败:', error.message);
      
      if (error.response) {
        console.error('   状态码:', error.response.status);
        console.error('   状态文本:', error.response.statusText);
        console.error('   响应头:', error.response.headers);
      }
      
      throw error;
    }
  }, 60000); // 60秒超时

  test('应该能够处理认证失败的情况', async () => {
    console.log('🧪 测试认证失败处理...');
    
    try {
      await axios.get(mediaUrl, {
        auth: {
          username: 'invalid_sid',
          password: 'invalid_token'
        },
        responseType: 'arraybuffer',
        timeout: 10000
      });
      
      // 如果没有抛出错误，测试失败
      fail('应该抛出认证错误');
      
    } catch (error) {
      // 验证是认证错误
      expect(error.response).toBeDefined();
      expect(error.response.status).toBe(401);
      
      console.log('✅ 正确处理认证失败:');
      console.log('   状态码:', error.response.status);
      console.log('   错误信息:', error.message);
    }
  });

  test('应该能够处理网络超时', async () => {
    console.log('🧪 测试网络超时处理...');
    
    try {
      await axios.get(mediaUrl, {
        auth: {
          username: accountSid,
          password: authToken
        },
        responseType: 'arraybuffer',
        timeout: 1 // 1毫秒超时，必定失败
      });
      
      fail('应该抛出超时错误');
      
    } catch (error) {
      expect(error.code).toBe('ECONNABORTED');
      expect(error.message).toContain('timeout');
      
      console.log('✅ 正确处理网络超时:');
      console.log('   错误代码:', error.code);
      console.log('   错误信息:', error.message);
    }
  });

  test('应该能够验证文件完整性', async () => {
    console.log('🧪 测试文件完整性验证...');
    
    // 下载文件两次，比较哈希值
    const download1 = await axios.get(mediaUrl, {
      auth: {
        username: accountSid,
        password: authToken
      },
      responseType: 'arraybuffer',
      timeout: 30000
    });
    
    const download2 = await axios.get(mediaUrl, {
      auth: {
        username: accountSid,
        password: authToken
      },
      responseType: 'arraybuffer',
      timeout: 30000
    });
    
    // 计算哈希值
    const hash1 = crypto.createHash('md5').update(download1.data).digest('hex');
    const hash2 = crypto.createHash('md5').update(download2.data).digest('hex');
    
    // 验证两次下载的文件相同
    expect(hash1).toBe(hash2);
    expect(download1.data.length).toBe(download2.data.length);
    
    console.log('✅ 文件完整性验证通过:');
    console.log('   第一次下载哈希:', hash1);
    console.log('   第二次下载哈希:', hash2);
    console.log('   文件大小:', download1.data.length, 'bytes');
  });

  test('应该能够完整处理：下载→上传TOS→清理本地文件', async () => {
    // 跳过测试如果没有凭据
    if (skipIfNoCredentials()) {
      return;
    }

    console.log('🧪 测试完整的媒体处理流程...');
    console.log('📋 流程: Twilio下载 → TOS上传 → 本地清理');

    let localFilePath = null;
    let tosUploadResult = null;

    try {
      // 步骤1: 从Twilio下载媒体文件
      console.log('\n📥 步骤1: 下载Twilio媒体文件...');
      const downloadResponse = await axios.get(mediaUrl, {
        auth: {
          username: accountSid,
          password: authToken
        },
        responseType: 'arraybuffer',
        timeout: 30000
      });

      expect(downloadResponse.status).toBe(200);
      expect(downloadResponse.data.length).toBeGreaterThan(0);

      console.log('✅ 下载成功:');
      console.log('   文件大小:', downloadResponse.data.length, 'bytes');
      console.log('   内容类型:', downloadResponse.headers['content-type']);

      // 步骤2: 保存到本地临时文件
      console.log('\n💾 步骤2: 保存到本地临时文件...');
      const contentType = downloadResponse.headers['content-type'] || 'image/jpeg';
      const extension = getFileExtension(contentType);
      const timestamp = Date.now();
      const fileName = `test-complete-flow-${timestamp}${extension}`;
      localFilePath = path.join(downloadDir, fileName);

      fs.writeFileSync(localFilePath, downloadResponse.data);

      // 验证文件保存成功
      expect(fs.existsSync(localFilePath)).toBe(true);
      const fileStats = fs.statSync(localFilePath);
      expect(fileStats.size).toBe(downloadResponse.data.length);

      console.log('✅ 本地保存成功:');
      console.log('   文件路径:', localFilePath);
      console.log('   文件大小:', fileStats.size, 'bytes');

      // 步骤3: 上传到TOS
      console.log('\n☁️ 步骤3: 上传到火山引擎TOS...');

      if (tosClient) {
        try {
          // 生成TOS文件名
          const tosFileName = tosClient.generateFileName(mediaUrl, contentType);

          // 上传到TOS
          tosUploadResult = await tosClient.uploadToTOS(
            downloadResponse.data,
            tosFileName,
            contentType
          );

          console.log('✅ TOS上传成功:');
          console.log('   公开URL:', tosUploadResult.url);
          console.log('   文件Key:', tosUploadResult.key);
          console.log('   存储桶:', tosUploadResult.bucket);

          // 验证上传结果
          expect(tosUploadResult.url).toBeDefined();
          expect(tosUploadResult.key).toBeDefined();
          expect(tosUploadResult.bucket).toBe('whatsapp');

          // 验证URL格式
          expect(tosUploadResult.url).toMatch(/^https:\/\/whatsapp\.tos-cn-beijing\.volces\.com\//);

        } catch (tosError) {
          console.log('⚠️ TOS上传失败:', tosError.message);
          console.log('   这可能是由于网络连接或TOS配置问题');
          // 不让TOS错误导致整个测试失败
        }
      } else {
        console.log('⚠️ TOS客户端未初始化，跳过上传步骤');
      }

      // 步骤4: 清理本地文件
      console.log('\n🧹 步骤4: 清理本地缓存文件...');

      // 验证文件存在
      expect(fs.existsSync(localFilePath)).toBe(true);

      // 删除本地文件
      fs.unlinkSync(localFilePath);

      // 验证文件已删除
      expect(fs.existsSync(localFilePath)).toBe(false);

      console.log('✅ 本地文件清理成功');
      console.log('   已删除:', localFilePath);

      // 步骤5: 验证完整流程
      console.log('\n🎯 步骤5: 验证完整流程...');

      const processResult = {
        downloadSuccess: true,
        downloadSize: downloadResponse.data.length,
        localSaveSuccess: true,
        tosUploadSuccess: tosUploadResult !== null,
        localCleanupSuccess: !fs.existsSync(localFilePath),
        publicUrl: tosUploadResult?.url || null
      };

      console.log('📊 流程验证结果:');
      console.log('   下载成功:', processResult.downloadSuccess ? '✅' : '❌');
      console.log('   本地保存:', processResult.localSaveSuccess ? '✅' : '❌');
      console.log('   TOS上传:', processResult.tosUploadSuccess ? '✅' : '⚠️');
      console.log('   本地清理:', processResult.localCleanupSuccess ? '✅' : '❌');

      if (processResult.publicUrl) {
        console.log('   公开URL:', processResult.publicUrl);
      }

      // 验证关键步骤
      expect(processResult.downloadSuccess).toBe(true);
      expect(processResult.localSaveSuccess).toBe(true);
      expect(processResult.localCleanupSuccess).toBe(true);

      return processResult;

    } catch (error) {
      console.error('❌ 完整流程测试失败:', error.message);

      // 确保清理本地文件（即使测试失败）
      if (localFilePath && fs.existsSync(localFilePath)) {
        try {
          fs.unlinkSync(localFilePath);
          console.log('🧹 已清理失败测试的本地文件');
        } catch (cleanupError) {
          console.error('⚠️ 清理失败:', cleanupError.message);
        }
      }

      throw error;
    }
  }, 120000); // 2分钟超时，因为包含网络操作

  test('应该能够处理TOS上传失败的情况', async () => {
    // 跳过测试如果没有凭据
    if (skipIfNoCredentials()) {
      return;
    }

    console.log('🧪 测试TOS上传失败处理...');

    try {
      // 下载文件
      const downloadResponse = await axios.get(mediaUrl, {
        auth: {
          username: accountSid,
          password: authToken
        },
        responseType: 'arraybuffer',
        timeout: 30000
      });

      // 创建一个无效的TOS客户端来模拟上传失败
      const invalidTosClient = new VolcengineTOSClient();
      // 修改配置使其无效
      invalidTosClient.config.accessKeyId = 'invalid_key';

      try {
        await invalidTosClient.uploadToTOS(
          downloadResponse.data,
          'test-invalid-upload.jpg',
          'image/jpeg'
        );

        fail('应该抛出TOS上传错误');

      } catch (tosError) {
        console.log('✅ 正确处理TOS上传失败:');
        console.log('   错误信息:', tosError.message);
        expect(tosError.message).toContain('上传到对象存储失败');
      }

    } catch (error) {
      console.error('❌ TOS失败测试出错:', error.message);
      throw error;
    }
  });
});

// 辅助函数：获取文件扩展名
function getFileExtension(contentType) {
  const typeMap = {
    'image/jpeg': '.jpg',
    'image/png': '.png',
    'image/gif': '.gif',
    'image/webp': '.webp',
    'audio/mpeg': '.mp3',
    'audio/mp4': '.m4a',
    'video/mp4': '.mp4',
    'video/quicktime': '.mov',
    'application/pdf': '.pdf',
    'text/plain': '.txt'
  };
  
  return typeMap[contentType] || '.bin';
}

// 辅助函数：格式化文件大小
function formatFileSize(bytes) {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
