// 快速API测试
const axios = require('axios');

const BASE_URL = 'http://localhost:3001';
const API_KEY = 'demo_api_key_12345';

async function quickTest() {
  console.log('🚀 快速API测试开始...\n');

  try {
    // 1. 健康检查
    console.log('1. 健康检查...');
    const health = await axios.get(`${BASE_URL}/health`);
    console.log('✅ 健康检查成功');
    console.log('   状态:', health.data.status);
    console.log('');

    // 2. 发送消息
    console.log('2. 发送消息...');
    const sendResponse = await axios.post(`${BASE_URL}/api/messages/send`, {
      to: '+**********',
      body: 'Hello from Twilio Business Messaging API!',
      campaignId: 'test-campaign'
    }, {
      headers: {
        'X-API-Key': API_KEY,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ 消息发送成功');
    console.log('   消息ID:', sendResponse.data.data.messageId);
    console.log('   Twilio SID:', sendResponse.data.data.twilioSid);
    console.log('   状态:', sendResponse.data.data.status);
    console.log('');

    // 3. 查询消息状态
    console.log('3. 查询消息状态...');
    const messageId = sendResponse.data.data.messageId;
    const statusResponse = await axios.get(`${BASE_URL}/api/status/message/${messageId}`, {
      headers: {
        'X-API-Key': API_KEY
      }
    });
    
    console.log('✅ 状态查询成功');
    console.log('   状态:', statusResponse.data.data.status);
    console.log('   价格:', statusResponse.data.data.price, statusResponse.data.data.priceUnit);
    console.log('');

    // 4. 批量发送
    console.log('4. 批量发送消息...');
    const bulkResponse = await axios.post(`${BASE_URL}/api/messages/send-bulk`, {
      messages: [
        { to: '+1111111111', body: '批量消息 1' },
        { to: '+2222222222', body: '批量消息 2' },
        { to: '+3333333333', body: '批量消息 3' }
      ]
    }, {
      headers: {
        'X-API-Key': API_KEY,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ 批量发送成功');
    console.log('   成功:', bulkResponse.data.data.success, '条');
    console.log('   失败:', bulkResponse.data.data.failed, '条');
    console.log('');

    // 5. 传递报告
    console.log('5. 获取传递报告...');
    const reportResponse = await axios.get(`${BASE_URL}/api/status/delivery-report`, {
      headers: {
        'X-API-Key': API_KEY
      }
    });
    
    console.log('✅ 报告生成成功');
    console.log('   总消息数:', reportResponse.data.data.summary.totalMessages);
    console.log('   传递率:', reportResponse.data.data.summary.deliveryRate + '%');
    console.log('   总费用:', reportResponse.data.data.summary.totalCost, 'USD');
    console.log('');

    // 6. 测试认证失败
    console.log('6. 测试认证保护...');
    try {
      await axios.post(`${BASE_URL}/api/messages/send`, {
        to: '+**********',
        body: 'This should fail'
      });
      console.log('❌ 认证保护失败');
    } catch (authError) {
      if (authError.response && authError.response.status === 401) {
        console.log('✅ 认证保护正常工作');
      } else {
        console.log('⚠️ 意外错误:', authError.message);
      }
    }
    console.log('');

    console.log('🎉 所有测试完成！');
    console.log('\n📋 测试总结:');
    console.log('   ✅ 健康检查端点正常');
    console.log('   ✅ 消息发送API正常');
    console.log('   ✅ 消息状态查询正常');
    console.log('   ✅ 批量发送功能正常');
    console.log('   ✅ 传递报告生成正常');
    console.log('   ✅ 认证保护正常');
    console.log('\n💡 这是演示模式，使用模拟响应');
    console.log('   配置真实Twilio凭据可发送真实消息');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.response) {
      console.error('   状态码:', error.response.status);
      console.error('   错误信息:', error.response.data);
    }
  }
}

quickTest();
