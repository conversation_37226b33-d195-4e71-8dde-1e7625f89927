/**
 * Slack 令牌诊断工具
 * 用于检查和诊断 Slack 令牌的状态，包括过期时间、刷新令牌等
 */

require('dotenv').config();
const SlackAuthDatabase = require('../src/database/slackAuth');
const TokenManager = require('../src/services/tokenManager');
const axios = require('axios');

class SlackTokenDiagnostic {
  constructor() {
    this.db = new SlackAuthDatabase();
    this.tokenManager = new TokenManager();
  }

  /**
   * 运行完整的令牌诊断
   */
  async runDiagnosis() {
    console.log('🔍 开始 Slack 令牌诊断...');
    console.log('=' .repeat(60));

    try {
      // 1. 检查环境配置
      await this.checkEnvironmentConfig();
      
      // 2. 检查数据库中的工作区
      await this.checkWorkspaces();
      
      // 3. 检查用户令牌
      await this.checkUserTokens();
      
      // 4. 测试令牌有效性
      await this.testTokenValidity();
      
      // 5. 检查特定团队的令牌
      await this.checkSpecificTeam('T0983BEJT4J');

    } catch (error) {
      console.error('❌ 诊断过程中出现错误:', error.message);
    }

    console.log('\n🏁 诊断完成');
  }

  /**
   * 检查环境配置
   */
  async checkEnvironmentConfig() {
    console.log('\n📋 检查环境配置...');
    
    const requiredEnvs = [
      'SLACK_CLIENT_ID',
      'SLACK_CLIENT_SECRET', 
      'SLACK_BOT_TOKEN',
      'SLACK_REDIRECT_URI'
    ];

    for (const env of requiredEnvs) {
      const value = process.env[env];
      if (value) {
        console.log(`✅ ${env}: ${this.maskSensitiveData(value)}`);
      } else {
        console.log(`❌ ${env}: 未配置`);
      }
    }
  }

  /**
   * 检查数据库中的工作区
   */
  async checkWorkspaces() {
    console.log('\n🏢 检查工作区信息...');
    
    try {
      const workspaces = await this.db.getAllWorkspaces();
      console.log(`📊 找到 ${workspaces.length} 个工作区`);

      for (const workspace of workspaces) {
        console.log(`\n🏢 工作区: ${workspace.team_name} (${workspace.team_id})`);
        console.log(`   Bot用户ID: ${workspace.bot_user_id}`);
        console.log(`   授权用户: ${workspace.authed_user_id}`);
        console.log(`   权限范围: ${workspace.scope}`);
        console.log(`   安装时间: ${workspace.installed_at}`);
        console.log(`   更新时间: ${workspace.updated_at}`);
        
        // 检查Bot令牌
        if (workspace.bot_user_access_token) {
          console.log(`   Bot令牌: ${this.maskSensitiveData(workspace.bot_user_access_token)}`);
        } else {
          console.log(`   ❌ Bot令牌: 未找到`);
        }

        // 检查Bot刷新令牌
        if (workspace.bot_refresh_token) {
          console.log(`   Bot刷新令牌: ${this.maskSensitiveData(workspace.bot_refresh_token)}`);
        } else {
          console.log(`   ⚠️ Bot刷新令牌: 未找到`);
        }

        // 检查过期时间
        if (workspace.bot_token_expires_at) {
          const expiresAt = new Date(workspace.bot_token_expires_at);
          const now = new Date();
          const isExpired = expiresAt <= now;
          const timeLeft = expiresAt - now;
          
          console.log(`   过期时间: ${expiresAt.toISOString()}`);
          console.log(`   当前时间: ${now.toISOString()}`);
          console.log(`   状态: ${isExpired ? '❌ 已过期' : '✅ 有效'}`);
          
          if (!isExpired) {
            const hoursLeft = Math.floor(timeLeft / (1000 * 60 * 60));
            const minutesLeft = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
            console.log(`   剩余时间: ${hoursLeft}小时${minutesLeft}分钟`);
          } else {
            const hoursExpired = Math.floor(Math.abs(timeLeft) / (1000 * 60 * 60));
            console.log(`   已过期: ${hoursExpired}小时前`);
          }
        } else {
          console.log(`   ⚠️ 过期时间: 未设置（可能是永久令牌）`);
        }
      }
    } catch (error) {
      console.error('❌ 检查工作区失败:', error.message);
    }
  }

  /**
   * 检查用户令牌
   */
  async checkUserTokens() {
    console.log('\n👤 检查用户令牌...');
    
    try {
      // 这里需要添加获取所有用户令牌的方法
      console.log('ℹ️ 用户令牌检查功能待实现');
    } catch (error) {
      console.error('❌ 检查用户令牌失败:', error.message);
    }
  }

  /**
   * 测试令牌有效性
   */
  async testTokenValidity() {
    console.log('\n🧪 测试令牌有效性...');
    
    try {
      const workspaces = await this.db.getAllWorkspaces();
      
      for (const workspace of workspaces) {
        if (workspace.bot_user_access_token) {
          console.log(`\n🧪 测试工作区 ${workspace.team_name} 的Bot令牌...`);
          
          try {
            const response = await axios.post('https://slack.com/api/auth.test', {}, {
              headers: {
                'Authorization': `Bearer ${workspace.bot_user_access_token}`,
                'Content-Type': 'application/json'
              }
            });

            if (response.data.ok) {
              console.log('✅ Bot令牌有效');
              console.log(`   团队: ${response.data.team}`);
              console.log(`   用户: ${response.data.user}`);
              console.log(`   Bot ID: ${response.data.bot_id}`);
            } else {
              console.log('❌ Bot令牌无效');
              console.log(`   错误: ${response.data.error}`);
              
              // 如果令牌无效且有刷新令牌，尝试刷新
              if (workspace.bot_refresh_token) {
                console.log('🔄 尝试刷新Bot令牌...');
                await this.testTokenRefresh(workspace);
              }
            }
          } catch (error) {
            console.log('❌ 令牌测试失败:', error.message);
          }
        }
      }
    } catch (error) {
      console.error('❌ 测试令牌有效性失败:', error.message);
    }
  }

  /**
   * 测试令牌刷新
   */
  async testTokenRefresh(workspace) {
    try {
      console.log('🔄 测试令牌刷新...');
      
      const refreshData = {
        client_id: process.env.SLACK_CLIENT_ID,
        client_secret: process.env.SLACK_CLIENT_SECRET,
        grant_type: 'refresh_token',
        refresh_token: workspace.bot_refresh_token
      };

      console.log('📤 发送刷新请求...');
      console.log('   Client ID:', this.maskSensitiveData(refreshData.client_id));
      console.log('   Refresh Token:', this.maskSensitiveData(refreshData.refresh_token));

      const response = await axios.post('https://slack.com/api/oauth.v2.access', refreshData, {
        headers: {
          'Content-Type': 'application/json'
        }
      });

      console.log('📥 刷新响应:', {
        ok: response.data.ok,
        error: response.data.error,
        access_token: response.data.access_token ? '存在' : '不存在',
        refresh_token: response.data.refresh_token ? '存在' : '不存在',
        expires_in: response.data.expires_in
      });

      if (response.data.ok) {
        console.log('✅ 令牌刷新成功');
        return response.data;
      } else {
        console.log('❌ 令牌刷新失败:', response.data.error);
        return null;
      }
    } catch (error) {
      console.log('❌ 令牌刷新异常:', error.message);
      if (error.response) {
        console.log('   HTTP状态:', error.response.status);
        console.log('   响应数据:', error.response.data);
      }
      return null;
    }
  }

  /**
   * 检查特定团队的令牌
   */
  async checkSpecificTeam(teamId) {
    console.log(`\n🎯 检查特定团队 ${teamId}...`);
    
    try {
      const workspace = await this.db.getWorkspaceByTeamId(teamId);
      
      if (!workspace) {
        console.log(`❌ 未找到团队 ${teamId} 的信息`);
        return;
      }

      console.log('📋 团队详细信息:');
      console.log(`   团队名: ${workspace.team_name}`);
      console.log(`   Bot用户ID: ${workspace.bot_user_id}`);
      console.log(`   Bot令牌: ${workspace.bot_user_access_token ? this.maskSensitiveData(workspace.bot_user_access_token) : '未找到'}`);
      console.log(`   Bot刷新令牌: ${workspace.bot_refresh_token ? this.maskSensitiveData(workspace.bot_refresh_token) : '未找到'}`);
      
      if (workspace.bot_token_expires_at) {
        const expiresAt = new Date(workspace.bot_token_expires_at);
        const now = new Date();
        const isExpired = expiresAt <= now;
        
        console.log(`   过期时间: ${expiresAt.toISOString()}`);
        console.log(`   状态: ${isExpired ? '❌ 已过期' : '✅ 有效'}`);
        
        if (isExpired && workspace.bot_refresh_token) {
          console.log('\n🔄 令牌已过期，尝试使用TokenManager刷新...');
          try {
            const newToken = await this.tokenManager.getBotToken(teamId);
            if (newToken) {
              console.log('✅ TokenManager刷新成功');
              console.log(`   新令牌: ${this.maskSensitiveData(newToken)}`);
            } else {
              console.log('❌ TokenManager刷新失败');
            }
          } catch (error) {
            console.log('❌ TokenManager刷新异常:', error.message);
          }
        }
      }
    } catch (error) {
      console.error(`❌ 检查团队 ${teamId} 失败:`, error.message);
    }
  }

  /**
   * 掩码敏感数据
   */
  maskSensitiveData(data) {
    if (!data) return 'N/A';
    if (data.length <= 10) return data;
    return data.substring(0, 10) + '...' + data.substring(data.length - 4);
  }
}

// 运行诊断
async function main() {
  const diagnostic = new SlackTokenDiagnostic();
  await diagnostic.runDiagnosis();
  process.exit(0);
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = SlackTokenDiagnostic;
