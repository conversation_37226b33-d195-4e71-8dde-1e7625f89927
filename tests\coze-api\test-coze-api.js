// 测试Coze API连接和功能
require('dotenv').config();
const CozeApiClient = require('./coze-api-client');

// 初始化Coze客户端
const cozeClient = new CozeApiClient({
  botId: '7528309468237529127',
  accessToken: 'pat_FSEGBGcfbYwabmxELRPZYAReTrVaWIMMPBwlyIfUeXnqaJsBTcbbIZrpNyEAZwLR',
});

// 测试消息列表
const testMessages = [
  {
    message: "你好，我是新用户",
    description: "测试问候语"
  },
  {
    message: "你能帮我做什么？",
    description: "测试功能询问"
  },
  {
    message: "今天天气怎么样？",
    description: "测试天气询问"
  },
  {
    message: "请帮我写一首关于春天的诗",
    description: "测试创作能力"
  },
  {
    message: "刚才我问了什么问题？",
    description: "测试上下文记忆"
  }
];

// 测试基本连接
async function testBasicConnection() {
  console.log('🔄 测试Coze API基本连接...\n');
  
  try {
    const result = await cozeClient.checkConnection();
    
    if (result.success) {
      console.log('✅ Coze API连接成功!');
      console.log(`   机器人ID: ${result.botId}`);
      console.log(`   测试响应: ${result.response}`);
      return true;
    } else {
      console.log('❌ Coze API连接失败:');
      console.log(`   错误: ${result.error}`);
      if (result.details) {
        console.log(`   详细信息: ${JSON.stringify(result.details, null, 2)}`);
      }
      return false;
    }
  } catch (error) {
    console.error('❌ 连接测试异常:', error.message);
    return false;
  }
}

// 测试单条消息
async function testSingleMessage(message, userId = 'test_user_001') {
  console.log(`📤 测试消息: "${message}"`);
  console.log(`   用户ID: ${userId}`);
  
  try {
    const response = await cozeClient.sendMessage(message, userId);
    
    console.log('✅ 消息发送成功!');
    console.log(`   回复: ${response.text}`);
    console.log(`   会话ID: ${response.conversation_id || 'N/A'}`);
    console.log(`   消息ID: ${response.id || 'N/A'}`);
    console.log('');
    
    return {
      success: true,
      message: message,
      reply: response.text,
      conversationId: response.conversation_id
    };
    
  } catch (error) {
    console.log('❌ 消息发送失败:', error.message);
    console.log('');
    
    return {
      success: false,
      message: message,
      error: error.message
    };
  }
}

// 测试对话上下文
async function testConversationContext() {
  console.log('🔄 测试对话上下文功能...\n');
  
  const testUserId = 'context_test_user';
  const results = [];
  
  for (let i = 0; i < testMessages.length; i++) {
    const testCase = testMessages[i];
    
    console.log(`${i + 1}. ${testCase.description}`);
    
    const result = await testSingleMessage(testCase.message, testUserId);
    results.push(result);
    
    // 等待1秒避免请求过快
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  // 分析结果
  console.log('📊 对话上下文测试结果:');
  console.log(`   总测试数: ${results.length}`);
  console.log(`   成功数: ${results.filter(r => r.success).length}`);
  console.log(`   失败数: ${results.filter(r => !r.success).length}`);
  console.log('');
  
  // 显示对话流程
  console.log('💬 完整对话流程:');
  results.forEach((result, index) => {
    if (result.success) {
      console.log(`   用户: ${result.message}`);
      console.log(`   机器人: ${result.reply.substring(0, 100)}${result.reply.length > 100 ? '...' : ''}`);
      console.log('');
    }
  });
  
  return results;
}

// 测试多用户会话
async function testMultiUserSessions() {
  console.log('🔄 测试多用户会话管理...\n');
  
  const users = [
    { id: 'user_001', name: '用户A' },
    { id: 'user_002', name: '用户B' },
    { id: 'user_003', name: '用户C' }
  ];
  
  const results = [];
  
  for (const user of users) {
    console.log(`👤 测试用户: ${user.name} (${user.id})`);
    
    const message = `你好，我是${user.name}，这是我的第一条消息`;
    const result = await testSingleMessage(message, user.id);
    
    results.push({
      userId: user.id,
      userName: user.name,
      ...result
    });
    
    // 等待1秒
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  // 测试每个用户的第二条消息
  console.log('📝 发送第二轮消息...');
  
  for (const user of users) {
    console.log(`👤 ${user.name}的第二条消息:`);
    
    const message = `你还记得我刚才说我是谁吗？`;
    const result = await testSingleMessage(message, user.id);
    
    results.push({
      userId: user.id,
      userName: user.name,
      round: 2,
      ...result
    });
    
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log('📊 多用户测试完成!');
  return results;
}

// 测试错误处理
async function testErrorHandling() {
  console.log('🔄 测试错误处理...\n');
  
  const errorTests = [
    {
      description: '空消息测试',
      message: '',
      userId: 'error_test_user'
    },
    {
      description: '超长消息测试',
      message: 'A'.repeat(10000),
      userId: 'error_test_user'
    },
    {
      description: '特殊字符测试',
      message: '🎉💻🚀 Hello! @#$%^&*()_+ 你好世界 ñáéíóú',
      userId: 'error_test_user'
    }
  ];
  
  const results = [];
  
  for (const test of errorTests) {
    console.log(`🧪 ${test.description}`);
    
    const result = await testSingleMessage(test.message, test.userId);
    results.push({
      test: test.description,
      ...result
    });
    
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  return results;
}

// 主测试函数
async function runAllTests() {
  console.log('🤖 Coze API 完整测试套件');
  console.log('=' .repeat(60));
  console.log(`🎯 机器人ID: 7528309468237529127`);
  console.log(`🔑 访问令牌: pat_FSEGBG...（已隐藏）`);
  console.log('');
  
  const testResults = {
    connection: null,
    singleMessage: null,
    context: null,
    multiUser: null,
    errorHandling: null
  };
  
  try {
    // 1. 基本连接测试
    console.log('1️⃣ 基本连接测试');
    console.log('-'.repeat(30));
    testResults.connection = await testBasicConnection();
    
    if (!testResults.connection) {
      console.log('❌ 基本连接失败，停止后续测试');
      return testResults;
    }
    
    console.log('\n');
    
    // 2. 单条消息测试
    console.log('2️⃣ 单条消息测试');
    console.log('-'.repeat(30));
    testResults.singleMessage = await testSingleMessage('你好，这是一条测试消息');
    console.log('\n');
    
    // 3. 对话上下文测试
    console.log('3️⃣ 对话上下文测试');
    console.log('-'.repeat(30));
    testResults.context = await testConversationContext();
    console.log('\n');
    
    // 4. 多用户会话测试
    console.log('4️⃣ 多用户会话测试');
    console.log('-'.repeat(30));
    testResults.multiUser = await testMultiUserSessions();
    console.log('\n');
    
    // 5. 错误处理测试
    console.log('5️⃣ 错误处理测试');
    console.log('-'.repeat(30));
    testResults.errorHandling = await testErrorHandling();
    
    // 生成测试报告
    generateTestReport(testResults);
    
  } catch (error) {
    console.error('💥 测试过程中发生异常:', error.message);
  }
  
  return testResults;
}

// 生成测试报告
function generateTestReport(results) {
  console.log('\n📋 测试报告汇总');
  console.log('=' .repeat(60));
  
  console.log('✅ 测试结果:');
  console.log(`   基本连接: ${results.connection ? '✅ 通过' : '❌ 失败'}`);
  console.log(`   单条消息: ${results.singleMessage?.success ? '✅ 通过' : '❌ 失败'}`);
  console.log(`   对话上下文: ${results.context?.filter(r => r.success).length || 0}/${results.context?.length || 0} 通过`);
  console.log(`   多用户会话: ${results.multiUser?.filter(r => r.success).length || 0}/${results.multiUser?.length || 0} 通过`);
  console.log(`   错误处理: ${results.errorHandling?.filter(r => r.success).length || 0}/${results.errorHandling?.length || 0} 通过`);
  
  console.log('\n🎯 总体评估:');
  const overallSuccess = results.connection && results.singleMessage?.success;
  console.log(`   Coze API集成: ${overallSuccess ? '✅ 可用' : '❌ 不可用'}`);
  console.log(`   上下文功能: ${results.context?.some(r => r.success) ? '✅ 支持' : '❌ 不支持'}`);
  console.log(`   多用户支持: ${results.multiUser?.some(r => r.success) ? '✅ 支持' : '❌ 不支持'}`);
  
  console.log('\n💡 建议:');
  if (overallSuccess) {
    console.log('   ✅ API集成正常，可以部署到生产环境');
    console.log('   ✅ 建议启动 WhatsApp Coze 机器人服务');
    console.log('   📝 运行: node whatsapp-coze-bot.js');
  } else {
    console.log('   ❌ 请检查API配置和网络连接');
    console.log('   🔧 确认机器人ID和访问令牌正确');
  }
}

// 主函数
async function main() {
  const command = process.argv[2];
  
  try {
    switch (command) {
      case 'connection':
        await testBasicConnection();
        break;
        
      case 'message':
        const message = process.argv[3] || '你好，这是一条测试消息';
        await testSingleMessage(message);
        break;
        
      case 'context':
        await testConversationContext();
        break;
        
      case 'multi':
        await testMultiUserSessions();
        break;
        
      case 'error':
        await testErrorHandling();
        break;
        
      case 'full':
      default:
        await runAllTests();
        break;
    }
  } catch (error) {
    console.error('💥 测试执行失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = {
  testBasicConnection,
  testSingleMessage,
  testConversationContext,
  testMultiUserSessions,
  testErrorHandling,
  runAllTests
};
