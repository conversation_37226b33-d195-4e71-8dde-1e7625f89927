const webhookService = require('../services/webhookService');
const logger = require('../config/logger');

class WebhookController {

  /**
   * 处理消息状态webhook
   */
  async handleMessageStatus(req, res) {
    try {
      logger.info('Received message status webhook', {
        messageSid: req.body.MessageSid,
        status: req.body.MessageStatus,
        ip: req.ip,
      });

      // 验证webhook签名（可选，但推荐）
      if (process.env.NODE_ENV === 'production') {
        const signature = req.get('X-Twilio-Signature');
        const url = `${req.protocol}://${req.get('host')}${req.originalUrl}`;
        
        if (!webhookService.validateWebhookSignature(signature, url, req.body)) {
          logger.warn('Invalid webhook signature', { ip: req.ip });
          return res.status(403).json({ error: 'Invalid signature' });
        }
      }

      // 处理状态更新
      const result = await webhookService.handleMessageStatus(req.body);

      if (result.success) {
        res.status(200).json({ success: true });
      } else {
        res.status(400).json({ error: result.error });
      }

    } catch (error) {
      logger.error('Message status webhook error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * 处理入站消息webhook
   */
  async handleIncomingMessage(req, res) {
    try {
      logger.info('Received incoming message webhook', {
        messageSid: req.body.MessageSid,
        from: req.body.From,
        to: req.body.To,
        ip: req.ip,
      });

      // 验证webhook签名（可选，但推荐）
      if (process.env.NODE_ENV === 'production') {
        const signature = req.get('X-Twilio-Signature');
        const url = `${req.protocol}://${req.get('host')}${req.originalUrl}`;
        
        if (!webhookService.validateWebhookSignature(signature, url, req.body)) {
          logger.warn('Invalid webhook signature', { ip: req.ip });
          return res.status(403).json({ error: 'Invalid signature' });
        }
      }

      // 处理入站消息
      const result = await webhookService.handleIncomingMessage(req.body);

      if (result.success) {
        // 返回TwiML响应（可选）
        res.set('Content-Type', 'text/xml');
        res.status(200).send(`
          <?xml version="1.0" encoding="UTF-8"?>
          <Response>
            <!-- 可以在这里添加自动回复的TwiML -->
          </Response>
        `);
      } else {
        res.status(400).json({ error: result.error });
      }

    } catch (error) {
      logger.error('Incoming message webhook error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * 处理通用webhook（用于调试）
   */
  async handleGenericWebhook(req, res) {
    try {
      logger.info('Received generic webhook', {
        headers: req.headers,
        body: req.body,
        query: req.query,
        ip: req.ip,
      });

      res.status(200).json({ 
        success: true, 
        message: 'Webhook received',
        timestamp: new Date().toISOString(),
      });

    } catch (error) {
      logger.error('Generic webhook error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Webhook健康检查
   */
  async healthCheck(req, res) {
    try {
      res.status(200).json({
        success: true,
        message: 'Webhook endpoint is healthy',
        timestamp: new Date().toISOString(),
        version: process.env.npm_package_version || '1.0.0',
      });
    } catch (error) {
      logger.error('Webhook health check error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
}

module.exports = new WebhookController();
