# Zep Cloud 集成文档

## 概述

Zep Cloud 是一个为AI助手提供长期记忆的服务平台。它可以帮助AI助手记住过去的对话，无论时间多久远，同时减少幻觉、延迟和成本。

## 主要功能

### 🧠 记忆管理
- **聊天历史存储**: 自动保存和检索对话历史
- **智能摘要**: 自动生成对话摘要
- **上下文感知**: 基于历史对话提供相关上下文

### 🕸️ 知识图谱
- **事实提取**: 自动从对话中提取事实信息
- **实体识别**: 识别和跟踪对话中的实体
- **关系映射**: 构建实体间的关系网络

### 📊 结构化数据提取
- **对话分类**: 自动分类对话内容
- **情感分析**: 理解用户意图和情感
- **数据提取**: 从对话中提取结构化业务数据

## 安装和配置

### 1. 安装依赖

```bash
npm install @getzep/zep-cloud
```

### 2. 环境配置

在 `.env` 文件中添加您的 Zep API 密钥：

```env
ZEP_API_KEY=your_zep_api_key_here
```

### 3. 获取 API 密钥

1. 访问 [Zep Cloud](https://www.getzep.com/)
2. 注册账户并创建项目
3. 在项目设置中获取 API 密钥

## 使用示例

### 基本用法

```javascript
const { ZepClient } = require('@getzep/zep-cloud');

const zepClient = new ZepClient({
  apiKey: process.env.ZEP_API_KEY
});

// 创建用户
const user = await zepClient.user.add({
  userId: 'user-123',
  email: '<EMAIL>',
  firstName: 'John',
  lastName: 'Doe'
});

// 创建会话
const session = await zepClient.memory.addSession({
  sessionId: 'session-123',
  userId: 'user-123'
});

// 添加消息到记忆
await zepClient.memory.add('session-123', {
  messages: [
    { roleType: 'user', content: '你好，我是John' },
    { roleType: 'assistant', content: '你好John！很高兴认识你。' }
  ]
});

// 获取记忆
const memory = await zepClient.memory.get('session-123');
console.log(memory.messages); // 获取所有消息
console.log(memory.context);  // 获取上下文信息
```

### 知识图谱使用

```javascript
// 添加结构化数据到知识图谱
await zepClient.graph.add({
  userId: 'user-123',
  type: 'json',
  data: JSON.stringify({
    name: 'John Doe',
    occupation: '软件工程师',
    skills: ['JavaScript', 'Python', 'React'],
    location: '北京'
  })
});

// 搜索知识图谱
const searchResults = await zepClient.graph.search({
  userId: 'user-123',
  query: '用户有什么技能？'
});
```

## 测试文件

### 1. 连接测试 (`test-zep-connection.js`)

快速验证 API 密钥和基本连接：

```bash
node test-zep-connection.js
```

这个测试会：
- 验证 API 密钥有效性
- 测试用户和会话创建
- 测试记忆功能
- 测试知识图谱功能

### 2. 单元测试 (`tests/unit/zep-cloud.test.js`)

完整的单元测试套件：

```bash
npm test tests/unit/zep-cloud.test.js
```

测试覆盖：
- ✅ 用户管理（创建、获取、错误处理）
- ✅ 会话管理（创建、获取）
- ✅ 记忆管理（添加消息、获取记忆、上下文）
- ✅ 知识图谱（添加数据、搜索）
- ✅ 错误处理（无效API密钥、不存在的资源）

### 3. 功能演示 (`zep-demo.js`)

完整的功能演示：

```bash
node zep-demo.js
```

演示内容：
- 🚀 用户和会话初始化
- 🧠 多轮对话记忆存储
- 🕸️ 知识图谱构建和搜索
- 💭 上下文感知的智能对话

## 集成到现有项目

### 在 Twilio 消息处理中使用 Zep

```javascript
const { ZepClient } = require('@getzep/zep-cloud');

class MessageService {
  constructor() {
    this.zepClient = new ZepClient({
      apiKey: process.env.ZEP_API_KEY
    });
  }

  async processMessage(from, body) {
    const userId = from; // 使用电话号码作为用户ID
    const sessionId = `session-${from}`;

    // 确保用户和会话存在
    await this.ensureUserAndSession(userId, sessionId);

    // 添加用户消息到记忆
    const memoryResult = await this.zepClient.memory.add(sessionId, {
      messages: [{ roleType: 'user', content: body }],
      returnContext: true
    });

    // 使用上下文生成回复
    const response = await this.generateResponse(body, memoryResult.context);

    // 添加助手回复到记忆
    await this.zepClient.memory.add(sessionId, {
      messages: [{ roleType: 'assistant', content: response }]
    });

    return response;
  }

  async ensureUserAndSession(userId, sessionId) {
    try {
      await this.zepClient.user.get(userId);
    } catch (error) {
      if (error.statusCode === 404) {
        await this.zepClient.user.add({
          userId: userId,
          metadata: { source: 'twilio' }
        });
      }
    }

    try {
      await this.zepClient.memory.getSession(sessionId);
    } catch (error) {
      if (error.statusCode === 404) {
        await this.zepClient.memory.addSession({
          sessionId: sessionId,
          userId: userId
        });
      }
    }
  }

  async generateResponse(message, context) {
    // 这里可以集成您的AI模型
    // 使用context提供的历史信息来生成更智能的回复
    return `基于我们之前的对话，我理解您的消息：${message}`;
  }
}
```

## 最佳实践

### 1. 用户ID管理
- 使用一致的用户标识符（如电话号码、用户ID）
- 为不同渠道的用户建立映射关系

### 2. 会话管理
- 为不同的对话主题创建不同的会话
- 定期清理过期的会话数据

### 3. 记忆优化
- 合理使用 `returnContext` 参数
- 定期检查记忆使用量

### 4. 知识图谱
- 使用结构化的JSON数据格式
- 等待足够的处理时间再进行搜索

### 5. 错误处理
- 始终处理API调用的异常
- 实现重试机制处理临时故障

## 故障排除

### 常见问题

1. **API密钥无效**
   - 检查 `.env` 文件中的 `ZEP_API_KEY`
   - 确认密钥格式正确

2. **知识图谱搜索无结果**
   - 数据处理需要时间，等待几秒后重试
   - 检查添加的数据格式是否正确

3. **会话不存在错误**
   - 确保先创建用户再创建会话
   - 检查用户ID和会话ID的一致性

### 调试技巧

1. 启用详细日志：
```javascript
const zepClient = new ZepClient({
  apiKey: process.env.ZEP_API_KEY,
  debug: true // 启用调试模式
});
```

2. 检查API响应：
```javascript
try {
  const result = await zepClient.memory.get(sessionId);
  console.log('Memory result:', JSON.stringify(result, null, 2));
} catch (error) {
  console.error('Error details:', error.statusCode, error.body);
}
```

## 相关资源

- [Zep Cloud 官方文档](https://help.getzep.com/)
- [Zep Cloud API 参考](https://help.getzep.com/api)
- [Zep GitHub 仓库](https://github.com/getzep/zep)
- [Zep Python SDK](https://github.com/getzep/zep-python)

## 支持

如果您在使用过程中遇到问题：

1. 查看本文档的故障排除部分
2. 运行测试文件验证配置
3. 查看 Zep 官方文档
4. 在 [Zep Discord](https://discord.gg/W8Kw6bsgXQ) 社区寻求帮助
