const { Message, Conversation } = require('../models');
const twilioService = require('../config/twilio');
const logger = require('../config/logger');

class StatusTrackingService {

  /**
   * 获取消息状态
   * @param {string} messageId - 消息ID
   */
  async getMessageStatus(messageId) {
    try {
      const message = await Message.findById(messageId);
      
      if (!message) {
        throw new Error('Message not found');
      }

      return {
        messageId: message._id,
        twilioSid: message.twilioSid,
        status: message.status,
        direction: message.direction,
        from: message.from,
        to: message.to,
        sentAt: message.sentAt,
        deliveredAt: message.deliveredAt,
        readAt: message.readAt,
        errorCode: message.errorCode,
        errorMessage: message.errorMessage,
        price: message.price,
        priceUnit: message.priceUnit,
        createdAt: message.createdAt,
        updatedAt: message.updatedAt,
      };

    } catch (error) {
      logger.error('Failed to get message status:', error);
      throw error;
    }
  }

  /**
   * 从Twilio同步消息状态
   * @param {string} twilioSid - Twilio消息SID
   */
  async syncMessageStatusFromTwilio(twilioSid) {
    try {
      const twilioClient = twilioService.getClient();
      
      // 从Twilio获取消息详情
      const twilioMessage = await twilioClient.messages(twilioSid).fetch();
      
      // 查找本地消息记录
      const message = await Message.findOne({ twilioSid });
      
      if (!message) {
        throw new Error('Message not found in local database');
      }

      // 更新消息状态
      const updateData = {
        status: twilioMessage.status,
        price: twilioMessage.price,
        priceUnit: twilioMessage.priceUnit,
        errorCode: twilioMessage.errorCode,
        errorMessage: twilioMessage.errorMessage,
      };

      // 更新时间戳
      if (twilioMessage.dateSent) {
        updateData.sentAt = new Date(twilioMessage.dateSent);
      }

      await message.updateStatus(twilioMessage.status, updateData);

      logger.info('Message status synced from Twilio', {
        messageId: message._id,
        twilioSid: twilioSid,
        status: twilioMessage.status,
      });

      return await this.getMessageStatus(message._id);

    } catch (error) {
      logger.error('Failed to sync message status from Twilio:', error);
      throw error;
    }
  }

  /**
   * 获取会话状态统计
   * @param {string} conversationId - 会话ID
   */
  async getConversationStatus(conversationId) {
    try {
      const conversation = await Conversation.findOne({ conversationId });
      
      if (!conversation) {
        throw new Error('Conversation not found');
      }

      // 获取消息状态统计
      const statusStats = await Message.aggregate([
        { $match: { conversationId } },
        {
          $group: {
            _id: '$status',
            count: { $sum: 1 },
          }
        }
      ]);

      // 获取方向统计
      const directionStats = await Message.aggregate([
        { $match: { conversationId } },
        {
          $group: {
            _id: '$direction',
            count: { $sum: 1 },
          }
        }
      ]);

      // 获取最新消息
      const latestMessages = await Message.find({ conversationId })
        .sort({ createdAt: -1 })
        .limit(5)
        .select('status direction body createdAt');

      return {
        conversationId: conversation.conversationId,
        status: conversation.status,
        messageCount: conversation.messageCount,
        statusBreakdown: statusStats.reduce((acc, stat) => {
          acc[stat._id] = stat.count;
          return acc;
        }, {}),
        directionBreakdown: directionStats.reduce((acc, stat) => {
          acc[stat._id] = stat.count;
          return acc;
        }, {}),
        latestMessages: latestMessages,
        lastActivityAt: conversation.lastActivityAt,
        startedAt: conversation.startedAt,
      };

    } catch (error) {
      logger.error('Failed to get conversation status:', error);
      throw error;
    }
  }

  /**
   * 获取消息传递报告
   * @param {Object} filters - 过滤条件
   */
  async getDeliveryReport(filters = {}) {
    try {
      const {
        startDate,
        endDate,
        phoneNumber,
        campaignId,
        status,
        direction = 'outbound',
      } = filters;

      // 构建查询条件
      const query = { direction };

      if (startDate || endDate) {
        query.createdAt = {};
        if (startDate) query.createdAt.$gte = new Date(startDate);
        if (endDate) query.createdAt.$lte = new Date(endDate);
      }

      if (phoneNumber) {
        query.$or = [
          { from: phoneNumber },
          { to: phoneNumber }
        ];
      }

      if (campaignId) {
        query.campaignId = campaignId;
      }

      if (status) {
        query.status = status;
      }

      // 获取统计数据
      const totalMessages = await Message.countDocuments(query);
      
      const statusStats = await Message.aggregate([
        { $match: query },
        {
          $group: {
            _id: '$status',
            count: { $sum: 1 },
            totalPrice: { $sum: { $toDouble: '$price' } },
          }
        }
      ]);

      // 计算传递率
      const deliveredCount = statusStats.find(s => s._id === 'delivered')?.count || 0;
      const sentCount = statusStats.find(s => s._id === 'sent')?.count || 0;
      const failedCount = statusStats.find(s => s._id === 'failed')?.count || 0;
      const undeliveredCount = statusStats.find(s => s._id === 'undelivered')?.count || 0;

      const deliveryRate = totalMessages > 0 ? 
        ((deliveredCount + sentCount) / totalMessages * 100).toFixed(2) : 0;
      
      const failureRate = totalMessages > 0 ? 
        ((failedCount + undeliveredCount) / totalMessages * 100).toFixed(2) : 0;

      // 计算总费用
      const totalCost = statusStats.reduce((sum, stat) => sum + (stat.totalPrice || 0), 0);

      return {
        summary: {
          totalMessages,
          deliveryRate: parseFloat(deliveryRate),
          failureRate: parseFloat(failureRate),
          totalCost: totalCost.toFixed(4),
        },
        statusBreakdown: statusStats.reduce((acc, stat) => {
          acc[stat._id] = {
            count: stat.count,
            percentage: ((stat.count / totalMessages) * 100).toFixed(2),
            totalPrice: stat.totalPrice || 0,
          };
          return acc;
        }, {}),
        filters: filters,
        generatedAt: new Date(),
      };

    } catch (error) {
      logger.error('Failed to generate delivery report:', error);
      throw error;
    }
  }

  /**
   * 获取实时状态更新
   * @param {Array} messageIds - 消息ID数组
   */
  async getRealTimeStatus(messageIds) {
    try {
      const messages = await Message.find({
        _id: { $in: messageIds }
      }).select('_id twilioSid status sentAt deliveredAt readAt errorCode errorMessage');

      const statusUpdates = messages.map(message => ({
        messageId: message._id,
        twilioSid: message.twilioSid,
        status: message.status,
        sentAt: message.sentAt,
        deliveredAt: message.deliveredAt,
        readAt: message.readAt,
        hasError: !!(message.errorCode || message.errorMessage),
        errorCode: message.errorCode,
        errorMessage: message.errorMessage,
        lastUpdated: message.updatedAt,
      }));

      return statusUpdates;

    } catch (error) {
      logger.error('Failed to get real-time status:', error);
      throw error;
    }
  }

  /**
   * 批量同步消息状态
   * @param {Array} twilioSids - Twilio SID数组
   */
  async batchSyncStatus(twilioSids) {
    const results = [];
    const errors = [];

    for (const twilioSid of twilioSids) {
      try {
        const result = await this.syncMessageStatusFromTwilio(twilioSid);
        results.push(result);
      } catch (error) {
        errors.push({
          twilioSid,
          error: error.message,
        });
      }
    }

    return {
      success: results.length,
      failed: errors.length,
      results,
      errors,
    };
  }
}

module.exports = new StatusTrackingService();
