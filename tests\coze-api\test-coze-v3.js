// 测试Coze API v3客户端
require('dotenv').config();
const cozeV3Client = require('./coze-api-v3-client');

// 测试消息列表
const testMessages = [
  {
    message: "你好，我是新用户",
    description: "测试问候语"
  },
  {
    message: "你能帮我做什么？",
    description: "测试功能询问"
  },
  {
    message: "今天天气怎么样？",
    description: "测试天气询问"
  },
  {
    message: "请帮我写一首关于春天的诗",
    description: "测试创作能力"
  },
  {
    message: "刚才我问了什么问题？",
    description: "测试上下文记忆"
  }
];

// 测试基本连接
async function testBasicConnection() {
  console.log('🔄 测试Coze API v3基本连接...\n');
  
  try {
    const result = await cozeV3Client.testConnection();
    
    if (result.success) {
      console.log('✅ Coze API v3连接成功!');
      console.log(`   机器人ID: ${cozeV3Client.botId}`);
      console.log(`   测试响应: ${result.reply}`);
      return true;
    } else {
      console.log('❌ Coze API v3连接失败:');
      console.log(`   错误: ${result.error}`);
      return false;
    }
  } catch (error) {
    console.error('❌ 连接测试异常:', error.message);
    return false;
  }
}

// 测试单条消息
async function testSingleMessage(message, userId = 'test_user_001', useStream = false) {
  console.log(`📤 测试${useStream ? '流式' : '非流式'}消息: "${message}"`);
  console.log(`   用户ID: ${userId}`);
  
  try {
    let response;
    if (useStream) {
      response = await cozeV3Client.sendMessageStream(message, userId);
    } else {
      response = await cozeV3Client.sendMessage(message, userId);
    }
    
    console.log('✅ 消息发送成功!');
    console.log(`   回复: ${response.text}`);
    console.log(`   会话ID: ${response.conversation_id || 'N/A'}`);
    console.log(`   聊天ID: ${response.chat_id || 'N/A'}`);
    console.log('');
    
    return {
      success: true,
      message: message,
      reply: response.text,
      conversationId: response.conversation_id,
      chatId: response.chat_id
    };
    
  } catch (error) {
    console.log('❌ 消息发送失败:', error.message);
    console.log('');
    
    return {
      success: false,
      message: message,
      error: error.message
    };
  }
}

// 测试对话上下文
async function testConversationContext() {
  console.log('🔄 测试对话上下文功能...\n');
  
  const testUserId = 'context_test_user';
  const results = [];
  
  for (let i = 0; i < testMessages.length; i++) {
    const testCase = testMessages[i];
    
    console.log(`${i + 1}. ${testCase.description}`);
    
    const result = await testSingleMessage(testCase.message, testUserId, false);
    results.push(result);
    
    // 等待2秒避免请求过快
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
  
  // 分析结果
  console.log('📊 对话上下文测试结果:');
  console.log(`   总测试数: ${results.length}`);
  console.log(`   成功数: ${results.filter(r => r.success).length}`);
  console.log(`   失败数: ${results.filter(r => !r.success).length}`);
  console.log('');
  
  // 显示对话流程
  console.log('💬 完整对话流程:');
  results.forEach((result, index) => {
    if (result.success) {
      console.log(`   用户: ${result.message}`);
      console.log(`   机器人: ${result.reply.substring(0, 100)}${result.reply.length > 100 ? '...' : ''}`);
      console.log('');
    }
  });
  
  return results;
}

// 测试流式对话
async function testStreamConversation() {
  console.log('🌊 测试流式对话功能...\n');
  
  const streamTestMessages = [
    "请详细介绍一下人工智能的发展历史",
    "继续刚才的话题，AI在未来会如何发展？"
  ];
  
  const testUserId = 'stream_test_user';
  const results = [];
  
  for (let i = 0; i < streamTestMessages.length; i++) {
    const message = streamTestMessages[i];
    
    console.log(`${i + 1}. 流式测试: "${message}"`);
    
    const result = await testSingleMessage(message, testUserId, true);
    results.push(result);
    
    // 等待3秒
    await new Promise(resolve => setTimeout(resolve, 3000));
  }
  
  console.log('📊 流式对话测试结果:');
  console.log(`   总测试数: ${results.length}`);
  console.log(`   成功数: ${results.filter(r => r.success).length}`);
  
  return results;
}

// 测试聊天历史获取
async function testChatHistory() {
  console.log('📚 测试聊天历史获取...\n');
  
  try {
    // 先发送一条消息
    const testUserId = 'history_test_user';
    const result = await testSingleMessage('这是一条测试消息，用于测试历史记录', testUserId);
    
    if (result.success && result.chatId) {
      console.log('📖 获取聊天历史...');
      
      const history = await cozeV3Client.getChatHistory(result.chatId, result.conversationId);
      
      console.log('✅ 聊天历史获取成功!');
      console.log(`   消息数量: ${history.data?.length || 0}`);
      
      return { success: true, history: history };
    } else {
      console.log('❌ 无法获取聊天历史，因为消息发送失败');
      return { success: false, error: '消息发送失败' };
    }
    
  } catch (error) {
    console.error('❌ 聊天历史测试失败:', error.message);
    return { success: false, error: error.message };
  }
}

// 主测试函数
async function runAllTests() {
  console.log('🤖 Coze API v3 完整测试套件');
  console.log('=' .repeat(60));
  console.log(`🎯 机器人ID: ${cozeV3Client.botId}`);
  console.log(`🔑 访问令牌: pat_FSEGBG...（已隐藏）`);
  console.log(`🌐 API地址: https://api.coze.cn/v3/chat`);
  console.log('');
  
  const testResults = {
    connection: null,
    singleMessage: null,
    context: null,
    stream: null,
    history: null
  };
  
  try {
    // 1. 基本连接测试
    console.log('1️⃣ 基本连接测试');
    console.log('-'.repeat(30));
    testResults.connection = await testBasicConnection();
    
    if (!testResults.connection) {
      console.log('❌ 基本连接失败，停止后续测试');
      return testResults;
    }
    
    console.log('\n');
    
    // 2. 单条消息测试
    console.log('2️⃣ 单条消息测试');
    console.log('-'.repeat(30));
    testResults.singleMessage = await testSingleMessage('你好，这是一条测试消息');
    console.log('\n');
    
    // 3. 对话上下文测试
    console.log('3️⃣ 对话上下文测试');
    console.log('-'.repeat(30));
    testResults.context = await testConversationContext();
    console.log('\n');
    
    // 4. 流式对话测试
    console.log('4️⃣ 流式对话测试');
    console.log('-'.repeat(30));
    testResults.stream = await testStreamConversation();
    console.log('\n');
    
    // 5. 聊天历史测试
    console.log('5️⃣ 聊天历史测试');
    console.log('-'.repeat(30));
    testResults.history = await testChatHistory();
    
    // 生成测试报告
    generateTestReport(testResults);
    
  } catch (error) {
    console.error('💥 测试过程中发生异常:', error.message);
  }
  
  return testResults;
}

// 生成测试报告
function generateTestReport(results) {
  console.log('\n📋 测试报告汇总');
  console.log('=' .repeat(60));
  
  console.log('✅ 测试结果:');
  console.log(`   基本连接: ${results.connection ? '✅ 通过' : '❌ 失败'}`);
  console.log(`   单条消息: ${results.singleMessage?.success ? '✅ 通过' : '❌ 失败'}`);
  console.log(`   对话上下文: ${results.context?.filter(r => r.success).length || 0}/${results.context?.length || 0} 通过`);
  console.log(`   流式对话: ${results.stream?.filter(r => r.success).length || 0}/${results.stream?.length || 0} 通过`);
  console.log(`   聊天历史: ${results.history?.success ? '✅ 通过' : '❌ 失败'}`);
  
  console.log('\n🎯 总体评估:');
  const overallSuccess = results.connection && results.singleMessage?.success;
  console.log(`   Coze API v3集成: ${overallSuccess ? '✅ 可用' : '❌ 不可用'}`);
  console.log(`   上下文功能: ${results.context?.some(r => r.success) ? '✅ 支持' : '❌ 不支持'}`);
  console.log(`   流式功能: ${results.stream?.some(r => r.success) ? '✅ 支持' : '❌ 不支持'}`);
  
  console.log('\n💡 建议:');
  if (overallSuccess) {
    console.log('   ✅ API集成正常，可以部署到生产环境');
    console.log('   ✅ 建议更新 WhatsApp 机器人使用新的v3客户端');
    console.log('   📝 运行: node whatsapp-coze-bot.js');
  } else {
    console.log('   ❌ 请检查API配置和网络连接');
    console.log('   🔧 确认机器人ID和访问令牌正确');
  }
}

// 主函数
async function main() {
  const command = process.argv[2];
  
  try {
    switch (command) {
      case 'connection':
        await testBasicConnection();
        break;
        
      case 'message':
        const message = process.argv[3] || '你好，这是一条测试消息';
        await testSingleMessage(message);
        break;
        
      case 'stream':
        const streamMessage = process.argv[3] || '请详细介绍一下人工智能';
        await testSingleMessage(streamMessage, 'test_user', true);
        break;
        
      case 'context':
        await testConversationContext();
        break;
        
      case 'history':
        await testChatHistory();
        break;
        
      case 'full':
      default:
        await runAllTests();
        break;
    }
  } catch (error) {
    console.error('💥 测试执行失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = {
  testBasicConnection,
  testSingleMessage,
  testConversationContext,
  testStreamConversation,
  testChatHistory,
  runAllTests
};
