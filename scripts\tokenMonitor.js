/**
 * Slack 令牌监控和管理工具
 * 提供令牌状态检查、刷新、清理等功能
 */

require('dotenv').config();
const SlackAuthDatabase = require('../src/database/slackAuth');
const TokenManager = require('../src/services/tokenManager');
const axios = require('axios');

class SlackTokenMonitor {
  constructor() {
    this.db = new SlackAuthDatabase();
    this.tokenManager = new TokenManager();
  }

  /**
   * 显示帮助信息
   */
  showHelp() {
    console.log(`
🔧 Slack 令牌监控和管理工具

用法: node scripts/tokenMonitor.js <命令> [参数]

命令:
  status                    - 显示所有工作区的令牌状态
  check <team_id>          - 检查特定团队的令牌状态
  refresh <team_id>        - 强制刷新特定团队的令牌
  test <team_id>           - 测试特定团队的令牌有效性
  clean                    - 清理无效的令牌
  reauth <team_id>         - 生成重新授权链接
  help                     - 显示此帮助信息

示例:
  node scripts/tokenMonitor.js status
  node scripts/tokenMonitor.js check T0983BEJT4J
  node scripts/tokenMonitor.js refresh T0983BEJT4J
  node scripts/tokenMonitor.js test T0983BEJT4J
    `);
  }

  /**
   * 显示所有工作区的令牌状态
   */
  async showStatus() {
    console.log('📊 Slack 工作区令牌状态');
    console.log('=' .repeat(60));

    try {
      const workspaces = await this.db.getAllWorkspaces();
      
      if (workspaces.length === 0) {
        console.log('⚠️ 没有找到任何工作区');
        return;
      }

      for (const workspace of workspaces) {
        await this.displayWorkspaceStatus(workspace);
        console.log('-' .repeat(40));
      }

      // 显示汇总信息
      const validCount = workspaces.filter(w => this.isTokenValid(w)).length;
      const expiredCount = workspaces.filter(w => this.isTokenExpired(w)).length;
      const invalidCount = workspaces.length - validCount - expiredCount;

      console.log('\n📈 汇总统计:');
      console.log(`   总工作区: ${workspaces.length}`);
      console.log(`   有效令牌: ${validCount}`);
      console.log(`   过期令牌: ${expiredCount}`);
      console.log(`   无效令牌: ${invalidCount}`);

    } catch (error) {
      console.error('❌ 获取工作区状态失败:', error.message);
    }
  }

  /**
   * 显示单个工作区的状态
   */
  async displayWorkspaceStatus(workspace) {
    const status = this.getTokenStatus(workspace);
    const statusIcon = this.getStatusIcon(status);
    
    console.log(`\n${statusIcon} 工作区: ${workspace.team_name} (${workspace.team_id})`);
    console.log(`   Bot用户ID: ${workspace.bot_user_id}`);
    console.log(`   授权用户: ${workspace.authed_user_id}`);
    console.log(`   令牌状态: ${status}`);
    
    if (workspace.bot_token_expires_at) {
      const expiresAt = new Date(workspace.bot_token_expires_at);
      const now = new Date();
      const timeLeft = expiresAt - now;
      
      console.log(`   过期时间: ${expiresAt.toISOString()}`);
      
      if (timeLeft > 0) {
        const hoursLeft = Math.floor(timeLeft / (1000 * 60 * 60));
        const minutesLeft = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
        console.log(`   剩余时间: ${hoursLeft}小时${minutesLeft}分钟`);
      } else {
        const hoursExpired = Math.floor(Math.abs(timeLeft) / (1000 * 60 * 60));
        console.log(`   已过期: ${hoursExpired}小时前`);
      }
    }
    
    console.log(`   刷新令牌: ${workspace.bot_refresh_token ? '有' : '无'}`);
    console.log(`   安装时间: ${workspace.installed_at}`);
  }

  /**
   * 检查特定团队的令牌状态
   */
  async checkTeam(teamId) {
    console.log(`🔍 检查团队 ${teamId} 的令牌状态`);
    console.log('=' .repeat(50));

    try {
      const workspace = await this.db.getWorkspaceByTeamId(teamId);
      
      if (!workspace) {
        console.log(`❌ 未找到团队 ${teamId} 的信息`);
        return;
      }

      await this.displayWorkspaceStatus(workspace);
      
      // 测试令牌有效性
      if (workspace.bot_user_access_token) {
        console.log('\n🧪 测试令牌有效性...');
        const isValid = await this.testToken(workspace.bot_user_access_token);
        console.log(`   API测试结果: ${isValid ? '✅ 有效' : '❌ 无效'}`);
      }

    } catch (error) {
      console.error(`❌ 检查团队 ${teamId} 失败:`, error.message);
    }
  }

  /**
   * 刷新特定团队的令牌
   */
  async refreshTeam(teamId) {
    console.log(`🔄 刷新团队 ${teamId} 的令牌`);
    console.log('=' .repeat(50));

    try {
      const newToken = await this.tokenManager.getBotToken(teamId);
      
      if (newToken) {
        console.log('✅ 令牌刷新成功');
        console.log(`   新令牌: ${this.maskToken(newToken)}`);
        
        // 重新检查状态
        await this.checkTeam(teamId);
      } else {
        console.log('❌ 令牌刷新失败');
      }

    } catch (error) {
      console.error(`❌ 刷新团队 ${teamId} 令牌失败:`, error.message);
    }
  }

  /**
   * 测试令牌有效性
   */
  async testToken(token) {
    try {
      const response = await axios.post('https://slack.com/api/auth.test', {}, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      return response.data.ok;
    } catch (error) {
      return false;
    }
  }

  /**
   * 清理无效令牌
   */
  async cleanInvalidTokens() {
    console.log('🧹 清理无效令牌');
    console.log('=' .repeat(50));

    try {
      const workspaces = await this.db.getAllWorkspaces();
      let cleanedCount = 0;

      for (const workspace of workspaces) {
        if (workspace.bot_user_access_token) {
          const isValid = await this.testToken(workspace.bot_user_access_token);
          
          if (!isValid) {
            console.log(`🗑️ 清理工作区 ${workspace.team_name} 的无效令牌`);
            
            // 这里可以添加清理逻辑，比如标记为无效或删除
            // await this.db.markTokenInvalid(workspace.team_id);
            cleanedCount++;
          }
        }
      }

      console.log(`✅ 清理完成，处理了 ${cleanedCount} 个无效令牌`);

    } catch (error) {
      console.error('❌ 清理无效令牌失败:', error.message);
    }
  }

  /**
   * 生成重新授权链接
   */
  generateReauthLink(teamId) {
    console.log(`🔗 生成团队 ${teamId} 的重新授权链接`);
    console.log('=' .repeat(50));

    const baseUrl = process.env.SLACK_REDIRECT_URI?.replace('/slack/oauth/callback', '');
    if (!baseUrl) {
      console.log('❌ 无法生成重新授权链接，SLACK_REDIRECT_URI 未配置');
      return;
    }

    const authUrl = `${baseUrl}/slack/oauth/start`;
    console.log(`🔗 重新授权链接: ${authUrl}`);
    console.log('💡 请访问上述链接重新授权工作区');
  }

  /**
   * 获取令牌状态
   */
  getTokenStatus(workspace) {
    if (!workspace.bot_user_access_token) {
      return '无令牌';
    }

    if (!workspace.bot_token_expires_at) {
      return '永久有效';
    }

    const now = Date.now();
    if (workspace.bot_token_expires_at <= now) {
      return workspace.bot_refresh_token ? '已过期(可刷新)' : '已过期(不可刷新)';
    }

    return '有效';
  }

  /**
   * 获取状态图标
   */
  getStatusIcon(status) {
    switch (status) {
      case '有效':
      case '永久有效':
        return '✅';
      case '已过期(可刷新)':
        return '⚠️';
      case '已过期(不可刷新)':
      case '无令牌':
        return '❌';
      default:
        return 'ℹ️';
    }
  }

  /**
   * 检查令牌是否有效
   */
  isTokenValid(workspace) {
    if (!workspace.bot_user_access_token) return false;
    if (!workspace.bot_token_expires_at) return true;
    return workspace.bot_token_expires_at > Date.now();
  }

  /**
   * 检查令牌是否过期
   */
  isTokenExpired(workspace) {
    if (!workspace.bot_user_access_token) return false;
    if (!workspace.bot_token_expires_at) return false;
    return workspace.bot_token_expires_at <= Date.now();
  }

  /**
   * 掩码令牌
   */
  maskToken(token) {
    if (!token || token.length <= 10) return token;
    return token.substring(0, 10) + '...' + token.substring(token.length - 4);
  }
}

// 命令行处理
async function main() {
  const monitor = new SlackTokenMonitor();
  const args = process.argv.slice(2);
  const command = args[0];
  const param = args[1];

  switch (command) {
    case 'status':
      await monitor.showStatus();
      break;
    
    case 'check':
      if (!param) {
        console.log('❌ 请提供团队ID');
        console.log('用法: node scripts/tokenMonitor.js check <team_id>');
        break;
      }
      await monitor.checkTeam(param);
      break;
    
    case 'refresh':
      if (!param) {
        console.log('❌ 请提供团队ID');
        console.log('用法: node scripts/tokenMonitor.js refresh <team_id>');
        break;
      }
      await monitor.refreshTeam(param);
      break;
    
    case 'test':
      if (!param) {
        console.log('❌ 请提供团队ID');
        console.log('用法: node scripts/tokenMonitor.js test <team_id>');
        break;
      }
      await monitor.checkTeam(param);
      break;
    
    case 'clean':
      await monitor.cleanInvalidTokens();
      break;
    
    case 'reauth':
      if (!param) {
        console.log('❌ 请提供团队ID');
        console.log('用法: node scripts/tokenMonitor.js reauth <team_id>');
        break;
      }
      monitor.generateReauthLink(param);
      break;
    
    case 'help':
    default:
      monitor.showHelp();
      break;
  }

  process.exit(0);
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = SlackTokenMonitor;
