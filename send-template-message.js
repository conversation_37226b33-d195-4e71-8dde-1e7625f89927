// 使用Twilio内容模板发送WhatsApp消息
require('dotenv').config();

async function sendTemplateMessage() {
  console.log('📱 使用内容模板发送WhatsApp消息...\n');

  try {
    // 使用环境变量中的凭据
    const accountSid = process.env.TWILIO_ACCOUNT_SID;
    const authToken = process.env.TWILIO_AUTH_TOKEN;
    const client = require('twilio')(accountSid, authToken);

    console.log('📋 消息配置:');
    console.log('   Account SID:', accountSid);
    console.log('   发送方:', 'whatsapp:+***********');
    console.log('   接收方:', 'whatsapp:+***********');
    console.log('   内容模板ID:', 'HXb5b62575e6e4ff6129ad7c8efe1f983e');
    console.log('   模板变量:', '{"1":"12/1","2":"3pm"}');
    console.log('');

    console.log('🚀 正在发送模板消息...');

    const message = await client.messages.create({
      from: 'whatsapp:+***********',
      contentSid: 'HXb5b62575e6e4ff6129ad7c8efe1f983e',
      contentVariables: '{"1":"12/1","2":"3pm"}',
      to: 'whatsapp:+***********'
    });

    console.log('✅ 模板消息发送成功!');
    console.log('   消息SID:', message.sid);
    console.log('   状态:', message.status);
    console.log('   发送时间:', new Date().toISOString());
    console.log('');

    // 等待几秒后查询状态
    console.log('⏳ 等待状态更新...');
    setTimeout(async () => {
      try {
        const updatedMessage = await client.messages(message.sid).fetch();
        console.log('📊 消息状态更新:');
        console.log('   当前状态:', updatedMessage.status);
        console.log('   错误代码:', updatedMessage.errorCode || 'None');
        console.log('   错误信息:', updatedMessage.errorMessage || 'None');
        console.log('   价格:', updatedMessage.price || 'N/A', updatedMessage.priceUnit || '');
        
        // 状态说明
        const statusDescriptions = {
          'queued': '📤 消息已排队等待发送',
          'sending': '🔄 消息正在发送中',
          'sent': '✅ 消息已发送到WhatsApp',
          'delivered': '📱 消息已送达到设备',
          'read': '👀 消息已被阅读',
          'failed': '❌ 消息发送失败',
          'undelivered': '⚠️ 消息未能送达'
        };

        console.log('   状态说明:', statusDescriptions[updatedMessage.status] || '未知状态');

      } catch (statusError) {
        console.log('⚠️ 状态查询失败:', statusError.message);
      }
    }, 5000);

    return message;

  } catch (error) {
    console.error('❌ 模板消息发送失败:', error.message);
    
    // 详细错误分析
    if (error.code === 63016) {
      console.error('\n📋 可能的原因:');
      console.error('   1. 接收号码未加入WhatsApp沙盒');
      console.error('   2. 内容模板ID无效或未批准');
      console.error('   3. 模板变量格式错误');
    } else if (error.code === 21211) {
      console.error('   错误: 无效的电话号码格式');
    } else if (error.code === 20003) {
      console.error('   错误: Twilio认证失败');
    } else {
      console.error('   错误代码:', error.code);
      console.error('   详细信息:', error.moreInfo || 'N/A');
    }

    throw error;
  }
}

// 发送自定义模板消息
async function sendCustomTemplateMessage(options) {
  const {
    to,
    contentSid,
    contentVariables = '{}',
    from = 'whatsapp:+***********'
  } = options;

  console.log('📱 发送自定义模板消息...');
  console.log('   接收方:', to);
  console.log('   模板ID:', contentSid);
  console.log('   变量:', contentVariables);
  console.log('');

  try {
    const accountSid = process.env.TWILIO_ACCOUNT_SID;
    const authToken = process.env.TWILIO_AUTH_TOKEN;
    const client = require('twilio')(accountSid, authToken);

    const message = await client.messages.create({
      from: from,
      contentSid: contentSid,
      contentVariables: contentVariables,
      to: to
    });

    console.log('✅ 自定义模板消息发送成功!');
    console.log('   消息SID:', message.sid);
    
    return message;

  } catch (error) {
    console.error('❌ 自定义模板消息发送失败:', error.message);
    throw error;
  }
}

// 获取可用的内容模板
async function listContentTemplates() {
  console.log('📋 获取可用的内容模板...\n');

  try {
    const accountSid = process.env.TWILIO_ACCOUNT_SID;
    const authToken = process.env.TWILIO_AUTH_TOKEN;
    const client = require('twilio')(accountSid, authToken);

    const contents = await client.content.contents.list({ limit: 20 });

    console.log('📄 可用的内容模板:');
    console.log('   总数:', contents.length);
    console.log('');

    contents.forEach((content, index) => {
      console.log(`   ${index + 1}. ${content.friendlyName || 'Unnamed'}`);
      console.log(`      SID: ${content.sid}`);
      console.log(`      语言: ${content.language || 'N/A'}`);
      console.log(`      状态: ${content.approvalRequests || 'N/A'}`);
      console.log('');
    });

    return contents;

  } catch (error) {
    console.error('❌ 获取内容模板失败:', error.message);
    throw error;
  }
}

// 主函数
async function main() {
  console.log('📱 Twilio WhatsApp模板消息工具');
  console.log('=' .repeat(50));

  const command = process.argv[2];

  try {
    switch (command) {
      case 'send':
        // 发送默认模板消息
        await sendTemplateMessage();
        break;

      case 'custom':
        // 发送自定义模板消息
        const to = process.argv[3];
        const contentSid = process.argv[4];
        const variables = process.argv[5] || '{}';

        if (!to || !contentSid) {
          console.log('❌ 请提供接收号码和模板ID');
          console.log('\n使用方法:');
          console.log('   node send-template-message.js custom whatsapp:+1234567890 HX123456 \'{"1":"value1","2":"value2"}\'');
          return;
        }

        await sendCustomTemplateMessage({
          to: to,
          contentSid: contentSid,
          contentVariables: variables
        });
        break;

      case 'list':
        // 列出可用模板
        await listContentTemplates();
        break;

      default:
        console.log('📋 可用命令:');
        console.log('   send   - 发送默认模板消息');
        console.log('   custom - 发送自定义模板消息');
        console.log('   list   - 列出可用的内容模板');
        console.log('');
        console.log('📝 示例:');
        console.log('   node send-template-message.js send');
        console.log('   node send-template-message.js list');
        console.log('   node send-template-message.js custom whatsapp:+1234567890 HX123456 \'{"1":"12/1","2":"3pm"}\'');
    }

  } catch (error) {
    console.log('\n💥 执行过程中发生错误:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = {
  sendTemplateMessage,
  sendCustomTemplateMessage,
  listContentTemplates
};
