// 单独测试Coze接口
const coze = require('./coze-api-v3-client');

async function testSingleMessage() {
  console.log('🧪 单独测试Coze接口');
  console.log('发送消息: 你好');
  console.log('');
  
  try {
    const result = await coze.sendMessage('你好', 'test_user_single');
    
    console.log('📥 完整返回结果:');
    console.log(JSON.stringify(result, null, 2));
    console.log('');
    
    console.log('📝 提取的回复文本:');
    console.log(result.text);
    console.log('');
    
    console.log('✅ 测试完成');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.error('错误详情:', error);
  }
}

testSingleMessage();
