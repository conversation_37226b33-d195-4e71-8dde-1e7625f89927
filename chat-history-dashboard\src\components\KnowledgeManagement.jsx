import React, { useState, useEffect } from 'react';
import { 
  Brain, 
  Search, 
  RefreshCw, 
  Database, 
  AlertCircle,
  CheckCircle,
  Trash2,
  Upload,
  Download,
  Edit,
  Save,
  X,
  Plus
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { Input } from './ui/input';
import { apiService } from '../services/api';

export function KnowledgeManagement() {
  const [knowledgeData, setKnowledgeData] = useState([]);
  const [knowledgeStatus, setKnowledgeStatus] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState(null);
  const [searchLoading, setSearchLoading] = useState(false);
  const [editingItem, setEditingItem] = useState(null);
  const [editContent, setEditContent] = useState('');

  useEffect(() => {
    loadKnowledgeData();
    loadKnowledgeStatus();
  }, []);

  const loadKnowledgeData = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiService.getKnowledgeData();
      setKnowledgeData(response.data.edges || []);
    } catch (err) {
      setError(err.message);
      console.error('加载知识图谱数据失败:', err);
    } finally {
      setLoading(false);
    }
  };

  const loadKnowledgeStatus = async () => {
    try {
      const response = await apiService.getKnowledgeStatus();
      setKnowledgeStatus(response.data);
    } catch (err) {
      console.error('获取知识图谱状态失败:', err);
    }
  };

  const handleSearch = async (e) => {
    e.preventDefault();
    if (!searchQuery.trim()) return;

    try {
      setSearchLoading(true);
      const response = await apiService.searchKnowledge(searchQuery.trim());
      setSearchResults(response.data);
    } catch (err) {
      setError(err.message);
      console.error('搜索知识图谱失败:', err);
    } finally {
      setSearchLoading(false);
    }
  };

  const handleReset = async () => {
    if (!confirm('确定要重置知识图谱吗？这将删除所有现有数据并重新初始化。')) {
      return;
    }

    try {
      setLoading(true);
      await apiService.resetKnowledge();
      await loadKnowledgeData();
      await loadKnowledgeStatus();
      alert('知识图谱重置成功！');
    } catch (err) {
      setError(err.message);
      console.error('重置知识图谱失败:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleUpdate = async () => {
    try {
      setLoading(true);
      await apiService.updateKnowledge();
      await loadKnowledgeData();
      await loadKnowledgeStatus();
      alert('知识图谱更新成功！');
    } catch (err) {
      setError(err.message);
      console.error('更新知识图谱失败:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (item, index) => {
    setEditingItem(index);
    setEditContent(item.fact);
  };

  const handleSaveEdit = () => {
    // 这里可以添加保存编辑的逻辑
    // 目前只是演示，实际需要后端支持
    alert('编辑功能正在开发中，将在下个版本支持！');
    setEditingItem(null);
    setEditContent('');
  };

  const handleCancelEdit = () => {
    setEditingItem(null);
    setEditContent('');
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'healthy':
        return 'text-green-600 bg-green-100';
      case 'error':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle className="h-4 w-4" />;
      case 'error':
        return <AlertCircle className="h-4 w-4" />;
      default:
        return <Database className="h-4 w-4" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="flex items-center space-x-3">
          <Brain className="h-8 w-8 text-blue-600" />
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">知识图谱管理</h1>
            <p className="text-sm sm:text-base text-gray-600">管理和维护智能客服的知识库</p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline" onClick={loadKnowledgeData} disabled={loading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            {loading ? '加载中...' : '刷新'}
          </Button>
        </div>
      </div>

      {/* 状态卡片 */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
        <Card className="w-full">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">知识图谱状态</CardTitle>
            {knowledgeStatus && getStatusIcon(knowledgeStatus.status)}
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2 mb-2">
              {knowledgeStatus ? (
                <Badge className={getStatusColor(knowledgeStatus.status)}>
                  {knowledgeStatus.status === 'healthy' ? '正常' : '异常'}
                </Badge>
              ) : (
                <Badge variant="secondary">检查中...</Badge>
              )}
            </div>
            <p className="text-xs text-gray-500">
              {knowledgeStatus?.timestamp ?
                `更新时间: ${new Date(knowledgeStatus.timestamp).toLocaleString()}` :
                '正在获取状态...'
              }
            </p>
          </CardContent>
        </Card>

        <Card className="w-full">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">知识条目数量</CardTitle>
            <Database className="h-4 w-4 text-gray-400" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">{knowledgeData.length}</div>
            <p className="text-xs text-gray-500">
              已存储的知识条目
            </p>
          </CardContent>
        </Card>

        <Card className="w-full">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">操作</CardTitle>
            <Upload className="h-4 w-4 text-gray-400" />
          </CardHeader>
          <CardContent className="space-y-2">
            <Button
              size="sm"
              className="w-full bg-blue-600 hover:bg-blue-700 text-white"
              onClick={handleUpdate}
              disabled={loading}
            >
              <Upload className="h-3 w-3 mr-1" />
              {loading ? '更新中...' : '更新到Zep'}
            </Button>
            <Button
              size="sm"
              variant="destructive"
              className="w-full"
              onClick={handleReset}
              disabled={loading}
            >
              <Trash2 className="h-3 w-3 mr-1" />
              重置知识库
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* 搜索功能 */}
      <Card className="w-full">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Search className="h-5 w-5 text-blue-600" />
            <span>知识搜索测试</span>
          </CardTitle>
          <CardDescription>测试知识图谱的搜索功能</CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSearch} className="space-y-4">
            <div className="flex flex-col sm:flex-row gap-2">
              <Input
                type="text"
                placeholder="输入搜索关键词..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="flex-1"
              />
              <Button
                type="submit"
                disabled={searchLoading}
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                <Search className={`h-4 w-4 mr-2 ${searchLoading ? 'animate-spin' : ''}`} />
                {searchLoading ? '搜索中...' : '搜索'}
              </Button>
            </div>
          </form>

          {searchResults && (
            <div className="mt-4 p-4 bg-gray-50 rounded-lg border">
              <h4 className="font-medium mb-3 text-gray-900">搜索结果:</h4>
              {searchResults.found ? (
                <div className="space-y-3">
                  <div className="flex flex-wrap gap-2">
                    <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                      ✓ 找到结果: {searchResults.count || 0}
                    </Badge>
                    <Badge variant="secondary" className="bg-blue-50 text-blue-700">
                      搜索方法: {searchResults.searchMethod}
                    </Badge>
                  </div>
                  <div className="bg-white p-4 rounded-md border shadow-sm">
                    <div className="text-sm text-gray-700 whitespace-pre-wrap leading-relaxed">
                      {searchResults.knowledge}
                    </div>
                  </div>
                </div>
              ) : (
                <div className="flex items-center space-x-2 text-gray-500">
                  <AlertCircle className="h-4 w-4" />
                  <span>未找到相关知识</span>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* 错误提示 */}
      {error && (
        <Card className="border-red-200 bg-red-50 w-full">
          <CardContent className="pt-6">
            <div className="flex items-start space-x-3 text-red-600">
              <AlertCircle className="h-5 w-5 mt-0.5 flex-shrink-0" />
              <div>
                <p className="font-medium">操作失败</p>
                <p className="text-sm mt-1">{error}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 知识条目列表 */}
      <Card className="w-full">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Database className="h-5 w-5 text-blue-600" />
            <span>知识条目列表</span>
          </CardTitle>
          <CardDescription>
            当前知识图谱中的所有条目 ({knowledgeData.length} 条)
          </CardDescription>
        </CardHeader>
        <CardContent className="p-6">
          {loading ? (
            <div className="space-y-4">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="animate-pulse border rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-3">
                    <div className="h-4 bg-gray-200 rounded w-16"></div>
                    <div className="h-4 bg-gray-200 rounded w-20"></div>
                  </div>
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-16 bg-gray-200 rounded"></div>
                </div>
              ))}
            </div>
          ) : knowledgeData.length === 0 ? (
            <div className="text-center text-gray-500 py-12">
              <Brain className="h-16 w-16 mx-auto mb-4 text-gray-300" />
              <p className="text-lg font-medium text-gray-700 mb-2">暂无知识条目</p>
              <p className="text-sm text-gray-500 mb-4">点击"更新到Zep"按钮初始化知识库</p>
              <Button
                onClick={handleUpdate}
                disabled={loading}
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                <Upload className="h-4 w-4 mr-2" />
                立即初始化
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {knowledgeData.map((item, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 hover:border-gray-300 transition-colors">
                  <div className="flex items-start justify-between gap-4">
                    <div className="flex-1 min-w-0">
                      <div className="flex flex-wrap items-center gap-2 mb-3">
                        <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                          条目 {index + 1}
                        </Badge>
                        {item.source && (
                          <Badge variant="secondary" className="bg-gray-100 text-gray-700">
                            {item.source}
                          </Badge>
                        )}
                      </div>
                      {editingItem === index ? (
                        <div className="space-y-3">
                          <textarea
                            value={editContent}
                            onChange={(e) => setEditContent(e.target.value)}
                            className="w-full p-3 border border-gray-300 rounded-md resize-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            rows={6}
                            placeholder="编辑知识内容..."
                          />
                          <div className="flex space-x-2">
                            <Button
                              size="sm"
                              onClick={handleSaveEdit}
                              className="bg-green-600 hover:bg-green-700 text-white"
                            >
                              <Save className="h-3 w-3 mr-1" />
                              保存
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={handleCancelEdit}
                              className="border-gray-300"
                            >
                              <X className="h-3 w-3 mr-1" />
                              取消
                            </Button>
                          </div>
                        </div>
                      ) : (
                        <div className="bg-gray-50 rounded-md p-3 border">
                          <p className="text-gray-800 whitespace-pre-wrap leading-relaxed text-sm">
                            {item.fact}
                          </p>
                        </div>
                      )}
                    </div>
                    {editingItem !== index && (
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => handleEdit(item, index)}
                        className="flex-shrink-0 hover:bg-blue-50 hover:text-blue-600"
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
