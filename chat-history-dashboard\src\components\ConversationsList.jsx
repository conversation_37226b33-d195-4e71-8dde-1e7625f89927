import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { 
  MessageSquare, 
  Phone, 
  Hash, 
  MessageCircle, 
  Filter,
  RefreshCw,
  ChevronRight,
  Clock
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { Input } from './ui/input';
import { apiService } from '../services/api';
import { formatDistanceToNow } from 'date-fns';
import { zhCN } from 'date-fns/locale';

export function ConversationsList() {
  const [conversations, setConversations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [filters, setFilters] = useState({
    platform: '',
    page: 1,
    limit: 20
  });

  useEffect(() => {
    loadConversations();
  }, [filters]);

  const loadConversations = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiService.getConversations(filters);
      setConversations(response.data || []);
    } catch (err) {
      setError(err.message);
      console.error('加载对话列表失败:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = () => {
    loadConversations();
  };

  const handlePlatformFilter = (platform) => {
    setFilters(prev => ({
      ...prev,
      platform: prev.platform === platform ? '' : platform,
      page: 1
    }));
  };

  const getPlatformIcon = (platform) => {
    switch (platform) {
      case 'whatsapp':
        return <Phone className="h-4 w-4" />;
      case 'slack':
        return <Hash className="h-4 w-4" />;
      case 'line':
        return <MessageCircle className="h-4 w-4" />;
      default:
        return <MessageSquare className="h-4 w-4" />;
    }
  };

  const getPlatformColor = (platform) => {
    switch (platform) {
      case 'whatsapp':
        return 'bg-green-100 text-green-800';
      case 'slack':
        return 'bg-purple-100 text-purple-800';
      case 'line':
        return 'bg-green-50 text-green-700';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatLastMessageTime = (timestamp) => {
    if (!timestamp) return '未知时间';
    try {
      return formatDistanceToNow(new Date(timestamp), { 
        addSuffix: true, 
        locale: zhCN 
      });
    } catch {
      return '时间格式错误';
    }
  };

  const truncateMessage = (message, maxLength = 60) => {
    if (!message) return '暂无消息';
    return message.length > maxLength ? message.substring(0, maxLength) + '...' : message;
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold text-gray-900">对话列表</h1>
          <Button disabled>
            <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
            加载中...
          </Button>
        </div>
        <div className="grid gap-4">
          {[...Array(5)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
                  <div className="flex-1 space-y-2">
                    <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                    <div className="h-3 bg-gray-200 rounded w-3/4"></div>
                  </div>
                  <div className="h-4 bg-gray-200 rounded w-16"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">对话列表</h1>
        <div className="flex items-center space-x-3">
          <Button variant="outline" onClick={handleRefresh}>
            <RefreshCw className="h-4 w-4 mr-2" />
            刷新
          </Button>
        </div>
      </div>

      {/* 过滤器 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Filter className="h-5 w-5 mr-2" />
            筛选条件
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            <Button
              variant={filters.platform === '' ? 'default' : 'outline'}
              size="sm"
              onClick={() => handlePlatformFilter('')}
            >
              全部平台
            </Button>
            {['whatsapp', 'slack', 'line'].map((platform) => (
              <Button
                key={platform}
                variant={filters.platform === platform ? 'default' : 'outline'}
                size="sm"
                onClick={() => handlePlatformFilter(platform)}
                className="capitalize"
              >
                {getPlatformIcon(platform)}
                <span className="ml-2">{platform}</span>
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* 错误提示 */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="text-red-600">
              加载对话列表失败: {error}
            </div>
          </CardContent>
        </Card>
      )}

      {/* 对话列表 */}
      <div className="space-y-4">
        {conversations.length === 0 ? (
          <Card>
            <CardContent className="pt-6">
              <div className="text-center text-gray-500 py-8">
                <MessageSquare className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p className="text-lg font-medium">暂无对话记录</p>
                <p className="text-sm">当有用户发送消息时，对话记录将显示在这里</p>
              </div>
            </CardContent>
          </Card>
        ) : (
          conversations.map((conversation) => (
            <Card key={conversation.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <Link 
                  to={`/conversations/${conversation.id}`}
                  className="flex items-center space-x-4 group"
                >
                  {/* 平台图标 */}
                  <div className={`p-3 rounded-full ${getPlatformColor(conversation.platform)}`}>
                    {getPlatformIcon(conversation.platform)}
                  </div>

                  {/* 对话信息 */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-1">
                      <h3 className="text-lg font-medium text-gray-900 truncate">
                        {conversation.user?.displayName || conversation.user?.platformUserId || '未知用户'}
                      </h3>
                      <Badge variant="outline" className="capitalize">
                        {conversation.platform}
                      </Badge>
                    </div>
                    
                    <p className="text-sm text-gray-600 mb-2">
                      {conversation.user?.phoneNumber && (
                        <span className="mr-4">📱 {conversation.user.phoneNumber}</span>
                      )}
                      {conversation.channelId && (
                        <span>🏷️ {conversation.channelId}</span>
                      )}
                    </p>

                    <p className="text-sm text-gray-500 truncate">
                      {truncateMessage(conversation.lastMessage)}
                    </p>
                  </div>

                  {/* 统计信息 */}
                  <div className="text-right space-y-1">
                    <div className="flex items-center text-sm text-gray-500">
                      <Clock className="h-4 w-4 mr-1" />
                      {formatLastMessageTime(conversation.lastMessageTime)}
                    </div>
                    <div className="text-sm text-gray-600">
                      {conversation.messageCount || 0} 条消息
                    </div>
                  </div>

                  {/* 箭头图标 */}
                  <ChevronRight className="h-5 w-5 text-gray-400 group-hover:text-gray-600" />
                </Link>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* 分页 */}
      {conversations.length > 0 && (
        <div className="flex justify-center">
          <Button 
            variant="outline"
            onClick={() => setFilters(prev => ({ ...prev, page: prev.page + 1 }))}
          >
            加载更多
          </Button>
        </div>
      )}
    </div>
  );
}
