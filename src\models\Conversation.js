const mongoose = require('mongoose');

const conversationSchema = new mongoose.Schema({
  // 会话唯一标识
  conversationId: {
    type: String,
    required: true,
  },

  // 参与者
  participants: [{
    phoneNumber: {
      type: String,
      required: true,
    },
    role: {
      type: String,
      enum: ['business', 'customer'],
      required: true,
    },
    name: {
      type: String,
    },
  }],

  // 会话状态
  status: {
    type: String,
    enum: ['active', 'closed', 'archived'],
    default: 'active',
  },

  // 最后一条消息
  lastMessage: {
    messageId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Message',
    },
    content: {
      type: String,
    },
    timestamp: {
      type: Date,
    },
    direction: {
      type: String,
      enum: ['outbound', 'inbound'],
    },
  },

  // 消息统计
  messageCount: {
    total: {
      type: Number,
      default: 0,
    },
    outbound: {
      type: Number,
      default: 0,
    },
    inbound: {
      type: Number,
      default: 0,
    },
    unread: {
      type: Number,
      default: 0,
    },
  },

  // 业务信息
  businessInfo: {
    campaignId: {
      type: String,
    },
    source: {
      type: String, // 来源：website, app, api等
    },
    tags: [{
      type: String,
    }],
  },

  // 客户信息
  customerInfo: {
    name: {
      type: String,
    },
    email: {
      type: String,
    },
    customerId: {
      type: String,
    },
    metadata: {
      type: mongoose.Schema.Types.Mixed,
      default: {},
    },
  },

  // 时间信息
  startedAt: {
    type: Date,
    default: Date.now,
  },
  lastActivityAt: {
    type: Date,
    default: Date.now,
  },
  closedAt: {
    type: Date,
  },

}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// 索引
conversationSchema.index({ conversationId: 1 }, { unique: true });
conversationSchema.index({ 'participants.phoneNumber': 1 });
conversationSchema.index({ status: 1 });
conversationSchema.index({ lastActivityAt: -1 });
conversationSchema.index({ 'businessInfo.campaignId': 1 });

// 虚拟字段
conversationSchema.virtual('isActive').get(function() {
  return this.status === 'active';
});

conversationSchema.virtual('duration').get(function() {
  const endTime = this.closedAt || new Date();
  return endTime - this.startedAt;
});

// 实例方法
conversationSchema.methods.updateLastMessage = function(message) {
  this.lastMessage = {
    messageId: message._id,
    content: message.body || '[Media]',
    timestamp: message.createdAt,
    direction: message.direction,
  };
  this.lastActivityAt = new Date();
  
  // 更新消息计数
  this.messageCount.total += 1;
  if (message.direction === 'outbound') {
    this.messageCount.outbound += 1;
  } else {
    this.messageCount.inbound += 1;
    this.messageCount.unread += 1;
  }
  
  return this.save();
};

conversationSchema.methods.markAsRead = function() {
  this.messageCount.unread = 0;
  return this.save();
};

conversationSchema.methods.close = function() {
  this.status = 'closed';
  this.closedAt = new Date();
  return this.save();
};

// 静态方法
conversationSchema.statics.findByPhoneNumber = function(phoneNumber) {
  return this.find({
    'participants.phoneNumber': phoneNumber
  }).sort({ lastActivityAt: -1 });
};

conversationSchema.statics.findActive = function() {
  return this.find({ status: 'active' }).sort({ lastActivityAt: -1 });
};

module.exports = mongoose.model('Conversation', conversationSchema);
