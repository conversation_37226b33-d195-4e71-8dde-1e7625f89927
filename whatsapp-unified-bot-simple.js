// WhatsApp统一智能机器人 - 简化版本（确保webhook正常工作）
require('dotenv').config();
const express = require('express');
const twilio = require('twilio');

// 创建Express应用
const app = express();
const PORT = process.env.WEBHOOK_PORT || 3002;

// 解析表单数据（Twilio发送的是form-encoded数据）
app.use(express.urlencoded({ extended: true }));
app.use(express.json());

// 存储接收到的消息
const receivedMessages = [];

// 初始化Twilio客户端
const twilioClient = twilio(
  process.env.TWILIO_ACCOUNT_SID,
  process.env.TWILIO_AUTH_TOKEN
);

// Webhook端点 - 接收WhatsApp消息状态更新
app.post('/whatsapp-status', (req, res) => {
  console.log('\n📊 收到消息状态更新!');
  console.log('=' .repeat(50));

  const {
    MessageSid,
    MessageStatus,
    From,
    To,
    ErrorCode,
    ErrorMessage
  } = req.body;

  console.log('📋 状态更新详情:');
  console.log('   消息ID:', MessageSid);
  console.log('   状态:', MessageStatus);
  console.log('   发送方:', From);
  console.log('   接收方:', To);
  if (ErrorCode) {
    console.log('   错误代码:', ErrorCode);
    console.log('   错误信息:', ErrorMessage);
  }
  console.log('   更新时间:', new Date().toLocaleString());
  console.log('');

  // 返回200状态码确认接收
  res.status(200).send('OK');
});

// Webhook端点 - 接收WhatsApp消息
app.post('/whatsapp-webhook', async (req, res) => {
  console.log('\n📱 收到WhatsApp消息!');
  console.log('=' .repeat(50));
  
  const {
    MessageSid,
    From,
    To,
    Body,
    NumMedia,
    MediaUrl0,
    MediaContentType0,
    ProfileName,
    WaId
  } = req.body;

  // 记录消息详情
  const messageData = {
    sid: MessageSid,
    from: From,
    to: To,
    body: Body,
    numMedia: NumMedia || 0,
    mediaUrl: MediaUrl0,
    mediaType: MediaContentType0,
    profileName: ProfileName,
    waId: WaId,
    timestamp: new Date().toISOString(),
    rawData: req.body
  };

  receivedMessages.push(messageData);

  console.log('📋 消息详情:');
  console.log('   消息ID:', MessageSid);
  console.log('   发送方:', From);
  console.log('   接收方:', To);
  console.log('   内容:', Body);
  console.log('   发送者姓名:', ProfileName || 'Unknown');
  console.log('   WhatsApp ID:', WaId);
  console.log('   媒体文件数:', NumMedia || 0);
  if (MediaUrl0) {
    console.log('   媒体URL:', MediaUrl0);
    console.log('   媒体类型:', MediaContentType0);
  }
  console.log('   接收时间:', new Date().toLocaleString());
  console.log('');

  // 返回TwiML响应
  const twiml = new twilio.twiml.MessagingResponse();
  res.writeHead(200, {'Content-Type': 'text/xml'});
  res.end(twiml.toString());

  // 异步处理消息（不阻塞响应）
  setImmediate(async () => {
    try {
      await processMessage(From, Body, ProfileName, {
        numMedia: NumMedia || 0,
        mediaUrl: MediaUrl0,
        mediaType: MediaContentType0
      });
    } catch (error) {
      console.error('❌ 处理消息失败:', error.message);
    }
  });
});

// 处理消息的简化版本
async function processMessage(from, message, userName, mediaInfo) {
  console.log('🧠 开始处理消息...');
  
  // 生成简单回复
  let reply = '📨 消息已收到！这是统一机器人的测试回复。';
  
  if (message) {
    const lowerMessage = message.toLowerCase();
    if (lowerMessage.includes('hello') || lowerMessage.includes('你好')) {
      reply = `👋 你好 ${userName || '朋友'}！统一机器人正在正常工作！`;
    } else if (lowerMessage.includes('test') || lowerMessage.includes('测试')) {
      reply = `✅ 测试成功！统一机器人已收到您的消息："${message}"`;
    }
  }
  
  if (mediaInfo && mediaInfo.numMedia > 0) {
    reply += '\n\n🖼️ 检测到媒体文件，统一机器人已接收！';
  }
  
  // 发送回复
  await sendWhatsAppMessage(from, reply);
}

// 发送WhatsApp消息
async function sendWhatsAppMessage(to, body) {
  try {
    console.log(`📤 发送WhatsApp消息到 ${to}:`);
    console.log(`   ${body.substring(0, 100)}${body.length > 100 ? '...' : ''}`);
    
    const message = await twilioClient.messages.create({
      from: 'whatsapp:+14155238886',
      body: body,
      to: to
    });
    
    console.log(`✅ 消息发送成功: ${message.sid}`);
    return message;
    
  } catch (error) {
    console.error('❌ 发送WhatsApp消息失败:', error.message);
    throw error;
  }
}

// 健康检查端点
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    service: 'WhatsApp Unified Bot (Simple)',
    uptime: process.uptime(),
    receivedMessages: receivedMessages.length,
    timestamp: new Date().toISOString()
  });
});

// 查看接收到的消息
app.get('/messages', (req, res) => {
  res.json({
    total: receivedMessages.length,
    messages: receivedMessages.slice(-10),
    timestamp: new Date().toISOString()
  });
});

// 启动服务器
app.listen(PORT, () => {
  console.log('🚀 WhatsApp统一智能机器人（简化版）启动成功!');
  console.log('=' .repeat(60));
  console.log(`📡 监听端口: ${PORT}`);
  console.log(`🔗 Webhook URL: http://localhost:${PORT}/whatsapp-webhook`);
  console.log(`📊 状态更新URL: http://localhost:${PORT}/whatsapp-status`);
  console.log(`🏥 健康检查: http://localhost:${PORT}/health`);
  console.log(`📨 查看消息: http://localhost:${PORT}/messages`);
  console.log('');
  console.log('💡 使用ngrok暴露到公网:');
  console.log(`   ngrok http ${PORT}`);
  console.log('');
  console.log('⏳ 等待接收WhatsApp消息...');
});

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n📊 服务器关闭统计:');
  console.log(`   总接收消息: ${receivedMessages.length} 条`);
  console.log(`   运行时间: ${Math.floor(process.uptime())} 秒`);
  console.log('👋 统一机器人服务已关闭');
  process.exit(0);
});

module.exports = app;
