// 清理空的测试文件
const fs = require('fs');
const path = require('path');

class TestCleaner {
  constructor() {
    this.emptyFiles = [];
    this.cleanedFiles = [];
  }

  // 检查文件是否为空或只包含注释
  isEmptyTestFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8').trim();
      
      // 完全空文件
      if (content.length === 0) {
        return true;
      }
      
      // 只包含单行注释或空行
      const lines = content.split('\n').map(line => line.trim());
      const nonEmptyLines = lines.filter(line => line.length > 0);
      
      if (nonEmptyLines.length === 0) {
        return true;
      }
      
      // 只包含注释的文件
      const codeLines = nonEmptyLines.filter(line => 
        !line.startsWith('//') && 
        !line.startsWith('/*') && 
        !line.startsWith('*') &&
        !line.endsWith('*/')
      );
      
      return codeLines.length === 0;
      
    } catch (error) {
      console.error(`读取文件失败: ${filePath}`, error.message);
      return false;
    }
  }

  // 扫描目录中的测试文件
  scanDirectory(dirPath) {
    try {
      const items = fs.readdirSync(dirPath);
      
      for (const item of items) {
        const itemPath = path.join(dirPath, item);
        const stat = fs.statSync(itemPath);
        
        if (stat.isDirectory()) {
          // 递归扫描子目录
          this.scanDirectory(itemPath);
        } else if (item.endsWith('.js') && (item.startsWith('test-') || item.endsWith('.test.js'))) {
          // 检查测试文件
          if (this.isEmptyTestFile(itemPath)) {
            this.emptyFiles.push(itemPath);
          }
        }
      }
    } catch (error) {
      console.error(`扫描目录失败: ${dirPath}`, error.message);
    }
  }

  // 清理空文件
  cleanEmptyFiles(dryRun = false) {
    console.log(`\n🧹 ${dryRun ? '预览' : '执行'}清理空测试文件`);
    console.log('-'.repeat(50));
    
    if (this.emptyFiles.length === 0) {
      console.log('✅ 没有发现空的测试文件');
      return;
    }
    
    console.log(`发现 ${this.emptyFiles.length} 个空测试文件:`);
    
    for (const filePath of this.emptyFiles) {
      const relativePath = path.relative(process.cwd(), filePath);
      console.log(`   📄 ${relativePath}`);
      
      if (!dryRun) {
        try {
          fs.unlinkSync(filePath);
          this.cleanedFiles.push(filePath);
          console.log(`      ✅ 已删除`);
        } catch (error) {
          console.log(`      ❌ 删除失败: ${error.message}`);
        }
      }
    }
    
    if (dryRun) {
      console.log(`\n⚠️ 这是预览模式，没有实际删除文件`);
      console.log(`要执行删除，请运行: node tests/cleanup-empty-tests.js --execute`);
    } else {
      console.log(`\n✅ 清理完成，删除了 ${this.cleanedFiles.length} 个空文件`);
    }
  }

  // 生成清理报告
  generateReport() {
    console.log('\n📊 清理报告');
    console.log('=' .repeat(40));
    console.log(`扫描的测试目录: tests/`);
    console.log(`发现的空文件: ${this.emptyFiles.length}`);
    console.log(`成功清理: ${this.cleanedFiles.length}`);
    
    if (this.cleanedFiles.length > 0) {
      console.log('\n已清理的文件:');
      this.cleanedFiles.forEach((file, index) => {
        const relativePath = path.relative(process.cwd(), file);
        console.log(`   ${index + 1}. ${relativePath}`);
      });
    }
  }
}

// 主函数
function main() {
  console.log('🎯 测试文件清理工具');
  console.log('=' .repeat(60));
  
  const args = process.argv.slice(2);
  const execute = args.includes('--execute') || args.includes('-e');
  const dryRun = !execute;
  
  const cleaner = new TestCleaner();
  const testsDir = path.join(__dirname);
  
  console.log(`📁 扫描测试目录: ${testsDir}`);
  
  // 扫描所有测试文件
  cleaner.scanDirectory(testsDir);
  
  // 清理空文件
  cleaner.cleanEmptyFiles(dryRun);
  
  // 生成报告
  cleaner.generateReport();
  
  if (dryRun && cleaner.emptyFiles.length > 0) {
    console.log('\n💡 使用说明:');
    console.log('   预览模式: node tests/cleanup-empty-tests.js');
    console.log('   执行清理: node tests/cleanup-empty-tests.js --execute');
  }
}

// 错误处理
process.on('uncaughtException', (error) => {
  console.error('💥 未捕获的异常:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('💥 未处理的Promise拒绝:', reason);
  process.exit(1);
});

// 运行清理
main();
