# Twilio Business Messaging API

一个完整的Twilio Business-Initiated消息后台API系统，基于Node.js和Express构建。

## 功能特性

- ✅ **消息发送**: 支持单条和批量消息发送
- ✅ **消息接收**: 处理入站消息和自动回复
- ✅ **状态跟踪**: 实时跟踪消息发送、送达、已读状态
- ✅ **Webhook处理**: 完整的Twilio webhook集成
- ✅ **会话管理**: 自动创建和管理对话会话
- ✅ **数据持久化**: MongoDB数据存储
- ✅ **认证授权**: API Key和JWT双重认证
- ✅ **限流保护**: 多层级API限流
- ✅ **错误处理**: 完善的错误处理和日志记录
- ✅ **API文档**: 详细的API文档和示例

## 技术栈

- **运行环境**: Node.js 18+
- **Web框架**: Express.js
- **数据库**: MongoDB + Mongoose
- **消息服务**: Twilio SDK
- **认证**: JWT + API Keys
- **日志**: Winston
- **测试**: Jest + Supertest
- **验证**: Joi

## 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd twilio-business-messaging

# 安装依赖
npm install

# 复制环境配置文件
cp .env.example .env
```

### 2. 配置环境变量

编辑 `.env` 文件：

```env
# Twilio配置
TWILIO_ACCOUNT_SID=your_account_sid_here
TWILIO_AUTH_TOKEN=your_auth_token_here
TWILIO_PHONE_NUMBER=your_twilio_phone_number

# 数据库配置
MONGODB_URI=mongodb://localhost:27017/twilio_messaging

# 服务器配置
PORT=3000
NODE_ENV=development

# 认证配置
JWT_SECRET=your_jwt_secret_here
API_KEY=your_api_key_here

# Webhook配置
WEBHOOK_BASE_URL=https://your-domain.com
```

### 3. 启动服务

```bash
# 开发模式
npm run dev

# 生产模式
npm start
```

### 4. 验证安装

访问 `http://localhost:3000/health` 检查服务状态。

## API使用示例

### 发送消息

```javascript
const axios = require('axios');

const sendMessage = async () => {
  try {
    const response = await axios.post('http://localhost:3000/api/messages/send', {
      to: '+**********',
      body: 'Hello from our business!',
      campaignId: 'welcome-campaign'
    }, {
      headers: {
        'X-API-Key': 'your_api_key_here',
        'Content-Type': 'application/json'
      }
    });
    
    console.log('Message sent:', response.data);
  } catch (error) {
    console.error('Error:', error.response.data);
  }
};
```

### 获取消息状态

```javascript
const getMessageStatus = async (messageId) => {
  try {
    const response = await axios.get(`http://localhost:3000/api/status/message/${messageId}`, {
      headers: {
        'X-API-Key': 'your_api_key_here'
      }
    });
    
    console.log('Message status:', response.data);
  } catch (error) {
    console.error('Error:', error.response.data);
  }
};
```

## 项目结构

```
twilio-business-messaging/
├── src/
│   ├── controllers/     # 控制器层
│   ├── services/        # 业务逻辑层
│   ├── models/          # 数据模型
│   ├── routes/          # 路由定义
│   ├── middleware/      # 中间件
│   ├── utils/           # 工具函数
│   ├── config/          # 配置文件
│   └── app.js           # 主应用文件
├── tests/               # 测试文件
├── docs/                # API文档
├── logs/                # 日志文件
├── package.json         # 项目配置
└── README.md           # 项目说明
```

## API端点

### 消息相关
- `POST /api/messages/send` - 发送单条消息
- `POST /api/messages/send-bulk` - 批量发送消息
- `GET /api/messages/history` - 获取消息历史
- `GET /api/messages/conversation/:id` - 获取会话消息

### 状态跟踪
- `GET /api/status/message/:id` - 获取消息状态
- `POST /api/status/sync` - 同步Twilio状态
- `GET /api/status/conversation/:id` - 获取会话状态
- `GET /api/status/delivery-report` - 获取传递报告
- `POST /api/status/real-time` - 获取实时状态

### Webhook
- `POST /webhook/message-status` - 消息状态回调
- `POST /webhook/incoming-message` - 入站消息处理
- `GET /webhook/health` - Webhook健康检查

## 测试

```bash
# 运行所有测试
npm test

# 运行测试并生成覆盖率报告
npm run test:coverage

# 监听模式运行测试
npm run test:watch
```

## 部署

### Docker部署

```bash
# 构建镜像
docker build -t twilio-messaging .

# 运行容器
docker run -p 3000:3000 --env-file .env twilio-messaging
```

### PM2部署

```bash
# 安装PM2
npm install -g pm2

# 启动应用
pm2 start src/app.js --name twilio-messaging

# 查看状态
pm2 status

# 查看日志
pm2 logs twilio-messaging
```

## 监控和日志

### 日志文件
- `logs/combined.log` - 所有日志
- `logs/error.log` - 错误日志

### 健康检查
- `GET /health` - 服务健康状态
- `GET /webhook/health` - Webhook健康状态

## 安全考虑

1. **认证**: 使用API Key或JWT进行认证
2. **限流**: 多层级API限流保护
3. **验证**: 严格的输入验证
4. **日志**: 完整的操作日志记录
5. **错误处理**: 安全的错误信息返回

## 故障排除

### 常见问题

1. **Twilio连接失败**
   - 检查TWILIO_ACCOUNT_SID和TWILIO_AUTH_TOKEN
   - 验证Twilio账户状态

2. **数据库连接失败**
   - 检查MongoDB服务状态
   - 验证MONGODB_URI配置

3. **Webhook接收失败**
   - 检查WEBHOOK_BASE_URL配置
   - 确保服务器可从外网访问

### 日志分析

```bash
# 查看错误日志
tail -f logs/error.log

# 查看所有日志
tail -f logs/combined.log

# 搜索特定错误
grep "ERROR" logs/combined.log
```

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 许可证

MIT License

## 支持

如有问题或建议，请创建Issue或联系技术支持。
