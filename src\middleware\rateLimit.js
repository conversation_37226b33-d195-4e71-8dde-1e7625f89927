const rateLimit = require('express-rate-limit');
const logger = require('../config/logger');
const config = require('../config');

/**
 * 通用限流中间件
 */
const generalRateLimit = rateLimit({
  windowMs: config.api.rateLimit.windowMs, // 15分钟
  max: config.api.rateLimit.max, // 限制每个IP 15分钟内最多100个请求
  message: {
    success: false,
    error: 'Too many requests from this IP, please try again later',
    retryAfter: Math.ceil(config.api.rateLimit.windowMs / 1000),
  },
  standardHeaders: true, // 返回rate limit信息在 `RateLimit-*` headers
  legacyHeaders: false, // 禁用 `X-RateLimit-*` headers
  handler: (req, res) => {
    logger.warn('Rate limit exceeded', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      path: req.path,
      method: req.method,
    });
    
    res.status(429).json({
      success: false,
      error: 'Too many requests from this IP, please try again later',
      retryAfter: <PERSON>.ceil(config.api.rateLimit.windowMs / 1000),
    });
  },
});

/**
 * 严格限流中间件 - 用于敏感操作
 */
const strictRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 20, // 限制每个IP 15分钟内最多20个请求
  message: {
    success: false,
    error: 'Too many requests for this operation, please try again later',
    retryAfter: 900, // 15分钟
  },
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req, res) => {
    logger.warn('Strict rate limit exceeded', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      path: req.path,
      method: req.method,
    });
    
    res.status(429).json({
      success: false,
      error: 'Too many requests for this operation, please try again later',
      retryAfter: 900,
    });
  },
});

/**
 * 消息发送限流中间件
 */
const messagingRateLimit = rateLimit({
  windowMs: 60 * 1000, // 1分钟
  max: 10, // 限制每个IP 1分钟内最多10条消息
  message: {
    success: false,
    error: 'Too many messages sent, please slow down',
    retryAfter: 60,
  },
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req, res) => {
    logger.warn('Messaging rate limit exceeded', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      to: req.body?.to,
    });
    
    res.status(429).json({
      success: false,
      error: 'Too many messages sent, please slow down',
      retryAfter: 60,
    });
  },
});

/**
 * Webhook限流中间件 - 相对宽松
 */
const webhookRateLimit = rateLimit({
  windowMs: 60 * 1000, // 1分钟
  max: 1000, // 限制每个IP 1分钟内最多1000个webhook请求
  message: {
    success: false,
    error: 'Too many webhook requests',
  },
  standardHeaders: false,
  legacyHeaders: false,
  handler: (req, res) => {
    logger.warn('Webhook rate limit exceeded', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      path: req.path,
    });
    
    res.status(429).json({
      success: false,
      error: 'Too many webhook requests',
    });
  },
});

/**
 * 基于用户的限流中间件
 */
const createUserRateLimit = (windowMs, max) => {
  return rateLimit({
    windowMs,
    max,
    keyGenerator: (req) => {
      // 优先使用用户ID，其次使用API key，最后使用IP
      if (req.auth?.user?.id) {
        return `user:${req.auth.user.id}`;
      }
      if (req.auth?.type === 'api-key') {
        return `apikey:${req.header('X-API-Key') || req.query.apiKey}`;
      }
      return req.ip;
    },
    message: {
      success: false,
      error: 'Rate limit exceeded for this user/API key',
    },
    standardHeaders: true,
    legacyHeaders: false,
  });
};

module.exports = generalRateLimit;
module.exports.general = generalRateLimit;
module.exports.strict = strictRateLimit;
module.exports.messaging = messagingRateLimit;
module.exports.webhook = webhookRateLimit;
module.exports.createUserRateLimit = createUserRateLimit;
