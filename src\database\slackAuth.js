// Slack 工作区鉴权数据库管理
const sqlite3 = require('sqlite3').verbose();
const path = require('path');

class SlackAuthDatabase {
  constructor() {
    // 数据库文件路径
    this.dbPath = path.join(__dirname, '../../data/slack_auth.db');
    this.db = null;
    this.init();
  }

  // 初始化数据库
  async init() {
    return new Promise((resolve, reject) => {
      // 确保数据目录存在
      const fs = require('fs');
      const dataDir = path.dirname(this.dbPath);
      if (!fs.existsSync(dataDir)) {
        fs.mkdirSync(dataDir, { recursive: true });
      }

      this.db = new sqlite3.Database(this.dbPath, (err) => {
        if (err) {
          console.error('❌ SQLite 数据库连接失败:', err.message);
          reject(err);
        } else {
          console.log('✅ SQLite 数据库连接成功:', this.dbPath);
          this.createTables().then(resolve).catch(reject);
        }
      });
    });
  }

  // 创建表结构
  async createTables() {
    return new Promise((resolve, reject) => {
      const createWorkspacesTable = `
        CREATE TABLE IF NOT EXISTS slack_workspaces (
          team_id TEXT PRIMARY KEY,
          team_name TEXT,
          bot_user_id TEXT,
          bot_user_access_token TEXT,
          bot_refresh_token TEXT,
          bot_token_expires_at INTEGER,
          authed_user_id TEXT,
          scope TEXT,
          installed_at TEXT,
          updated_at TEXT,
          is_active INTEGER DEFAULT 1
        )
      `;

      const createUserMappingTable = `
        CREATE TABLE IF NOT EXISTS slack_user_mapping (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          user_id TEXT NOT NULL,
          team_id TEXT NOT NULL,
          channel_id TEXT,
          created_at TEXT,
          updated_at TEXT,
          FOREIGN KEY (team_id) REFERENCES slack_workspaces (team_id),
          UNIQUE(user_id, team_id)
        )
      `;

      const createUserTokensTable = `
        CREATE TABLE IF NOT EXISTS slack_user_tokens (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          user_id TEXT NOT NULL,
          team_id TEXT NOT NULL,
          access_token TEXT,
          refresh_token TEXT,
          token_expires_at INTEGER,
          scope TEXT,
          created_at TEXT,
          updated_at TEXT,
          FOREIGN KEY (team_id) REFERENCES slack_workspaces (team_id),
          UNIQUE(user_id, team_id)
        )
      `;

      // 添加缺失字段的迁移SQL
      const addBotRefreshTokenColumn = `
        ALTER TABLE slack_workspaces
        ADD COLUMN bot_refresh_token TEXT
      `;

      const addBotTokenExpiresAtColumn = `
        ALTER TABLE slack_workspaces
        ADD COLUMN bot_token_expires_at INTEGER
      `;

      this.db.serialize(() => {
        this.db.run(createWorkspacesTable, (err) => {
          if (err) {
            console.error('❌ 创建工作区表失败:', err.message);
            reject(err);
            return;
          }
          console.log('✅ 工作区表创建成功');

          // 尝试添加缺失的字段（如果表已存在）
          this.db.run(addBotRefreshTokenColumn, (err) => {
            if (err && !err.message.includes('duplicate column name')) {
              console.error('⚠️ 添加bot_refresh_token字段失败:', err.message);
            } else if (!err) {
              console.log('✅ 添加bot_refresh_token字段成功');
            }
          });

          this.db.run(addBotTokenExpiresAtColumn, (err) => {
            if (err && !err.message.includes('duplicate column name')) {
              console.error('⚠️ 添加bot_token_expires_at字段失败:', err.message);
            } else if (!err) {
              console.log('✅ 添加bot_token_expires_at字段成功');
            }
          });
        });

        this.db.run(createUserMappingTable, (err) => {
          if (err) {
            console.error('❌ 创建用户映射表失败:', err.message);
            reject(err);
            return;
          }
          console.log('✅ 用户映射表创建成功');
        });

        this.db.run(createUserTokensTable, (err) => {
          if (err) {
            console.error('❌ 创建用户令牌表失败:', err.message);
            reject(err);
            return;
          }
          console.log('✅ 用户令牌表创建成功');
          resolve();
        });
      });
    });
  }

  // 保存工作区鉴权信息
  async saveWorkspaceAuth(authData) {
    return new Promise((resolve, reject) => {
      const {
        team_id,
        team_name,
        bot_user_id,
        bot_user_access_token,
        bot_refresh_token,
        bot_token_expires_at,
        authed_user_id,
        scope,
        installed_at
      } = authData;

      const now = new Date().toISOString();

      const sql = `
        INSERT OR REPLACE INTO slack_workspaces
        (team_id, team_name, bot_user_id, bot_user_access_token, bot_refresh_token, bot_token_expires_at, authed_user_id, scope, installed_at, updated_at, is_active)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1)
      `;

      this.db.run(sql, [
        team_id,
        team_name,
        bot_user_id,
        bot_user_access_token,
        bot_refresh_token,
        bot_token_expires_at,
        authed_user_id,
        scope,
        installed_at || now,
        now
      ], function(err) {
        if (err) {
          console.error('❌ 保存工作区鉴权信息失败:', err.message);
          reject(err);
        } else {
          console.log('✅ 工作区鉴权信息保存成功:', team_id);
          console.log('   Bot Token过期时间:', bot_token_expires_at ? new Date(bot_token_expires_at).toISOString() : '未设置');
          console.log('   Bot Refresh Token:', bot_refresh_token ? '已保存' : '未提供');
          resolve({ team_id, changes: this.changes });
        }
      });
    });
  }

  // 根据团队ID获取工作区信息
  async getWorkspaceByTeamId(team_id) {
    return new Promise((resolve, reject) => {
      const sql = `
        SELECT * FROM slack_workspaces 
        WHERE team_id = ? AND is_active = 1
      `;

      this.db.get(sql, [team_id], (err, row) => {
        if (err) {
          console.error('❌ 获取工作区信息失败:', err.message);
          reject(err);
        } else {
          resolve(row);
        }
      });
    });
  }

  // 根据用户ID获取对应的工作区信息
  async getWorkspaceByUserId(user_id) {
    return new Promise((resolve, reject) => {
      const sql = `
        SELECT w.* FROM slack_workspaces w
        JOIN slack_user_mapping m ON w.team_id = m.team_id
        WHERE m.user_id = ? AND w.is_active = 1
        ORDER BY m.updated_at DESC
        LIMIT 1
      `;

      this.db.get(sql, [user_id], (err, row) => {
        if (err) {
          console.error('❌ 根据用户ID获取工作区信息失败:', err.message);
          reject(err);
        } else {
          resolve(row);
        }
      });
    });
  }

  // 保存用户和工作区的映射关系
  async saveUserMapping(user_id, team_id, channel_id = null) {
    return new Promise((resolve, reject) => {
      const now = new Date().toISOString();

      const sql = `
        INSERT OR REPLACE INTO slack_user_mapping
        (user_id, team_id, channel_id, created_at, updated_at)
        VALUES (?, ?, ?,
          COALESCE((SELECT created_at FROM slack_user_mapping WHERE user_id = ? AND team_id = ?), ?),
          ?)
      `;

      this.db.run(sql, [
        user_id, team_id, channel_id,
        user_id, team_id, now,  // for COALESCE
        now
      ], function(err) {
        if (err) {
          console.error('❌ 保存用户映射失败:', err.message);
          reject(err);
        } else {
          console.log('✅ 用户映射保存成功:', { user_id, team_id, channel_id });
          resolve({ user_id, team_id, changes: this.changes });
        }
      });
    });
  }

  // 获取用户和工作区的映射关系
  async getUserMapping(user_id) {
    return new Promise((resolve, reject) => {
      const sql = `
        SELECT user_id, team_id, channel_id, created_at, updated_at
        FROM slack_user_mapping
        WHERE user_id = ?
        ORDER BY updated_at DESC
        LIMIT 1
      `;

      this.db.get(sql, [user_id], (err, row) => {
        if (err) {
          console.error('❌ 获取用户映射失败:', err.message);
          reject(err);
        } else {
          resolve(row);
        }
      });
    });
  }

  // 获取所有活跃的工作区
  async getAllWorkspaces() {
    return new Promise((resolve, reject) => {
      const sql = `
        SELECT team_id, team_name, bot_user_id, authed_user_id, scope, installed_at, updated_at,
               CASE WHEN bot_user_access_token IS NOT NULL AND bot_user_access_token != '' THEN 1 ELSE 0 END as has_token
        FROM slack_workspaces 
        WHERE is_active = 1
        ORDER BY updated_at DESC
      `;

      this.db.all(sql, [], (err, rows) => {
        if (err) {
          console.error('❌ 获取工作区列表失败:', err.message);
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }

  // 根据团队ID获取Bot Token
  async getBotTokenForTeam(team_id) {
    const workspace = await this.getWorkspaceByTeamId(team_id);
    return workspace?.bot_user_access_token;
  }

  // 根据用户ID获取Bot Token
  async getBotTokenForUser(user_id) {
    const workspace = await this.getWorkspaceByUserId(user_id);
    return workspace?.bot_user_access_token;
  }

  // 保存用户令牌信息
  async saveUserToken(tokenData) {
    return new Promise((resolve, reject) => {
      const {
        user_id,
        team_id,
        access_token,
        refresh_token,
        token_expires_at,
        scope
      } = tokenData;

      const now = new Date().toISOString();

      const sql = `
        INSERT OR REPLACE INTO slack_user_tokens
        (user_id, team_id, access_token, refresh_token, token_expires_at, scope, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?,
          COALESCE((SELECT created_at FROM slack_user_tokens WHERE user_id = ? AND team_id = ?), ?),
          ?)
      `;

      this.db.run(sql, [
        user_id, team_id, access_token, refresh_token, token_expires_at, scope,
        user_id, team_id, now,  // for COALESCE
        now
      ], function(err) {
        if (err) {
          console.error('❌ 保存用户令牌失败:', err.message);
          reject(err);
        } else {
          console.log('✅ 用户令牌保存成功:', user_id);
          resolve({ user_id, team_id, changes: this.changes });
        }
      });
    });
  }

  // 获取用户令牌信息
  async getUserToken(user_id) {
    return new Promise((resolve, reject) => {
      const sql = `
        SELECT * FROM slack_user_tokens
        WHERE user_id = ?
        ORDER BY updated_at DESC
        LIMIT 1
      `;

      this.db.get(sql, [user_id], (err, row) => {
        if (err) {
          console.error('❌ 获取用户令牌失败:', err.message);
          reject(err);
        } else {
          resolve(row);
        }
      });
    });
  }

  // 更新工作区令牌信息
  async updateWorkspaceToken(team_id, tokenData) {
    return new Promise((resolve, reject) => {
      const {
        bot_user_access_token,
        bot_refresh_token,
        bot_token_expires_at
      } = tokenData;

      const now = new Date().toISOString();

      const sql = `
        UPDATE slack_workspaces
        SET bot_user_access_token = ?, bot_refresh_token = ?, bot_token_expires_at = ?, updated_at = ?
        WHERE team_id = ?
      `;

      this.db.run(sql, [
        bot_user_access_token,
        bot_refresh_token,
        bot_token_expires_at,
        now,
        team_id
      ], function(err) {
        if (err) {
          console.error('❌ 更新工作区令牌失败:', err.message);
          reject(err);
        } else {
          console.log('✅ 工作区令牌更新成功:', team_id);
          resolve({ team_id, changes: this.changes });
        }
      });
    });
  }

  // 关闭数据库连接
  close() {
    if (this.db) {
      this.db.close((err) => {
        if (err) {
          console.error('❌ 关闭数据库连接失败:', err.message);
        } else {
          console.log('✅ 数据库连接已关闭');
        }
      });
    }
  }
}

module.exports = SlackAuthDatabase;
