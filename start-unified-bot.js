#!/usr/bin/env node

// WhatsApp统一机器人启动脚本
console.log('🚀 启动WhatsApp统一智能机器人...\n');

// 检查环境变量
const requiredEnvVars = [
  'TWILIO_ACCOUNT_SID',
  'TWILIO_AUTH_TOKEN',
  'COZE_API_TOKEN',
  'COZE_BOT_ID'
];

console.log('🔍 检查环境变量配置...');
const missingVars = [];

requiredEnvVars.forEach(varName => {
  if (!process.env[varName]) {
    missingVars.push(varName);
  } else {
    console.log(`   ✅ ${varName}: 已配置`);
  }
});

if (missingVars.length > 0) {
  console.log('\n❌ 缺少必要的环境变量:');
  missingVars.forEach(varName => {
    console.log(`   - ${varName}`);
  });
  console.log('\n请在.env文件中配置这些变量，然后重新启动。');
  process.exit(1);
}

console.log('\n✅ 环境变量检查通过！');
console.log('\n🔄 启动统一机器人服务...\n');

// 启动统一机器人
require('./whatsapp-unified-bot');
