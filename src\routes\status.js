const express = require('express');
const statusController = require('../controllers/statusController');
const authMiddleware = require('../middleware/auth');
const rateLimitMiddleware = require('../middleware/rateLimit');

const router = express.Router();

// 应用认证和限流中间件
router.use(authMiddleware);
router.use(rateLimitMiddleware);

/**
 * @route GET /api/status/message/:messageId
 * @desc 获取消息状态
 * @access Private
 * @param messageId - 消息ID
 */
router.get('/message/:messageId', statusController.getMessageStatus);

/**
 * @route POST /api/status/sync
 * @desc 从Twilio同步消息状态
 * @access Private
 * @body twilioSid - Twilio消息SID
 */
router.post('/sync', statusController.syncMessageStatus);

/**
 * @route GET /api/status/conversation/:conversationId
 * @desc 获取会话状态统计
 * @access Private
 * @param conversationId - 会话ID
 */
router.get('/conversation/:conversationId', statusController.getConversationStatus);

/**
 * @route GET /api/status/delivery-report
 * @desc 获取消息传递报告
 * @access Private
 * @query startDate - 开始日期 (ISO格式)
 * @query endDate - 结束日期 (ISO格式)
 * @query phoneNumber - 电话号码
 * @query campaignId - 活动ID
 * @query status - 消息状态
 * @query direction - 消息方向 (outbound/inbound)
 */
router.get('/delivery-report', statusController.getDeliveryReport);

/**
 * @route POST /api/status/real-time
 * @desc 获取实时状态更新
 * @access Private
 * @body messageIds - 消息ID数组
 */
router.post('/real-time', statusController.getRealTimeStatus);

/**
 * @route POST /api/status/batch-sync
 * @desc 批量同步消息状态
 * @access Private
 * @body twilioSids - Twilio SID数组
 */
router.post('/batch-sync', statusController.batchSyncStatus);

module.exports = router;
