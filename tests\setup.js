// Jest测试环境设置
let mongod;

// 全局测试设置
beforeAll(async () => {
  // 设置测试环境变量
  process.env.NODE_ENV = 'test';
  process.env.LOG_LEVEL = 'error'; // 减少测试时的日志输出

  // 使用内存数据库进行测试（可选）
  if (process.env.USE_MEMORY_DB === 'true') {
    try {
      const { MongoMemoryServer } = require('mongodb-memory-server');
      mongod = await MongoMemoryServer.create();
      const uri = mongod.getUri();
      process.env.MONGODB_TEST_URI = uri;
    } catch (error) {
      console.warn('mongodb-memory-server not available, using regular test database');
    }
  }

  // 设置测试用的Twilio配置（使用测试凭据）
  process.env.TWILIO_ACCOUNT_SID = 'ACtest123456789';
  process.env.TWILIO_AUTH_TOKEN = 'test_auth_token';
  process.env.TWILIO_PHONE_NUMBER = '+***********';
  
  // 设置测试API密钥
  process.env.API_KEY = 'test-api-key';
  process.env.JWT_SECRET = 'test-jwt-secret';
  
  // 设置webhook基础URL
  process.env.WEBHOOK_BASE_URL = 'http://localhost:3000';
});

// 全局测试清理
afterAll(async () => {
  if (mongod) {
    await mongod.stop();
  }
});

// 模拟Twilio客户端
jest.mock('../src/config/twilio', () => {
  return {
    getClient: () => ({
      messages: {
        create: jest.fn().mockResolvedValue({
          sid: 'SM_mock_message_sid',
          status: 'queued',
          price: '0.0075',
          priceUnit: 'USD'
        })
      },
      api: {
        accounts: jest.fn().mockReturnValue({
          fetch: jest.fn().mockResolvedValue({
            sid: 'ACtest123456789',
            status: 'active',
            friendlyName: 'Test Account'
          })
        })
      }
    }),
    getPhoneNumber: () => '+***********',
    validateConnection: jest.fn().mockResolvedValue(true),
    getAccountInfo: jest.fn().mockResolvedValue({
      sid: 'ACtest123456789',
      friendlyName: 'Test Account',
      status: 'active',
      type: 'Full'
    })
  };
});

// 设置Jest超时时间
jest.setTimeout(30000);
