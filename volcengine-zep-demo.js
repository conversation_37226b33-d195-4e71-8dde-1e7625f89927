const { ZepClient } = require('@getzep/zep-cloud');
const OpenAI = require('openai');
require('dotenv').config();

/**
 * 火山方舟 + Zep Cloud 集成演示
 * 展示如何结合火山方舟大模型和Zep记忆服务构建智能对话系统
 */

class VolcengineZepDemo {
  constructor() {
    // 检查必要的环境变量
    if (!process.env.ZEP_API_KEY) {
      throw new Error('❌ 缺少 ZEP_API_KEY 环境变量');
    }
    
    if (!process.env.ARK_API_KEY) {
      throw new Error('❌ 缺少 ARK_API_KEY 环境变量');
    }

    // 初始化客户端
    this.zepClient = new ZepClient({
      apiKey: process.env.ZEP_API_KEY
    });

    this.openaiClient = new OpenAI({
      apiKey: process.env.ARK_API_KEY,
      baseURL: 'https://ark.cn-beijing.volces.com/api/v3',
    });

    // 配置
    this.userId = `volcengine-demo-${Date.now()}`;
    this.sessionId = `session-${Date.now()}`;
    this.modelEndpoint = process.env.VOLCENGINE_MODEL_ENDPOINT || 'ep-20241221105607-2w8zx';
  }

  async initialize() {
    console.log('🚀 初始化火山方舟 + Zep 演示系统...\n');

    // 创建用户
    console.log('👤 创建演示用户...');
    const user = await this.zepClient.user.add({
      userId: this.userId,
      email: '<EMAIL>',
      firstName: '火山',
      lastName: '用户',
      metadata: {
        source: 'volcengine-zep-demo',
        model: 'doubao',
        created: new Date().toISOString()
      }
    });
    console.log(`✅ 用户创建成功: ${user.firstName} ${user.lastName}\n`);

    // 创建会话
    console.log('💬 创建智能对话会话...');
    const session = await this.zepClient.memory.addSession({
      sessionId: this.sessionId,
      userId: this.userId,
      metadata: {
        type: 'volcengine-ai-chat',
        model: this.modelEndpoint,
        created: new Date().toISOString()
      }
    });
    console.log(`✅ 会话创建成功: ${session.sessionId}\n`);

    // 添加用户档案到知识图谱
    console.log('🕸️ 构建用户知识图谱...');
    const userProfile = {
      name: '火山用户',
      role: '产品经理',
      company: '科技公司',
      interests: ['人工智能', '产品设计', '用户体验', '数据分析'],
      experience: '3年产品管理经验',
      current_focus: 'AI产品开发',
      skills: ['需求分析', '原型设计', '数据驱动决策', 'A/B测试'],
      goals: ['学习AI技术', '提升产品能力', '了解行业趋势']
    };

    await this.zepClient.graph.add({
      userId: this.userId,
      type: 'json',
      data: JSON.stringify(userProfile)
    });
    console.log('✅ 用户档案已添加到知识图谱\n');
  }

  async chat(userMessage) {
    console.log(`👤 用户: ${userMessage}`);

    // 1. 添加用户消息到Zep记忆并获取上下文
    const memoryResult = await this.zepClient.memory.add(this.sessionId, {
      messages: [{ roleType: 'user', content: userMessage }],
      returnContext: true
    });

    // 2. 搜索知识图谱获取相关信息
    let userContext = '';
    try {
      const graphSearch = await this.zepClient.graph.search({
        userId: this.userId,
        query: `与"${userMessage}"相关的用户信息`
      });

      if (graphSearch.edges && graphSearch.edges.length > 0) {
        userContext = '用户相关信息:\n' + 
          graphSearch.edges.slice(0, 3).map(edge => `- ${edge.fact}`).join('\n') + '\n\n';
      }
    } catch (error) {
      console.log('   📊 知识图谱搜索跳过（数据处理中）');
    }

    // 3. 构建智能提示
    const systemPrompt = `你是一个专业的AI助手，名叫"火山小助手"。请基于以下信息为用户提供个性化、有价值的回答：

${userContext}对话历史上下文：
${memoryResult.context || '这是对话的开始'}

回答要求：
1. 结合用户的背景和兴趣提供个性化建议
2. 保持专业、友好的语调
3. 如果涉及专业知识，请提供具体、实用的信息
4. 适当引用对话历史保持连贯性
5. 回答要简洁明了，重点突出`;

    // 4. 调用火山方舟大模型
    console.log('   🤖 火山方舟大模型思考中...');
    
    const completion = await this.openaiClient.chat.completions.create({
      model: this.modelEndpoint,
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: userMessage }
      ],
      temperature: 0.8,
      max_tokens: 1000,
      top_p: 0.9
    });

    const aiResponse = completion.choices[0].message.content;
    console.log(`🤖 火山小助手: ${aiResponse}\n`);

    // 5. 添加AI回复到记忆
    await this.zepClient.memory.add(this.sessionId, {
      messages: [{ roleType: 'assistant', content: aiResponse }]
    });

    return aiResponse;
  }

  async demonstrateConversation() {
    console.log('💭 开始智能对话演示...\n');

    const conversations = [
      '你好，我想了解一下AI在产品管理中的应用。',
      '那具体在需求分析阶段，AI可以帮助我做什么？',
      '听起来很有用！你能推荐一些相关的工具或平台吗？',
      '我想学习更多AI知识，有什么学习路径建议吗？'
    ];

    for (let i = 0; i < conversations.length; i++) {
      console.log(`🔄 第 ${i + 1} 轮对话:`);
      await this.chat(conversations[i]);
      
      // 短暂停顿，模拟真实对话
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }

  async showMemoryInsights() {
    console.log('🧠 记忆洞察分析...\n');

    // 获取完整记忆
    const memory = await this.zepClient.memory.get(this.sessionId);
    
    console.log('📊 对话统计:');
    console.log(`   - 总消息数: ${memory.messages?.length || 0}`);
    console.log(`   - 用户消息: ${memory.messages?.filter(m => m.roleType === 'user').length || 0}`);
    console.log(`   - AI回复: ${memory.messages?.filter(m => m.roleType === 'assistant').length || 0}`);
    console.log(`   - 有对话摘要: ${memory.summary ? '是' : '否'}`);
    console.log(`   - 有上下文信息: ${memory.context ? '是' : '否'}\n`);

    if (memory.summary) {
      console.log('📝 对话摘要:');
      console.log(`   ${JSON.stringify(memory.summary)}\n`);
    }

    if (memory.context) {
      console.log('📋 上下文信息预览:');
      const contextPreview = memory.context;
      console.log(`   ${contextPreview}...\n`);
    }

    // 尝试搜索知识图谱
    console.log('🔍 知识图谱洞察:');
    try {
      const queries = [
        '用户的职业背景是什么？',
        '用户对什么感兴趣？',
        '用户的技能有哪些？'
      ];

      for (const query of queries) {
        const searchResults = await this.zepClient.graph.search({
          userId: this.userId,
          query: query
        });

        if (searchResults.edges && searchResults.edges.length > 0) {
          console.log(`   ❓ ${query}`);
          searchResults.edges.slice(0, 2).forEach(edge => {
            console.log(`      - ${edge.fact}`);
          });
        }
      }
    } catch (error) {
      console.log('   ⏳ 知识图谱数据还在处理中...');
    }
  }

  async testImageAnalysis() {
    console.log('\n🖼️ 测试多模态能力（图像分析）...\n');

    try {
      // 使用一个公开的测试图片URL
      const imageUrl = 'https://upload.wikimedia.org/wikipedia/commons/thumb/d/dd/Gfp-wisconsin-madison-the-nature-boardwalk.jpg/2560px-Gfp-wisconsin-madison-the-nature-boardwalk.jpg';
      
      const userMessage = '请分析这张图片，告诉我你看到了什么。';
      console.log(`👤 用户: ${userMessage}`);
      console.log(`🖼️ 图片: ${imageUrl}`);

      // 添加用户消息到记忆
      await this.zepClient.memory.add(this.sessionId, {
        messages: [{ roleType: 'user', content: `${userMessage} [图片: ${imageUrl}]` }]
      });

      // 调用火山方舟的多模态能力
      const completion = await this.openaiClient.chat.completions.create({
        model: this.modelEndpoint,
        messages: [
          {
            role: 'user',
            content: [
              {
                type: 'image_url',
                image_url: { url: imageUrl }
              },
              { type: 'text', text: userMessage }
            ]
          }
        ],
        max_tokens: 500
      });

      const imageAnalysis = completion.choices[0].message.content;
      console.log(`🤖 火山小助手: ${imageAnalysis}\n`);

      // 添加分析结果到记忆
      await this.zepClient.memory.add(this.sessionId, {
        messages: [{ roleType: 'assistant', content: imageAnalysis }]
      });

      console.log('✅ 多模态图像分析测试成功');

    } catch (error) {
      console.log('⚠️ 多模态功能可能不支持或需要特殊配置:', error.message);
    }
  }

  async cleanup() {
    console.log('\n🧹 清理演示数据...');
    try {
      await this.zepClient.user.delete(this.userId);
      console.log('✅ 演示数据清理完成');
    } catch (error) {
      console.log(`⚠️ 清理时出错: ${error.message}`);
    }
  }

  async run() {
    try {
      await this.initialize();
      await this.demonstrateConversation();
      await this.showMemoryInsights();
      await this.testImageAnalysis();
      
      console.log('\n🎉 火山方舟 + Zep 集成演示完成！');
      console.log('\n📝 演示总结:');
      console.log('✅ 智能对话系统初始化');
      console.log('✅ 多轮上下文感知对话');
      console.log('✅ 知识图谱个性化增强');
      console.log('✅ 记忆洞察和分析');
      console.log('✅ 多模态能力测试');
      console.log('\n💡 这个系统展示了如何构建具有长期记忆的智能AI助手！');
      
    } catch (error) {
      console.error('\n❌ 演示过程中出错:', error.message);
      if (error.response) {
        console.error('API响应错误:', error.response.status, error.response.data);
      }
    } finally {
      await this.cleanup();
    }
  }
}

// 运行演示
if (require.main === module) {
  const demo = new VolcengineZepDemo();
  demo.run().catch(console.error);
}

module.exports = VolcengineZepDemo;
