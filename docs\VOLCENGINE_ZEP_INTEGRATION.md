# 火山方舟 + Zep Cloud 集成指南

## 概述

本指南展示如何将火山方舟大模型与Zep Cloud记忆服务集成，构建具有长期记忆能力的智能对话系统。

## 架构图

```
用户输入 → Zep记忆检索 → 火山方舟大模型 → 智能回复 → Zep记忆存储
    ↓           ↓              ↓           ↓           ↓
  消息历史   上下文增强      AI推理生成    个性化回复   知识积累
```

## 功能特性

### 🧠 智能记忆管理
- **对话历史**: 自动存储和检索完整对话记录
- **上下文感知**: 基于历史对话提供相关上下文
- **智能摘要**: 自动生成对话要点和摘要

### 🤖 火山方舟大模型
- **豆包系列模型**: 支持豆包-pro、豆包-lite等多种模型
- **多模态能力**: 支持文本和图像理解
- **参数可调**: 温度、top_p等参数灵活配置

### 🕸️ 知识图谱增强
- **用户画像**: 构建详细的用户档案和偏好
- **关系映射**: 理解实体间的复杂关系
- **个性化推荐**: 基于用户背景提供定制化建议

## 环境配置

### 1. 安装依赖

```bash
npm install @getzep/zep-cloud openai
```

### 2. 环境变量配置

在 `.env` 文件中添加以下配置：

```env
# 火山方舟配置
ARK_API_KEY=your_ark_api_key_here
VOLCENGINE_MODEL_ENDPOINT=ep-20241221105607-2w8zx

# Zep Cloud配置
ZEP_API_KEY=your_zep_api_key_here
```

### 3. 获取API密钥

#### 火山方舟API密钥
1. 访问 [火山方舟控制台](https://console.volcengine.com/ark/)
2. 登录您的火山引擎账户
3. 进入"API密钥管理"创建新密钥
4. 创建模型推理端点并获取端点ID

#### Zep Cloud API密钥
1. 访问 [Zep Cloud](https://www.getzep.com/)
2. 注册账户并创建项目
3. 在项目设置中获取API密钥

## 快速开始

### 基本使用示例

```javascript
const { ZepClient } = require('@getzep/zep-cloud');
const OpenAI = require('openai');

// 初始化客户端
const zepClient = new ZepClient({
  apiKey: process.env.ZEP_API_KEY
});

const openaiClient = new OpenAI({
  apiKey: process.env.ARK_API_KEY,
  baseURL: 'https://ark.cn-beijing.volces.com/api/v3',
});

async function intelligentChat(userId, sessionId, userMessage) {
  // 1. 添加用户消息到Zep记忆
  const memoryResult = await zepClient.memory.add(sessionId, {
    messages: [{ roleType: 'user', content: userMessage }],
    returnContext: true
  });

  // 2. 构建包含上下文的提示
  const systemPrompt = `你是一个智能助手。基于以下上下文回答用户问题：
${memoryResult.context || ''}`;

  // 3. 调用火山方舟大模型
  const completion = await openaiClient.chat.completions.create({
    model: process.env.VOLCENGINE_MODEL_ENDPOINT,
    messages: [
      { role: 'system', content: systemPrompt },
      { role: 'user', content: userMessage }
    ],
    temperature: 0.7,
    max_tokens: 1000
  });

  const aiResponse = completion.choices[0].message.content;

  // 4. 保存AI回复到记忆
  await zepClient.memory.add(sessionId, {
    messages: [{ roleType: 'assistant', content: aiResponse }]
  });

  return aiResponse;
}
```

## 测试和演示

### 1. 连接测试

验证火山方舟API连接：

```bash
npm run test:volcengine-connection
# 或
node test-volcengine-connection.js
```

### 2. 单元测试

运行完整的集成测试：

```bash
npm run test:volcengine-zep
# 或
npm test tests/unit/zep-volcengine-integration.test.js
```

测试覆盖：
- ✅ 基础设置和客户端初始化
- ✅ 智能对话流程
- ✅ 多轮上下文感知对话
- ✅ 知识图谱增强对话
- ✅ 错误处理和边界情况

### 3. 功能演示

运行完整的功能演示：

```bash
npm run demo:volcengine-zep
# 或
node volcengine-zep-demo.js
```

演示内容：
- 🚀 系统初始化和用户创建
- 💬 多轮智能对话
- 🧠 记忆洞察分析
- 🖼️ 多模态图像分析

## 高级功能

### 1. 知识图谱增强

```javascript
// 添加用户档案到知识图谱
await zepClient.graph.add({
  userId: userId,
  type: 'json',
  data: JSON.stringify({
    name: '用户姓名',
    occupation: '职业',
    interests: ['兴趣1', '兴趣2'],
    skills: ['技能1', '技能2']
  })
});

// 搜索相关信息
const searchResults = await zepClient.graph.search({
  userId: userId,
  query: '用户的专业背景是什么？'
});
```

### 2. 多模态图像分析

```javascript
const imageResponse = await openaiClient.chat.completions.create({
  model: modelEndpoint,
  messages: [
    {
      role: 'user',
      content: [
        {
          type: 'image_url',
          image_url: { url: 'https://example.com/image.jpg' }
        },
        { type: 'text', text: '请分析这张图片' }
      ]
    }
  ]
});
```

### 3. 参数优化

```javascript
// 创造性回答 (高温度)
const creativeResponse = await openaiClient.chat.completions.create({
  model: modelEndpoint,
  messages: messages,
  temperature: 0.9,  // 更有创意
  top_p: 0.9,
  max_tokens: 1000
});

// 确定性回答 (低温度)
const deterministicResponse = await openaiClient.chat.completions.create({
  model: modelEndpoint,
  messages: messages,
  temperature: 0.1,  // 更确定
  top_p: 0.5,
  max_tokens: 1000
});
```

## 在Twilio项目中集成

### 消息处理服务集成

```javascript
const { ZepClient } = require('@getzep/zep-cloud');
const OpenAI = require('openai');

class IntelligentMessageService {
  constructor() {
    this.zepClient = new ZepClient({
      apiKey: process.env.ZEP_API_KEY
    });
    
    this.openaiClient = new OpenAI({
      apiKey: process.env.ARK_API_KEY,
      baseURL: 'https://ark.cn-beijing.volces.com/api/v3',
    });
  }

  async processWhatsAppMessage(from, body) {
    const userId = from.replace('whatsapp:', '');
    const sessionId = `whatsapp-${userId}`;

    // 确保用户和会话存在
    await this.ensureUserAndSession(userId, sessionId);

    // 获取历史上下文并生成回复
    const response = await this.generateIntelligentResponse(
      sessionId, 
      body
    );

    return response;
  }

  async generateIntelligentResponse(sessionId, userMessage) {
    // 添加用户消息到记忆
    const memoryResult = await this.zepClient.memory.add(sessionId, {
      messages: [{ roleType: 'user', content: userMessage }],
      returnContext: true
    });

    // 构建智能提示
    const systemPrompt = `你是一个专业的WhatsApp客服助手。
基于以下对话历史为用户提供帮助：

${memoryResult.context || ''}

请用友好、专业的语调回答用户问题。`;

    // 调用火山方舟生成回复
    const completion = await this.openaiClient.chat.completions.create({
      model: process.env.VOLCENGINE_MODEL_ENDPOINT,
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: userMessage }
      ],
      temperature: 0.7,
      max_tokens: 500
    });

    const aiResponse = completion.choices[0].message.content;

    // 保存AI回复到记忆
    await this.zepClient.memory.add(sessionId, {
      messages: [{ roleType: 'assistant', content: aiResponse }]
    });

    return aiResponse;
  }
}
```

## 最佳实践

### 1. 性能优化
- 合理设置`max_tokens`避免过长回复
- 使用适当的`temperature`平衡创造性和准确性
- 定期清理过期的会话数据

### 2. 成本控制
- 监控API调用频率和token使用量
- 根据场景选择合适的模型（pro vs lite）
- 实现请求缓存机制

### 3. 错误处理
- 实现重试机制处理网络异常
- 优雅降级：API失败时提供基础回复
- 记录详细的错误日志便于调试

### 4. 安全考虑
- 妥善保管API密钥
- 实现用户输入验证和过滤
- 遵循数据隐私保护规范

## 故障排除

### 常见问题

1. **火山方舟API调用失败**
   ```
   解决方案：
   - 检查ARK_API_KEY是否正确
   - 验证模型端点ID是否有效
   - 确认API密钥权限设置
   ```

2. **Zep记忆功能异常**
   ```
   解决方案：
   - 验证ZEP_API_KEY有效性
   - 检查用户和会话是否正确创建
   - 确认网络连接正常
   ```

3. **多模态功能不可用**
   ```
   解决方案：
   - 确认使用支持多模态的模型端点
   - 检查图片URL是否可访问
   - 验证图片格式是否支持
   ```

### 调试技巧

1. 启用详细日志：
```javascript
// 在环境变量中设置
DEBUG_MODE=true

// 在代码中使用
if (process.env.DEBUG_MODE === 'true') {
  console.log('详细调试信息:', response);
}
```

2. 测试API连接：
```bash
# 单独测试火山方舟连接
node test-volcengine-connection.js

# 单独测试Zep连接
node test-zep-connection.js
```

## 相关资源

- [火山方舟官方文档](https://www.volcengine.com/docs/82379)
- [Zep Cloud文档](https://help.getzep.com/)
- [OpenAI SDK文档](https://github.com/openai/openai-node)
- [火山方舟控制台](https://console.volcengine.com/ark/)

## 支持

如果您在使用过程中遇到问题：

1. 查看本文档的故障排除部分
2. 运行相关测试脚本验证配置
3. 查看官方文档和API参考
4. 在项目Issues中提交问题报告
