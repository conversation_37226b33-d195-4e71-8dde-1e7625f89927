#!/usr/bin/env node
// 测试LLM意图识别功能
require('dotenv').config();
const IntentRecognitionService = require('../src/services/intentRecognitionService');

async function main() {
  console.log('🚀 开始测试LLM意图识别功能...');
  console.log('=' .repeat(60));
  
  // 检查环境变量
  if (!process.env.ARK_API_KEY) {
    throw new Error('ARK_API_KEY 环境变量未设置');
  }
  
  console.log('✅ 环境变量检查通过');
  console.log(`🔑 ARK API Key: ${process.env.ARK_API_KEY.substring(0, 10)}...`);
  
  // 初始化意图识别服务
  const intentService = new IntentRecognitionService();
  
  // 测试消息
  const testMessages = [
    '你好',
    '畅游Pro的价格是多少？',
    '有什么流量套餐推荐吗？',
    '售后服务怎么样？',
    '今天天气真好',
    '我需要一个适合商务出差的路由器',
    '我想了解一下你们的产品',
    '我该怎么选择合适的路由器？',
    '我的路由器坏了怎么办？'
  ];
  
  // 测试每个消息
  for (const message of testMessages) {
    console.log(`\n📝 测试消息: "${message}"`);
    try {
      const result = await intentService.recognizeIntent(message);
      console.log(`   意图: ${result.intent}`);
      console.log(`   置信度: ${result.confidence.toFixed(2)}`);
      console.log(`   需要知识搜索: ${result.needsKnowledgeSearch}`);
      if (result.reasoning) {
        console.log(`   推理: ${result.reasoning}`);
      }
      if (result.searchKeywords && result.searchKeywords.length > 0) {
        console.log(`   搜索关键词: ${result.searchKeywords.join(', ')}`);
      }
      if (result.businessCategories && result.businessCategories.length > 0) {
        console.log(`   业务类别: ${result.businessCategories.join(', ')}`);
      }
    } catch (error) {
      console.log(`   ❌ 测试失败: ${error.message}`);
    }
    
    // 添加延迟，避免API限制
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log('\n✅ 测试完成!');
}

// 如果直接运行此脚本
if (require.main === module) {
  main().then(() => {
    console.log('\n👋 测试完成，脚本退出');
    process.exit(0);
  }).catch((error) => {
    console.error('\n💥 脚本执行失败:', error.message);
    process.exit(1);
  });
}

module.exports = main;
