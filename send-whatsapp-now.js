// 立即发送WhatsApp消息
require('dotenv').config();
const twilio = require('twilio');

async function sendWhatsAppMessage(recipientNumber) {
  console.log('📱 发送WhatsApp消息...\n');

  try {
    const accountSid = process.env.TWILIO_ACCOUNT_SID;
    const authToken = process.env.TWILIO_AUTH_TOKEN;
    const client = twilio(accountSid, authToken);

    // Twilio WhatsApp沙盒号码
    const whatsappFrom = 'whatsapp:+***********';
    
    // 格式化接收号码
    let whatsappTo;
    if (recipientNumber.startsWith('whatsapp:')) {
      whatsappTo = recipientNumber;
    } else if (recipientNumber.startsWith('+')) {
      whatsappTo = `whatsapp:${recipientNumber}`;
    } else {
      whatsappTo = `whatsapp:+${recipientNumber}`;
    }

    console.log('📋 消息详情:');
    console.log('   发送方:', whatsappFrom);
    console.log('   接收方:', whatsappTo);
    console.log('   时间:', new Date().toLocaleString());
    console.log('');

    // 发送消息
    console.log('🚀 正在发送消息...');
    
    const message = await client.messages.create({
      from: whatsappFrom,
      to: whatsappTo,
      body: `🎉 恭喜！Twilio WhatsApp沙盒设置成功！

✅ 您的号码已验证并加入沙盒
📱 双向消息通信已激活
🚀 API系统运行正常
⏰ 发送时间: ${new Date().toLocaleString()}

🔄 测试双向通信：
请回复任意消息到这个号码，我们的系统将记录您的回复。

💡 24小时会话窗口已开启，在此期间您可以自由发送消息。

感谢您测试我们的WhatsApp Business API！`
    });

    console.log('✅ WhatsApp消息发送成功!');
    console.log('   消息SID:', message.sid);
    console.log('   初始状态:', message.status);
    console.log('   消息内容长度:', message.body?.length || 0, '字符');
    console.log('');

    // 等待几秒后查询状态
    console.log('⏳ 等待消息状态更新...');
    
    setTimeout(async () => {
      try {
        const updatedMessage = await client.messages(message.sid).fetch();
        console.log('📊 消息状态更新:');
        console.log('   当前状态:', updatedMessage.status);
        console.log('   发送时间:', updatedMessage.dateSent || 'Pending');
        console.log('   价格:', updatedMessage.price || 'N/A', updatedMessage.priceUnit || '');
        
        if (updatedMessage.errorCode) {
          console.log('   错误代码:', updatedMessage.errorCode);
          console.log('   错误信息:', updatedMessage.errorMessage);
        }

        // 状态说明
        console.log('\n📱 状态说明:');
        switch (updatedMessage.status) {
          case 'queued':
            console.log('   📤 消息已排队，等待发送');
            break;
          case 'sending':
            console.log('   🔄 消息正在发送中');
            break;
          case 'sent':
            console.log('   ✅ 消息已发送到WhatsApp');
            break;
          case 'delivered':
            console.log('   📱 消息已送达到您的设备');
            break;
          case 'read':
            console.log('   👀 消息已被阅读');
            break;
          case 'failed':
            console.log('   ❌ 消息发送失败');
            break;
          case 'undelivered':
            console.log('   ⚠️  消息未能送达');
            break;
        }

        console.log('\n🎯 请检查您的WhatsApp应用查看消息!');
        
      } catch (statusError) {
        console.log('⚠️  状态查询失败:', statusError.message);
      }
    }, 5000);

    return message;

  } catch (error) {
    console.error('❌ WhatsApp消息发送失败:', error.message);
    
    if (error.code === 63016) {
      console.error('\n📋 可能的原因:');
      console.error('   1. 接收号码未加入Twilio WhatsApp沙盒');
      console.error('   2. 号码格式不正确');
      console.error('\n🔧 解决方案:');
      console.error('   1. 用WhatsApp添加联系人: +1 415 523 8886');
      console.error('   2. 发送消息: "join <sandbox-keyword>"');
      console.error('   3. 等待确认消息');
      console.error('   4. 确保号码格式正确 (+国家代码+号码)');
    } else if (error.code === 21211) {
      console.error('   错误: 无效的电话号码格式');
      console.error('   请使用格式: +1234567890 或 whatsapp:+1234567890');
    } else if (error.code === 20003) {
      console.error('   错误: Twilio认证失败');
    } else {
      console.error('   错误代码:', error.code);
      console.error('   详细信息:', error.moreInfo || 'N/A');
    }

    throw error;
  }
}

// 主函数
async function main() {
  console.log('📱 Twilio WhatsApp消息发送工具');
  console.log('=' .repeat(50));
  
  // 从命令行参数获取号码
  const recipientNumber = process.argv[2];
  
  if (!recipientNumber) {
    console.log('❌ 请提供接收方WhatsApp号码');
    console.log('\n使用方法:');
    console.log('   node send-whatsapp-now.js +1234567890');
    console.log('   node send-whatsapp-now.js 8613800138000');
    console.log('   node send-whatsapp-now.js whatsapp:+1234567890');
    console.log('\n💡 提示: 确保号码已加入Twilio WhatsApp沙盒');
    process.exit(1);
  }

  try {
    await sendWhatsAppMessage(recipientNumber);
  } catch (error) {
    console.log('\n❌ 发送失败，请检查设置后重试');
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = { sendWhatsAppMessage };
