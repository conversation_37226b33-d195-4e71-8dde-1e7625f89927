// 测试火山引擎TOS客户端
const VolcengineTOSClient = require('./volcengine-tos-client');

async function testTOSClient() {
  console.log('🧪 测试火山引擎TOS客户端');
  console.log('=' .repeat(50));
  
  try {
    // 1. 初始化客户端
    console.log('\n1️⃣ 初始化TOS客户端...');
    const tosClient = new VolcengineTOSClient();
    
    // 显示配置信息
    const config = tosClient.getConfig();
    console.log('📋 TOS配置:');
    console.log('   区域:', config.region);
    console.log('   端点:', config.endpoint);
    console.log('   存储桶:', config.bucket);
    
    // 2. 初始化存储桶
    console.log('\n2️⃣ 初始化存储桶...');
    const initResult = await tosClient.initialize();
    
    if (initResult) {
      console.log('✅ TOS客户端初始化成功');
    } else {
      console.log('❌ TOS客户端初始化失败');
      return;
    }
    
    // 3. 测试文件名生成
    console.log('\n3️⃣ 测试文件名生成...');
    const testUrl = 'https://api.twilio.com/test-image.jpg';
    const fileName1 = tosClient.generateFileName(testUrl, 'image/jpeg');
    const fileName2 = tosClient.generateFileName(testUrl, 'image/png');
    const fileName3 = tosClient.generateFileName(testUrl, 'image/gif');
    
    console.log('📝 生成的文件名:');
    console.log('   JPEG:', fileName1);
    console.log('   PNG:', fileName2);
    console.log('   GIF:', fileName3);
    
    // 4. 测试上传功能（使用测试数据）
    console.log('\n4️⃣ 测试上传功能...');
    const testImageData = Buffer.from('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==', 'base64');
    
    try {
      const uploadResult = await tosClient.uploadToTOS(
        testImageData,
        'test/test-image.png',
        'image/png'
      );
      
      console.log('✅ 测试上传成功:');
      console.log('   URL:', uploadResult.url);
      console.log('   Key:', uploadResult.key);
      console.log('   Bucket:', uploadResult.bucket);
      
    } catch (uploadError) {
      console.log('⚠️ 测试上传失败:', uploadError.message);
      console.log('   这可能是因为存储桶配置或权限问题');
    }
    
    console.log('\n✅ TOS客户端测试完成');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.error('错误详情:', error);
  }
}

// 测试Twilio认证下载（模拟）
async function testTwilioDownload() {
  console.log('\n🧪 测试Twilio媒体下载逻辑');
  console.log('-'.repeat(30));
  
  try {
    const tosClient = new VolcengineTOSClient();
    
    // 模拟Twilio配置
    const mockAccountSid = 'AC7657552c992e2a3737961532e7e609d1';
    const mockAuthToken = 'your_auth_token_here';
    const mockMediaUrl = 'https://httpbin.org/image/jpeg'; // 使用测试图片URL
    
    console.log('📋 模拟Twilio配置:');
    console.log('   Account SID:', mockAccountSid);
    console.log('   媒体URL:', mockMediaUrl);
    
    try {
      // 尝试下载测试图片
      const mediaFile = await tosClient.downloadTwilioMedia(
        mockMediaUrl,
        mockAccountSid,
        mockAuthToken
      );
      
      console.log('✅ 下载测试成功:');
      console.log('   文件大小:', mediaFile.size, 'bytes');
      console.log('   内容类型:', mediaFile.contentType);
      
    } catch (downloadError) {
      console.log('⚠️ 下载测试失败:', downloadError.message);
      console.log('   这是预期的，因为使用的是模拟凭据');
    }
    
  } catch (error) {
    console.error('❌ 下载测试失败:', error.message);
  }
}

// 主函数
async function main() {
  await testTOSClient();
  await testTwilioDownload();
  
  console.log('\n🎯 测试总结:');
  console.log('1. TOS客户端初始化 - 验证配置和连接');
  console.log('2. 存储桶管理 - 检查和创建存储桶');
  console.log('3. 文件名生成 - 唯一性和格式正确性');
  console.log('4. 上传功能 - 基本上传和URL生成');
  console.log('5. 下载逻辑 - Twilio认证下载流程');
}

main();
