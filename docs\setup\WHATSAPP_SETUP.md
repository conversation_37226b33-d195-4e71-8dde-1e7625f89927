# WhatsApp消息发送设置指南

## 🚀 快速开始

### 第一步：加入Twilio WhatsApp沙盒

1. **打开WhatsApp应用**

2. **添加Twilio沙盒号码**
   - 添加联系人：`****** 523 8886`
   - 联系人名称：`Twilio Sandbox`

3. **获取您的沙盒关键词**
   - 访问：https://console.twilio.com/us1/develop/sms/try-it-out/whatsapp-learn
   - 找到您的专属关键词（例如：`join <keyword>`）

4. **发送加入消息**
   - 向 `****** 523 8886` 发送：`join <your-keyword>`
   - 等待确认消息

5. **确认加入成功**
   - 您应该收到类似这样的确认消息：
   ```
   ✅ You are all set! You have joined the sandbox for +14155238886 
   and can start testing immediately.
   ```

### 第二步：发送测试消息

一旦您完成沙盒设置，就可以使用以下命令发送测试消息：

```bash
# 使用您的WhatsApp号码（包含国家代码）
node send-whatsapp-now.js +8613800138000

# 或者不带+号
node send-whatsapp-now.js 8613800138000

# 或者完整格式
node send-whatsapp-now.js whatsapp:+8613800138000
```

## 📱 支持的号码格式

- `+8613800138000` （推荐）
- `8613800138000`
- `whatsapp:+8613800138000`

## 🔧 故障排除

### 常见错误及解决方案

1. **错误代码 63016**
   ```
   The number +1234567890 is not a valid WhatsApp number 
   or not opted in to receive messages.
   ```
   **解决方案：**
   - 确保您的号码已加入沙盒
   - 重新发送 `join <keyword>` 消息
   - 等待确认消息

2. **错误代码 21211**
   ```
   The 'To' number is not a valid phone number.
   ```
   **解决方案：**
   - 检查号码格式
   - 确保包含国家代码
   - 使用 +86 开头（中国号码）

3. **消息状态为 'failed'**
   **可能原因：**
   - WhatsApp应用未安装或未激活
   - 号码未正确加入沙盒
   - 网络连接问题

### 检查沙盒状态

运行以下命令检查设置：
```bash
node check-whatsapp-sandbox.js
```

## 📊 消息状态说明

- `queued` - 消息已排队
- `sending` - 正在发送
- `sent` - 已发送到WhatsApp
- `delivered` - 已送达设备
- `read` - 已被阅读
- `failed` - 发送失败
- `undelivered` - 未能送达

## 🎯 测试步骤总结

1. ✅ 加入Twilio WhatsApp沙盒
2. ✅ 获取确认消息
3. ✅ 提供您的WhatsApp号码
4. ✅ 运行发送脚本
5. ✅ 检查WhatsApp应用

## 💡 提示

- 试用账户每月有免费的消息配额
- 沙盒环境仅支持已验证的号码
- 生产环境需要申请WhatsApp Business API
- 消息发送通常在几秒内完成

---

**准备好了吗？** 请提供您的WhatsApp号码，我将立即发送测试消息！
