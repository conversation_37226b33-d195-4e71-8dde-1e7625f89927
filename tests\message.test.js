const request = require('supertest');
const app = require('../src/app');
const { Message, Conversation } = require('../src/models');
const database = require('../src/config/database');

// 测试配置
const API_KEY = process.env.API_KEY || 'test-api-key';
const TEST_PHONE = '+1234567890';

describe('Message API Tests', () => {
  beforeAll(async () => {
    // 连接测试数据库
    process.env.MONGODB_URI = process.env.MONGODB_TEST_URI || 'mongodb://localhost:27017/twilio_messaging_test';
    await database.connect();
  });

  afterAll(async () => {
    // 清理测试数据并断开连接
    await Message.deleteMany({});
    await Conversation.deleteMany({});
    await database.disconnect();
  });

  beforeEach(async () => {
    // 每个测试前清理数据
    await Message.deleteMany({});
    await Conversation.deleteMany({});
  });

  describe('POST /api/messages/send', () => {
    it('should send a text message successfully', async () => {
      const messageData = {
        to: TEST_PHONE,
        body: 'Test message',
        campaignId: 'test-campaign'
      };

      const response = await request(app)
        .post('/api/messages/send')
        .set('X-API-Key', API_KEY)
        .send(messageData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('messageId');
      expect(response.body.data).toHaveProperty('twilioSid');
      expect(response.body.data).toHaveProperty('conversationId');
    });

    it('should send a message with media', async () => {
      const messageData = {
        to: TEST_PHONE,
        body: 'Test message with media',
        mediaUrls: ['https://example.com/image.jpg'],
        campaignId: 'test-campaign'
      };

      const response = await request(app)
        .post('/api/messages/send')
        .set('X-API-Key', API_KEY)
        .send(messageData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('messageId');
    });

    it('should fail without authentication', async () => {
      const messageData = {
        to: TEST_PHONE,
        body: 'Test message'
      };

      const response = await request(app)
        .post('/api/messages/send')
        .send(messageData)
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Authentication required');
    });

    it('should fail with invalid phone number', async () => {
      const messageData = {
        to: 'invalid-phone',
        body: 'Test message'
      };

      const response = await request(app)
        .post('/api/messages/send')
        .set('X-API-Key', API_KEY)
        .send(messageData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Validation error');
    });

    it('should fail without message content', async () => {
      const messageData = {
        to: TEST_PHONE
      };

      const response = await request(app)
        .post('/api/messages/send')
        .set('X-API-Key', API_KEY)
        .send(messageData)
        .expect(400);

      expect(response.body.success).toBe(false);
    });
  });

  describe('POST /api/messages/send-bulk', () => {
    it('should send multiple messages successfully', async () => {
      const bulkData = {
        messages: [
          {
            to: '+1234567890',
            body: 'Message 1'
          },
          {
            to: '+0987654321',
            body: 'Message 2'
          }
        ]
      };

      const response = await request(app)
        .post('/api/messages/send-bulk')
        .set('X-API-Key', API_KEY)
        .send(bulkData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.success).toBe(2);
      expect(response.body.data.failed).toBe(0);
    });

    it('should fail with empty messages array', async () => {
      const bulkData = {
        messages: []
      };

      const response = await request(app)
        .post('/api/messages/send-bulk')
        .set('X-API-Key', API_KEY)
        .send(bulkData)
        .expect(400);

      expect(response.body.success).toBe(false);
    });
  });

  describe('GET /api/messages/history', () => {
    beforeEach(async () => {
      // 创建测试消息
      await Message.create({
        direction: 'outbound',
        from: '+0987654321',
        to: TEST_PHONE,
        body: 'Test message 1',
        status: 'sent',
        conversationId: 'test-conv-1'
      });

      await Message.create({
        direction: 'inbound',
        from: TEST_PHONE,
        to: '+0987654321',
        body: 'Test reply',
        status: 'received',
        conversationId: 'test-conv-1'
      });
    });

    it('should get message history for phone number', async () => {
      const response = await request(app)
        .get(`/api/messages/history?phoneNumber=${TEST_PHONE}`)
        .set('X-API-Key', API_KEY)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.messages).toHaveLength(2);
      expect(response.body.data.pagination).toHaveProperty('limit');
      expect(response.body.data.pagination).toHaveProperty('offset');
    });

    it('should support pagination', async () => {
      const response = await request(app)
        .get(`/api/messages/history?phoneNumber=${TEST_PHONE}&limit=1&offset=0`)
        .set('X-API-Key', API_KEY)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.messages).toHaveLength(1);
      expect(response.body.data.pagination.limit).toBe(1);
    });

    it('should fail with invalid phone number format', async () => {
      const response = await request(app)
        .get('/api/messages/history?phoneNumber=invalid')
        .set('X-API-Key', API_KEY)
        .expect(400);

      expect(response.body.success).toBe(false);
    });
  });

  describe('GET /api/messages/conversation/:conversationId', () => {
    beforeEach(async () => {
      // 创建测试会话消息
      await Message.create({
        direction: 'outbound',
        from: '+0987654321',
        to: TEST_PHONE,
        body: 'Conversation message 1',
        status: 'sent',
        conversationId: 'test-conv-123'
      });

      await Message.create({
        direction: 'inbound',
        from: TEST_PHONE,
        to: '+0987654321',
        body: 'Conversation reply',
        status: 'received',
        conversationId: 'test-conv-123'
      });
    });

    it('should get conversation messages', async () => {
      const response = await request(app)
        .get('/api/messages/conversation/test-conv-123')
        .set('X-API-Key', API_KEY)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.messages).toHaveLength(2);
    });

    it('should return empty array for non-existent conversation', async () => {
      const response = await request(app)
        .get('/api/messages/conversation/non-existent')
        .set('X-API-Key', API_KEY)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.messages).toHaveLength(0);
    });
  });
});
