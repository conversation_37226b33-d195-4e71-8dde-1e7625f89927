// Coze API 客户端
const axios = require('axios');

class CozeApiClient {
  constructor(config) {
    this.botId = config.botId;
    this.accessToken = config.accessToken;
    this.baseUrl = config.baseUrl || 'https://api.coze.cn';
    this.apiVersion = config.apiVersion || 'v3';
    this.conversations = {};
  }

  // 创建API请求头
  getHeaders() {
    return {
      'Authorization': `Bearer ${this.accessToken}`,
      'Content-Type': 'application/json'
    };
  }

  // 获取API URL
  getApiUrl(endpoint) {
    return `${this.baseUrl}/${this.apiVersion}/${endpoint}`;
  }

  // 发送消息到Coze机器人
  async sendMessage(message, userId, conversationId = null) {
    try {
      // 如果没有提供conversationId，使用userId作为键获取或创建新的会话
      if (!conversationId) {
        conversationId = this.conversations[userId] || null;
      }

      const endpoint = 'chat';
      const url = this.getApiUrl(endpoint);
      
      const payload = {
        bot_id: this.botId,
        user_id: userId,
        query: message,
        stream: false,
        chat_history: []
      };

      if (conversationId) {
        payload.conversation_id = conversationId;
      }

      console.log(`🤖 发送消息到Coze机器人 (用户: ${userId}):`);
      console.log(`   消息: ${message}`);
      if (conversationId) {
        console.log(`   会话ID: ${conversationId}`);
      }

      const response = await axios.post(url, payload, {
        headers: this.getHeaders()
      });

      console.log('🔍 Coze API初始响应:', JSON.stringify(response.data, null, 2));

      // 检查是否需要轮询结果
      if (response.data.data && response.data.data.status === 'in_progress') {
        const chatId = response.data.data.id;
        const conversationId = response.data.data.conversation_id;

        console.log(`⏳ 等待Coze处理完成 (Chat ID: ${chatId})...`);

        // 轮询获取最终结果
        const finalResult = await this.pollChatResult(chatId, conversationId);

        // 保存会话ID
        if (conversationId) {
          this.conversations[userId] = conversationId;
          console.log(`   会话ID: ${conversationId}`);
        }

        return finalResult;
      }

      // 如果是同步响应，直接处理
      let replyText = '';
      if (response.data.messages && response.data.messages.length > 0) {
        const lastMessage = response.data.messages[response.data.messages.length - 1];
        replyText = lastMessage.content || lastMessage.text || '';
      } else if (response.data.content) {
        replyText = response.data.content;
      } else if (response.data.text) {
        replyText = response.data.text;
      } else {
        replyText = 'API连接成功，但未获取到回复内容';
      }

      console.log(`✅ Coze回复: ${replyText.substring(0, 50)}...`);

      return {
        ...response.data,
        text: replyText,
        conversation_id: response.data.conversation_id
      };

    } catch (error) {
      console.error('❌ Coze API调用失败:', error.message);
      if (error.response) {
        console.error('   状态码:', error.response.status);
        console.error('   响应数据:', error.response.data);
      }
      throw error;
    }
  }

  // 轮询获取聊天结果
  async pollChatResult(chatId, conversationId, maxAttempts = 30, interval = 2000) {
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        console.log(`   轮询第 ${attempt} 次...`);

        const endpoint = `chat/retrieve?chat_id=${chatId}&conversation_id=${conversationId}`;
        const url = this.getApiUrl(endpoint);

        const response = await axios.get(url, {
          headers: this.getHeaders()
        });

        console.log(`🔍 轮询响应 (第${attempt}次):`, JSON.stringify(response.data, null, 2));

        if (response.data.data && response.data.data.status === 'completed') {
          // 获取消息内容
          const messages = response.data.data.messages || [];
          let replyText = '';

          // 查找机器人的回复
          for (const message of messages.reverse()) {
            if (message.role === 'assistant' && message.content) {
              replyText = message.content;
              break;
            }
          }

          if (!replyText && messages.length > 0) {
            const lastMessage = messages[messages.length - 1];
            replyText = lastMessage.content || lastMessage.text || '';
          }

          console.log(`✅ Coze处理完成，回复: ${replyText.substring(0, 50)}...`);

          return {
            ...response.data.data,
            text: replyText,
            conversation_id: conversationId
          };
        }

        if (response.data.data && response.data.data.status === 'failed') {
          throw new Error(`Coze处理失败: ${response.data.data.last_error?.msg || 'Unknown error'}`);
        }

        // 如果还在处理中，等待后继续轮询
        if (attempt < maxAttempts) {
          await new Promise(resolve => setTimeout(resolve, interval));
        }

      } catch (error) {
        if (attempt === maxAttempts) {
          throw error;
        }
        console.log(`   轮询第 ${attempt} 次失败，继续尝试...`);
        await new Promise(resolve => setTimeout(resolve, interval));
      }
    }

    throw new Error(`轮询超时: 在 ${maxAttempts} 次尝试后仍未获得结果`);
  }

  // 获取会话历史
  async getConversationHistory(conversationId) {
    try {
      const endpoint = `conversations/${conversationId}/history`;
      const url = this.getApiUrl(endpoint);

      console.log(`📚 获取会话历史 (会话ID: ${conversationId})`);

      const response = await axios.get(url, {
        headers: this.getHeaders()
      });

      console.log(`✅ 获取到 ${response.data.messages.length} 条历史消息`);
      return response.data;

    } catch (error) {
      console.error('❌ 获取会话历史失败:', error.message);
      throw error;
    }
  }

  // 创建新会话
  async createConversation(userId) {
    try {
      const endpoint = 'conversations';
      const url = this.getApiUrl(endpoint);
      
      const payload = {
        bot_id: this.botId,
        user_id: userId
      };

      console.log(`🆕 为用户 ${userId} 创建新会话`);

      const response = await axios.post(url, payload, {
        headers: this.getHeaders()
      });

      const conversationId = response.data.conversation_id;
      this.conversations[userId] = conversationId;
      
      console.log(`✅ 新会话创建成功: ${conversationId}`);
      return conversationId;

    } catch (error) {
      console.error('❌ 创建会话失败:', error.message);
      throw error;
    }
  }

  // 获取用户的会话ID
  getUserConversationId(userId) {
    return this.conversations[userId] || null;
  }

  // 设置用户的会话ID
  setUserConversationId(userId, conversationId) {
    this.conversations[userId] = conversationId;
    return conversationId;
  }

  // 清除用户的会话ID（开始新会话）
  clearUserConversationId(userId) {
    delete this.conversations[userId];
    return null;
  }

  // 检查API连接
  async checkConnection() {
    try {
      // 尝试简单的API调用来验证连接
      const testMessage = "Hello, this is a connection test.";
      const testUserId = "connection_test_user";
      
      console.log('🔄 测试Coze API连接...');
      
      const response = await this.sendMessage(testMessage, testUserId);
      
      console.log('✅ Coze API连接成功!');
      console.log(`   机器人ID: ${this.botId}`);
      console.log(`   测试响应: ${response.text.substring(0, 50)}...`);
      
      // 清除测试会话
      this.clearUserConversationId(testUserId);
      
      return {
        success: true,
        botId: this.botId,
        response: response.text
      };

    } catch (error) {
      console.error('❌ Coze API连接失败:', error.message);
      return {
        success: false,
        error: error.message,
        details: error.response?.data || null
      };
    }
  }
}

module.exports = CozeApiClient;
