// 设置Twilio电话号码
require('dotenv').config();
const twilio = require('twilio');

async function setupTwilioNumber() {
  console.log('📞 设置Twilio电话号码...\n');

  try {
    const accountSid = process.env.TWILIO_ACCOUNT_SID;
    const authToken = process.env.TWILIO_AUTH_TOKEN;
    const client = twilio(accountSid, authToken);

    // 1. 检查现有电话号码
    console.log('1. 检查现有电话号码...');
    const existingNumbers = await client.incomingPhoneNumbers.list();
    
    if (existingNumbers.length > 0) {
      console.log('✅ 找到现有电话号码:');
      existingNumbers.forEach((number, index) => {
        console.log(`   ${index + 1}. ${number.phoneNumber} (${number.friendlyName || 'No name'})`);
        console.log(`      SID: ${number.sid}`);
        console.log(`      功能: SMS=${number.capabilities.sms}, Voice=${number.capabilities.voice}`);
      });
      
      const firstNumber = existingNumbers[0];
      console.log(`\n💡 建议使用: ${firstNumber.phoneNumber}`);
      console.log('   请更新.env文件中的TWILIO_PHONE_NUMBER');
      return;
    }

    console.log('⚠️  未找到现有电话号码，查找可购买的号码...\n');

    // 2. 搜索可用的电话号码
    console.log('2. 搜索可用的电话号码...');
    
    // 搜索美国的电话号码
    const availableNumbers = await client.availablePhoneNumbers('US')
      .local
      .list({
        smsEnabled: true,
        voiceEnabled: true,
        limit: 5
      });

    if (availableNumbers.length === 0) {
      console.log('❌ 未找到可用的电话号码');
      console.log('   请手动在Twilio控制台购买电话号码');
      return;
    }

    console.log('✅ 找到可用电话号码:');
    availableNumbers.forEach((number, index) => {
      console.log(`   ${index + 1}. ${number.phoneNumber}`);
      console.log(`      地区: ${number.locality}, ${number.region}`);
      console.log(`      功能: SMS=${number.capabilities.sms}, Voice=${number.capabilities.voice}`);
    });

    // 3. 询问是否购买（在试用账户中）
    console.log('\n3. 试用账户电话号码购买...');
    console.log('⚠️  注意: 这是试用账户，购买电话号码可能需要验证');
    console.log('   建议手动在Twilio控制台完成购买流程');
    
    const selectedNumber = availableNumbers[0];
    console.log(`\n💡 推荐号码: ${selectedNumber.phoneNumber}`);
    console.log('\n📋 手动购买步骤:');
    console.log('   1. 访问: https://console.twilio.com/us1/develop/phone-numbers/manage/search');
    console.log('   2. 搜索并购买电话号码');
    console.log('   3. 配置Webhook URL（如果需要）');
    console.log(`   4. 更新.env文件: TWILIO_PHONE_NUMBER=${selectedNumber.phoneNumber}`);

    // 4. 显示Webhook配置信息
    console.log('\n📡 Webhook配置（可选）:');
    console.log('   消息状态回调: http://your-domain.com/webhook/message-status');
    console.log('   入站消息处理: http://your-domain.com/webhook/incoming-message');
    console.log('   注意: 需要公网可访问的URL');

  } catch (error) {
    console.error('❌ 设置失败:', error.message);
    
    if (error.code === 21608) {
      console.error('   错误: 试用账户限制 - 需要升级账户才能购买电话号码');
      console.error('   解决方案: 在Twilio控制台升级账户或添加信用卡');
    } else if (error.code === 20003) {
      console.error('   错误: 认证失败 - 请检查凭据');
    } else {
      console.error('   错误代码:', error.code);
      console.error('   详细信息:', error.moreInfo || 'N/A');
    }
  }
}

setupTwilioNumber();
