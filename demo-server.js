// 演示服务器 - 不依赖外部服务
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
require('dotenv').config();

const app = express();

// 安全中间件
app.use(helmet());
app.use(cors());

// 请求解析
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 简单的认证中间件
const authMiddleware = (req, res, next) => {
  const apiKey = req.header('X-API-Key') || req.query.apiKey;
  
  if (!apiKey) {
    return res.status(401).json({
      success: false,
      error: 'Authentication required. Provide either API key or JWT token',
    });
  }

  if (apiKey !== 'demo_api_key_12345') {
    return res.status(401).json({
      success: false,
      error: 'Invalid API key',
    });
  }

  next();
};

// 健康检查
app.get('/health', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'Twilio Business Messaging API is healthy',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    environment: 'demo',
    status: {
      database: 'simulated',
      twilio: 'simulated'
    }
  });
});

// API信息
app.get('/api', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'Twilio Business Messaging API - Demo Mode',
    version: '1.0.0',
    documentation: '/docs',
    endpoints: {
      messages: '/api/messages',
      status: '/api/status',
      webhooks: '/webhook',
      health: '/health',
    },
    note: 'This is a demo server with simulated responses'
  });
});

// 模拟消息发送
app.post('/api/messages/send', authMiddleware, (req, res) => {
  const { to, body, mediaUrls, campaignId } = req.body;

  // 基本验证
  if (!to) {
    return res.status(400).json({
      success: false,
      error: 'Validation error',
      details: 'Recipient phone number is required'
    });
  }

  if (!body && (!mediaUrls || mediaUrls.length === 0)) {
    return res.status(400).json({
      success: false,
      error: 'Validation error',
      details: 'Message body or media is required'
    });
  }

  // 模拟成功响应
  const mockResponse = {
    success: true,
    data: {
      messageId: '60f7b3b3b3b3b3b3b3b3b3b3',
      twilioSid: 'SM_demo_' + Date.now(),
      status: 'queued',
      conversationId: 'conv_demo_' + Math.random().toString(36).substr(2, 9),
      to: to,
      body: body,
      mediaUrls: mediaUrls || [],
      campaignId: campaignId,
      timestamp: new Date().toISOString()
    }
  };

  console.log('📤 模拟消息发送:', { to, body: body?.substring(0, 50) + '...' });
  res.status(200).json(mockResponse);
});

// 模拟批量发送
app.post('/api/messages/send-bulk', authMiddleware, (req, res) => {
  const { messages } = req.body;

  if (!messages || !Array.isArray(messages) || messages.length === 0) {
    return res.status(400).json({
      success: false,
      error: 'Validation error',
      details: 'Messages array is required and cannot be empty'
    });
  }

  const results = messages.map((msg, index) => ({
    messageId: `60f7b3b3b3b3b3b3b3b3b3b${index}`,
    twilioSid: `SM_demo_bulk_${Date.now()}_${index}`,
    status: 'queued',
    to: msg.to
  }));

  console.log('📤 模拟批量发送:', messages.length, '条消息');
  res.status(200).json({
    success: true,
    data: {
      success: results.length,
      failed: 0,
      results: results,
      errors: []
    }
  });
});

// 模拟消息状态查询
app.get('/api/status/message/:messageId', authMiddleware, (req, res) => {
  const { messageId } = req.params;

  const mockStatus = {
    success: true,
    data: {
      messageId: messageId,
      twilioSid: 'SM_demo_' + messageId.slice(-8),
      status: 'delivered',
      direction: 'outbound',
      from: '+15551234567',
      to: '+1234567890',
      sentAt: new Date(Date.now() - 60000).toISOString(),
      deliveredAt: new Date().toISOString(),
      price: '0.0075',
      priceUnit: 'USD',
      createdAt: new Date(Date.now() - 120000).toISOString(),
      updatedAt: new Date().toISOString()
    }
  };

  console.log('📊 模拟状态查询:', messageId);
  res.status(200).json(mockStatus);
});

// 模拟传递报告
app.get('/api/status/delivery-report', authMiddleware, (req, res) => {
  const mockReport = {
    success: true,
    data: {
      summary: {
        totalMessages: 150,
        deliveryRate: 94.67,
        failureRate: 5.33,
        totalCost: '1.1250'
      },
      statusBreakdown: {
        delivered: { count: 142, percentage: '94.67', totalPrice: 1.065 },
        failed: { count: 5, percentage: '3.33', totalPrice: 0.0375 },
        undelivered: { count: 3, percentage: '2.00', totalPrice: 0.0225 }
      },
      filters: req.query,
      generatedAt: new Date().toISOString()
    }
  };

  console.log('📈 模拟传递报告生成');
  res.status(200).json(mockReport);
});

// Webhook健康检查
app.get('/webhook/health', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'Webhook endpoint is healthy',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// 模拟webhook接收
app.post('/webhook/message-status', (req, res) => {
  console.log('📥 模拟接收Webhook:', req.body);
  res.status(200).json({ success: true });
});

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: `Route ${req.originalUrl} not found`,
    availableEndpoints: [
      'GET /health',
      'GET /api',
      'POST /api/messages/send',
      'POST /api/messages/send-bulk',
      'GET /api/status/message/:messageId',
      'GET /api/status/delivery-report',
      'GET /webhook/health'
    ]
  });
});

// 错误处理
app.use((err, req, res, next) => {
  console.error('Error:', err);
  res.status(500).json({
    success: false,
    error: 'Internal server error',
    message: err.message
  });
});

// 启动服务器
const PORT = process.env.PORT || 3000;
const server = app.listen(PORT, () => {
  console.log(`🚀 Demo Server running on port ${PORT}`);
  console.log(`📚 API Documentation: http://localhost:${PORT}/docs`);
  console.log(`🏥 Health Check: http://localhost:${PORT}/health`);
  console.log(`🔑 Demo API Key: demo_api_key_12345`);
  console.log(`\n💡 This is a demo server with simulated responses`);
  console.log(`   Real Twilio integration requires valid credentials`);
});

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('Received SIGTERM, shutting down gracefully');
  server.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('Received SIGINT, shutting down gracefully');
  server.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
});

module.exports = app;
