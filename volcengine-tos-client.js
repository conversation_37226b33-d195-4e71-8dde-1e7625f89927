// 火山引擎对象存储客户端
const { TosClient } = require('@volcengine/tos-sdk');
const axios = require('axios');
const crypto = require('crypto');
const path = require('path');

class VolcengineTOSClient {
  constructor() {
    // 火山引擎TOS配置
    this.config = {
      accessKeyId: 'AKLTODQxYzI1ZjhiYTFlNGQxOTliZWI5MGQxMDNlODA4YTk',
      accessKeySecret: 'TldJNU4yUmxNMlJpTUdVd05HTTVZV0ZsWkRBeE5HWmlOell4TmpsbU1tTQ==',
      region: 'cn-beijing', // 华北2（北京）
      endpoint: 'tos-cn-beijing.volces.com' // 华北2（北京）端点（不包含协议）
    };

    // 存储桶名称
    this.bucketName = 'whatsapp'; // 已创建的存储桶
    
    // 初始化TOS客户端
    this.tosClient = new TosClient({
      accessKeyId: this.config.accessKeyId,
      accessKeySecret: this.config.accessKeySecret,
      region: this.config.region,
      endpoint: this.config.endpoint
    });
    
    console.log('🔥 火山引擎TOS客户端初始化完成');
  }

  // 从Twilio下载媒体文件
  async downloadTwilioMedia(mediaUrl, accountSid, authToken) {
    try {
      console.log('📥 开始下载Twilio媒体文件...');
      console.log('   媒体URL:', mediaUrl);
      
      // 使用Basic Auth下载文件
      const response = await axios.get(mediaUrl, {
        auth: {
          username: accountSid,
          password: authToken
        },
        responseType: 'arraybuffer',
        timeout: 30000
      });
      
      console.log('✅ Twilio媒体文件下载成功');
      console.log('   文件大小:', response.data.length, 'bytes');
      console.log('   内容类型:', response.headers['content-type']);
      
      return {
        data: response.data,
        contentType: response.headers['content-type'] || 'image/jpeg',
        size: response.data.length
      };
      
    } catch (error) {
      console.error('❌ 下载Twilio媒体文件失败:', error.message);
      throw new Error(`下载媒体文件失败: ${error.message}`);
    }
  }

  // 生成唯一的文件名
  generateFileName(originalUrl, contentType) {
    const timestamp = Date.now();
    const randomStr = crypto.randomBytes(8).toString('hex');
    
    // 根据内容类型确定文件扩展名
    let extension = '.jpg';
    if (contentType) {
      if (contentType.includes('png')) extension = '.png';
      else if (contentType.includes('gif')) extension = '.gif';
      else if (contentType.includes('webp')) extension = '.webp';
    }
    
    return `whatsapp-media/${timestamp}-${randomStr}${extension}`;
  }

  // 上传文件到火山引擎TOS
  async uploadToTOS(fileData, fileName, contentType) {
    try {
      console.log('☁️ 开始上传到火山引擎TOS...');
      console.log('   文件名:', fileName);
      console.log('   内容类型:', contentType);
      console.log('   文件大小:', fileData.length, 'bytes');
      
      const uploadParams = {
        bucket: this.bucketName,
        key: fileName,
        body: fileData,
        contentType: contentType
      };
      
      const result = await this.tosClient.putObject(uploadParams);
      
      // 构建公开访问URL
      const publicUrl = `https://${this.bucketName}.tos-${this.config.region}.volces.com/${fileName}`;
      
      console.log('✅ 文件上传到TOS成功');
      console.log('   ETag:', result.ETag);
      console.log('   公开URL:', publicUrl);
      
      return {
        url: publicUrl,
        etag: result.ETag,
        key: fileName,
        bucket: this.bucketName
      };
      
    } catch (error) {
      console.error('❌ 上传到TOS失败:', error.message);
      throw new Error(`上传到对象存储失败: ${error.message}`);
    }
  }

  // 完整的媒体处理流程：下载 → 上传 → 返回公开URL
  async processWhatsAppMedia(mediaUrl, accountSid, authToken) {
    try {
      console.log('🔄 开始处理WhatsApp媒体文件...');
      console.log('=' .repeat(60));
      
      // 1. 从Twilio下载媒体文件
      const mediaFile = await this.downloadTwilioMedia(mediaUrl, accountSid, authToken);
      
      // 2. 生成唯一文件名
      const fileName = this.generateFileName(mediaUrl, mediaFile.contentType);
      
      // 3. 上传到火山引擎TOS
      const uploadResult = await this.uploadToTOS(
        mediaFile.data, 
        fileName, 
        mediaFile.contentType
      );
      
      console.log('🎉 媒体文件处理完成');
      console.log('   原始URL:', mediaUrl.substring(0, 50) + '...');
      console.log('   公开URL:', uploadResult.url);
      console.log('=' .repeat(60));
      
      return {
        originalUrl: mediaUrl,
        publicUrl: uploadResult.url,
        fileName: fileName,
        contentType: mediaFile.contentType,
        size: mediaFile.size,
        bucket: uploadResult.bucket,
        key: uploadResult.key
      };
      
    } catch (error) {
      console.error('❌ 媒体文件处理失败:', error.message);
      throw error;
    }
  }

  // 检查存储桶是否存在，不存在则创建
  async ensureBucketExists() {
    try {
      console.log('🔍 检查存储桶是否存在...');

      // 尝试列出存储桶中的对象来验证访问权限
      await this.tosClient.listObjects({
        bucket: this.bucketName,
        maxKeys: 1
      });
      console.log('✅ 存储桶已存在且可访问:', this.bucketName);

    } catch (error) {
      console.log('⚠️ 存储桶检查失败:', error.message);
      console.log('   继续使用存储桶（可能已存在但权限不同）');
      // 不抛出错误，因为存储桶可能已存在但权限设置不同
    }
  }

  // 初始化方法
  async initialize() {
    try {
      console.log('🚀 初始化火山引擎TOS客户端...');
      // 跳过存储桶检查，因为存储桶已经存在
      console.log('✅ 使用已存在的存储桶:', this.bucketName);
      console.log('✅ TOS客户端初始化完成');
      return true;
    } catch (error) {
      console.error('❌ TOS客户端初始化失败:', error.message);
      return false;
    }
  }

  // 获取配置信息
  getConfig() {
    return {
      region: this.config.region,
      endpoint: this.config.endpoint,
      bucket: this.bucketName
    };
  }
}

module.exports = VolcengineTOSClient;
