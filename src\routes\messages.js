const express = require('express');
const messageController = require('../controllers/messageController');
const authMiddleware = require('../middleware/auth');
const rateLimitMiddleware = require('../middleware/rateLimit');

const router = express.Router();

// 应用认证和限流中间件
router.use(authMiddleware);
router.use(rateLimitMiddleware);

/**
 * @route POST /api/messages/send
 * @desc 发送单条消息
 * @access Private
 */
router.post('/send', messageController.sendMessage);

/**
 * @route POST /api/messages/send-bulk
 * @desc 批量发送消息
 * @access Private
 */
router.post('/send-bulk', messageController.sendBulkMessages);

/**
 * @route GET /api/messages/history
 * @desc 获取消息历史
 * @access Private
 * @query phoneNumber - 电话号码
 * @query limit - 限制数量 (默认50)
 * @query offset - 偏移量 (默认0)
 */
router.get('/history', messageController.getMessageHistory);

/**
 * @route GET /api/messages/conversation/:conversationId
 * @desc 获取会话消息
 * @access Private
 * @param conversationId - 会话ID
 * @query limit - 限制数量 (默认50)
 * @query offset - 偏移量 (默认0)
 */
router.get('/conversation/:conversationId', messageController.getConversationMessages);

module.exports = router;
