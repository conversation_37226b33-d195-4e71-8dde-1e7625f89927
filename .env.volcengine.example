# 火山方舟 (Volcengine) 配置示例
# 请复制此文件内容到您的 .env 文件中

# ===========================================
# 火山方舟 API 配置
# ===========================================

# 火山方舟 API 密钥 (必需)
# 获取方式: https://console.volcengine.com/ark/region:ark+cn-beijing/apiKey
ARK_API_KEY=your_ark_api_key_here

# 火山方舟模型端点ID (必需)
# 这是您在火山方舟控制台创建的模型端点ID
# 格式通常为: ep-xxxxxxxxxxxxxx-xxxxxx
VOLCENGINE_MODEL_ENDPOINT=ep-20241221105607-2w8zx

# 火山方舟访问密钥ID (可选，用于其他火山引擎服务)
VOLCENGINE_ACCESS_KEY_ID=your_access_key_id_here

# 火山方舟访问密钥 (可选，用于其他火山引擎服务)
VOLCENGINE_SECRET_ACCESS_KEY=your_secret_access_key_here

# 火山方舟地域 (可选，默认为 cn-beijing)
VOLCENGINE_REGION=cn-beijing

# ===========================================
# Zep Cloud 配置 (已存在)
# ===========================================

# Zep API 密钥 (必需)
ZEP_API_KEY=your_zep_api_key_here

# ===========================================
# 测试配置
# ===========================================

# 测试超时时间 (毫秒)
TEST_TIMEOUT=60000

# 是否启用详细日志
DEBUG_MODE=false

# ===========================================
# 使用说明
# ===========================================

# 1. 获取火山方舟 API 密钥:
#    - 访问: https://console.volcengine.com/ark/
#    - 登录您的火山引擎账户
#    - 进入 "API密钥管理"
#    - 创建新的API密钥

# 2. 创建模型端点:
#    - 在火山方舟控制台选择模型 (如: 豆包-pro-4k)
#    - 创建推理端点
#    - 复制端点ID到 VOLCENGINE_MODEL_ENDPOINT

# 3. 配置完成后运行测试:
#    npm run test:volcengine-zep
#    npm run demo:volcengine-zep

# ===========================================
# 常用模型端点示例
# ===========================================

# 豆包-pro-4k (推荐用于对话)
# VOLCENGINE_MODEL_ENDPOINT=ep-20241221105607-2w8zx

# 豆包-pro-32k (长文本处理)
# VOLCENGINE_MODEL_ENDPOINT=ep-xxxxxxxxxxxxxx-xxxxxx

# 豆包-lite-4k (轻量级，快速响应)
# VOLCENGINE_MODEL_ENDPOINT=ep-xxxxxxxxxxxxxx-xxxxxx

# ===========================================
# 注意事项
# ===========================================

# 1. 请妥善保管您的API密钥，不要提交到版本控制系统
# 2. 火山方舟按调用次数和token数量计费，请注意使用量
# 3. 不同模型有不同的能力和价格，请根据需求选择
# 4. 多模态功能 (图像分析) 需要支持的模型端点
# 5. 建议在生产环境中使用环境变量而不是 .env 文件
