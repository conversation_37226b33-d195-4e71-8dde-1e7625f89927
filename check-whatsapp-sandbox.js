// 检查WhatsApp沙盒设置
require('dotenv').config();
const twilio = require('twilio');

async function checkWhatsAppSandbox() {
  console.log('📱 检查WhatsApp沙盒设置...\n');

  try {
    const accountSid = process.env.TWILIO_ACCOUNT_SID;
    const authToken = process.env.TWILIO_AUTH_TOKEN;
    const client = twilio(accountSid, authToken);

    // 1. 获取账户信息
    console.log('1. 验证账户信息...');
    const account = await client.api.accounts(accountSid).fetch();
    console.log('✅ 账户验证成功');
    console.log('   账户类型:', account.type);
    console.log('   账户状态:', account.status);
    console.log('');

    // 2. 检查WhatsApp功能
    console.log('2. 检查WhatsApp功能可用性...');
    
    try {
      // 尝试获取WhatsApp相关信息
      const services = await client.messaging.services.list({ limit: 1 });
      console.log('✅ 消息服务可用');
    } catch (serviceError) {
      console.log('⚠️  消息服务检查:', serviceError.message);
    }

    // 3. 显示沙盒信息
    console.log('3. WhatsApp沙盒信息...');
    console.log('📋 Twilio WhatsApp沙盒设置:');
    console.log('   沙盒号码: +1 415 523 8886');
    console.log('   格式: whatsapp:+***********');
    console.log('');

    // 4. 显示设置步骤
    console.log('4. 沙盒设置步骤:');
    console.log('   步骤1: 打开WhatsApp应用');
    console.log('   步骤2: 添加联系人 +1 415 523 8886');
    console.log('   步骤3: 发送消息 "join <your-sandbox-keyword>"');
    console.log('   步骤4: 等待确认消息');
    console.log('');

    // 5. 获取沙盒关键词
    console.log('5. 获取您的沙盒关键词...');
    console.log('💡 请访问Twilio控制台获取您的专属关键词:');
    console.log('   URL: https://console.twilio.com/us1/develop/sms/try-it-out/whatsapp-learn');
    console.log('');

    // 6. 测试消息格式
    console.log('6. 消息格式示例:');
    console.log('   发送方: whatsapp:+***********');
    console.log('   接收方: whatsapp:+您的手机号码');
    console.log('   示例: whatsapp:+8613800138000');
    console.log('');

    console.log('🎯 下一步操作:');
    console.log('   1. 完成WhatsApp沙盒设置');
    console.log('   2. 更新 test-whatsapp-message.js 中的接收号码');
    console.log('   3. 运行: node test-whatsapp-message.js');

  } catch (error) {
    console.error('❌ 检查失败:', error.message);
    console.error('   错误代码:', error.code);
    console.error('   详细信息:', error.moreInfo || 'N/A');
  }
}

checkWhatsAppSandbox();
