// 测试错误处理机制
const smartReplySystem = require('./smart-reply-system');

async function testErrorHandling() {
  console.log('🧪 测试错误处理机制');
  console.log('=' .repeat(50));
  
  const userId = 'error_test_user';
  
  try {
    // 测试1: 模拟图片消息（可能触发API错误）
    console.log('\n1️⃣ 测试图片消息错误处理');
    console.log('👤 用户: [发送图片消息]');
    
    const mediaInfo = {
      numMedia: 1,
      mediaUrl: 'https://api.twilio.com/invalid-url',
      mediaType: 'image/jpeg'
    };
    
    const result1 = await smartReplySystem.generateSmartReply('这是什么？', userId, 'TestUser', mediaInfo);
    console.log('🤖 机器人:', result1.text);
    console.log('📊 回复来源:', result1.source);
    console.log('✅ 成功状态:', result1.success);
    
    // 测试2: 普通文本消息
    console.log('\n2️⃣ 测试普通文本消息');
    console.log('👤 用户: 你好');
    
    const result2 = await smartReplySystem.generateSmartReply('你好', userId, 'TestUser');
    console.log('🤖 机器人:', result2.text.substring(0, 80) + '...');
    console.log('📊 回复来源:', result2.source);
    console.log('✅ 成功状态:', result2.success);
    
    console.log('\n✅ 错误处理测试完成');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

// 测试系统繁忙提示
async function testSystemBusyMessage() {
  console.log('\n🧪 测试系统繁忙提示');
  console.log('-'.repeat(30));
  
  // 模拟不同类型的错误消息
  const errorMessages = [
    'Model encountered an internal error while processing your request',
    'system-level issue',
    'Request parameter error',
    'Coze处理失败: Internal server error'
  ];
  
  errorMessages.forEach((errorMsg, index) => {
    console.log(`\n${index + 1}. 错误: ${errorMsg}`);
    
    // 检查是否会触发系统繁忙提示
    const isSystemError = errorMsg.includes('Model encountered an internal error') || 
                         errorMsg.includes('system-level issue') ||
                         errorMsg.includes('Request parameter error') ||
                         errorMsg.includes('Coze处理失败');
    
    if (isSystemError) {
      console.log('   ✅ 会触发系统繁忙提示');
    } else {
      console.log('   ❌ 不会触发系统繁忙提示');
    }
  });
}

// 主函数
async function main() {
  await testErrorHandling();
  await testSystemBusyMessage();
}

main();
