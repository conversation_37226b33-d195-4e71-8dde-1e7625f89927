# WhatsApp Coze AI 智能回复集成指南

## 🎯 系统概述

我已经为您创建了一个完整的WhatsApp与Coze AI集成系统，具备以下功能：

### ✨ 核心功能
- 🤖 **智能回复**: 使用Coze AI机器人进行自然语言对话
- 💭 **上下文记忆**: 保持对话连续性，记住之前的对话内容
- 👥 **多用户支持**: 为每个用户维护独立的对话会话
- 🔄 **自动处理**: 自动接收WhatsApp消息并发送AI回复
- 📊 **会话管理**: 实时监控和管理用户会话

### 🔧 技术配置
- **机器人ID**: `7528309468237529127`
- **访问令牌**: `pat_FSEGBGcfbYwabmxELRPZYAReTrVaWIMMPBwlyIfUeXnqaJsBTcbbIZrpNyEAZwLR`
- **WhatsApp沙盒**: `+1 415 523 8886`

## 🚀 快速开始

### 第1步：测试Coze API连接
```bash
# 测试基本连接
node test-coze-api.js connection

# 完整功能测试
node test-coze-api.js full
```

### 第2步：启动智能回复服务
```bash
# 启动服务
node whatsapp-coze-bot.js

# 或使用一键启动脚本（Windows）
start-coze-bot.bat
```

### 第3步：配置Webhook
```bash
# 启动ngrok隧道
ngrok http 3002

# 在Twilio控制台配置Webhook URL
# https://your-ngrok-url.ngrok.io/whatsapp-webhook
```

### 第4步：测试智能对话
从您的WhatsApp发送消息到 `+1 415 523 8886`

## 📋 详细测试命令

### 🔍 Coze API测试
```bash
# 基本连接测试
node test-coze-api.js connection

# 单条消息测试
node test-coze-api.js message "你好，我是新用户"

# 对话上下文测试
node test-coze-api.js context

# 多用户会话测试
node test-coze-api.js multi

# 错误处理测试
node test-coze-api.js error

# 完整测试套件
node test-coze-api.js full
```

### 🤖 智能回复服务
```bash
# 启动服务
node whatsapp-coze-bot.js

# 检查服务状态
curl http://localhost:3002/health

# 查看活跃会话
curl http://localhost:3002/sessions

# 清除特定用户会话
curl -X POST http://localhost:3002/clear-session -H "Content-Type: application/json" -d '{"userId":"13463819202"}'
```

## 📊 预期测试结果

### ✅ 成功的Coze API测试
```
✅ Coze API连接成功!
   机器人ID: 7528309468237529127
   测试响应: 你好！我是您的AI助手...

📊 测试报告汇总
   基本连接: ✅ 通过
   单条消息: ✅ 通过
   对话上下文: 5/5 通过
   多用户会话: 6/6 通过
   错误处理: 3/3 通过

🎯 总体评估:
   Coze API集成: ✅ 可用
   上下文功能: ✅ 支持
   多用户支持: ✅ 支持
```

### ✅ 成功的智能回复服务
```
🚀 WhatsApp Coze机器人服务启动成功!
📡 监听端口: 3002
🔗 Webhook URL: http://localhost:3002/whatsapp-webhook
🤖 Coze机器人配置:
   机器人ID: 7528309468237529127
📱 WhatsApp配置:
   沙盒号码: +14155238886
⏳ 等待接收WhatsApp消息...
```

### ✅ 成功的消息处理
```
📱 收到WhatsApp消息!
📋 消息详情:
   消息ID: SM**********abcdef
   发送方: whatsapp:+13463819202
   内容: 你好，我想了解一下你的功能

🤖 使用Coze处理消息...
📤 发送到Coze (用户: 13463819202):
   消息: 你好，我想了解一下你的功能
   会话ID: New conversation

📥 Coze回复:
   你好！我是您的AI助手，我可以帮助您进行自然语言对话...

📤 发送WhatsApp消息到 whatsapp:+13463819202:
   你好！我是您的AI助手，我可以帮助您进行自然语言对话...

✅ 消息发送成功: SM0987654321fedcba
```

## 🔧 系统架构

### 📡 消息流程
1. **用户发送消息** → WhatsApp → Twilio → Webhook
2. **消息处理** → 提取用户信息和消息内容
3. **Coze AI处理** → 发送到Coze机器人获取智能回复
4. **回复发送** → 通过Twilio发送回复到用户WhatsApp

### 💾 会话管理
```javascript
// 用户会话结构
{
  "13463819202": {
    phoneNumber: "whatsapp:+13463819202",
    name: "User Name",
    lastActivity: "2024-01-20T15:30:25.000Z",
    messageCount: 5,
    conversationId: "coze_conversation_id_123"
  }
}
```

### 🔄 上下文保持
- 每个用户维护独立的Coze会话ID
- 自动关联历史对话上下文
- 支持长期对话记忆

## 🛠️ 高级配置

### 环境变量配置
```env
# Coze API Configuration
COZE_BOT_ID=7528309468237529127
COZE_ACCESS_TOKEN=pat_FSEGBGcfbYwabmxELRPZYAReTrVaWIMMPBwlyIfUeXnqaJsBTcbbIZrpNyEAZwLR
COZE_BASE_URL=https://api.coze.com
COZE_API_VERSION=v1

# WhatsApp Configuration
WHATSAPP_SANDBOX_NUMBER=whatsapp:+14155238886
WHATSAPP_AUTO_REPLY=true
```

### 自定义回复逻辑
您可以修改 `whatsapp-coze-bot.js` 中的 `processMessageWithCoze` 函数来：
- 添加消息预处理
- 自定义回复格式
- 添加特殊命令处理
- 集成其他AI服务

## 📈 监控和调试

### 健康检查端点
```bash
# 检查服务状态
curl http://localhost:3002/health

# 响应示例
{
  "status": "healthy",
  "service": "WhatsApp Coze Bot",
  "uptime": 3600,
  "activeSessions": 3,
  "coze": {
    "success": true,
    "botId": "7528309468237529127"
  },
  "twilio": {
    "success": true,
    "status": "active"
  }
}
```

### 会话管理
```bash
# 查看活跃会话
curl http://localhost:3002/sessions

# 清除用户会话
curl -X POST http://localhost:3002/clear-session \
  -H "Content-Type: application/json" \
  -d '{"userId":"13463819202"}'
```

## 🔍 故障排除

### 常见问题

#### 1. Coze API连接失败
```
❌ Coze API连接失败: Request failed with status code 401
```
**解决方案：**
- 检查访问令牌是否正确
- 确认机器人ID有效
- 验证网络连接

#### 2. WhatsApp消息接收失败
```
❌ 处理消息失败: The number is not a valid WhatsApp number
```
**解决方案：**
- 确保接收号码已加入沙盒
- 检查Webhook URL配置
- 验证ngrok隧道状态

#### 3. 智能回复不工作
```
❌ Coze处理失败: Bot not found
```
**解决方案：**
- 确认机器人ID正确
- 检查机器人状态和权限
- 验证API访问令牌

### 调试命令
```bash
# 测试Coze连接
node test-coze-api.js connection

# 检查服务状态
curl http://localhost:3002/health

# 查看ngrok状态
curl http://localhost:4040/api/tunnels

# 测试Webhook
curl -X POST http://localhost:3002/whatsapp-webhook \
  -d "Body=test&From=whatsapp:+**********"
```

## 🎉 成功指标

当系统正常工作时，您应该看到：

1. ✅ **Coze API测试通过**
2. ✅ **智能回复服务启动成功**
3. ✅ **Webhook配置正确**
4. ✅ **用户发送消息被接收**
5. ✅ **Coze AI生成智能回复**
6. ✅ **回复成功发送到用户WhatsApp**
7. ✅ **上下文对话正常工作**

## 💡 下一步建议

系统运行成功后，您可以：

1. **自定义机器人行为** - 在Coze平台配置机器人个性和技能
2. **添加更多功能** - 集成业务逻辑、数据库查询等
3. **扩展到生产环境** - 申请WhatsApp Business API
4. **监控和分析** - 添加用户行为分析和对话质量监控
5. **多语言支持** - 配置多语言对话能力

---

**准备开始了吗？** 运行 `node test-coze-api.js full` 开始完整测试！🚀
