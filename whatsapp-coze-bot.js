// WhatsApp与Coze机器人集成
require('dotenv').config();
const express = require('express');
const twilio = require('twilio');
const smartReplySystem = require('./smart-reply-system');
const VolcengineTOSClient = require('./volcengine-tos-client');
const config = require('./src/config');

// 创建Express应用
const app = express();
const PORT = process.env.WEBHOOK_PORT || 3002;

// 初始化火山引擎TOS客户端
const tosClient = new VolcengineTOSClient();

// 解析表单数据（Twilio发送的是form-encoded数据）
app.use(express.urlencoded({ extended: true }));
app.use(express.json());

// 存储用户会话信息
const userSessions = {};

// 智能回复系统已在模块中初始化

// 初始化Twilio客户端
const twilioClient = twilio(
  process.env.TWILIO_ACCOUNT_SID,
  process.env.TWILIO_AUTH_TOKEN
);

// Webhook端点 - 接收WhatsApp消息
app.post('/whatsapp-webhook', async (req, res) => {
  console.log('\n📱 收到WhatsApp消息!');
  console.log('=' .repeat(50));
  
  const {
    MessageSid,
    From,
    To,
    Body,
    NumMedia,
    MediaUrl0,
    MediaContentType0,
    ProfileName,
    WaId
  } = req.body;

  // 记录消息详情
  console.log('📋 消息详情:');
  console.log('   消息ID:', MessageSid);
  console.log('   发送方:', From);
  console.log('   接收方:', To);
  console.log('   内容:', Body);
  console.log('   发送者姓名:', ProfileName || 'Unknown');
  console.log('   WhatsApp ID:', WaId);
  console.log('   媒体文件数:', NumMedia || 0);
  if (MediaUrl0) {
    console.log('   媒体URL:', MediaUrl0);
    console.log('   媒体类型:', MediaContentType0);
  }
  console.log('   接收时间:', new Date().toLocaleString());
  console.log('');

  // 准备TwiML响应
  const twiml = new twilio.twiml.MessagingResponse();
  
  // 立即返回响应，避免Twilio超时
  res.writeHead(200, {'Content-Type': 'text/xml'});
  res.end(twiml.toString());

  try {
    // 处理用户消息并获取AI回复，包含媒体信息
    const mediaInfo = {
      numMedia: NumMedia || 0,
      mediaUrl: MediaUrl0,
      mediaType: MediaContentType0
    };
    await processMessageWithAI(From, Body, ProfileName, mediaInfo);
  } catch (error) {
    console.error('❌ 处理消息失败:', error.message);
    // 发送错误通知
    await sendWhatsAppMessage(
      From, 
      '很抱歉，处理您的消息时出现了问题。请稍后再试。'
    );
  }
});

// 使用智能回复系统处理消息
async function processMessageWithAI(from, message, userName, mediaInfo = null) {
  try {
    console.log('🧠 使用智能回复系统处理消息...');

    // 提取用户ID（去掉whatsapp:前缀）
    const userId = from.replace('whatsapp:', '');

    // 获取或创建用户会话
    if (!userSessions[userId]) {
      userSessions[userId] = {
        phoneNumber: from,
        name: userName || 'User',
        lastActivity: new Date(),
        messageCount: 0
      };
    }

    // 更新会话信息
    userSessions[userId].lastActivity = new Date();
    userSessions[userId].messageCount += 1;

    // 处理媒体文件（如果有）
    let processedMediaInfo = mediaInfo;
    if (mediaInfo && mediaInfo.numMedia > 0 && mediaInfo.mediaUrl && mediaInfo.mediaType && mediaInfo.mediaType.startsWith('image/')) {
      console.log('🖼️ 检测到图片消息，开始完整处理流程...');
      console.log('   原始媒体URL:', mediaInfo.mediaUrl);
      console.log('   媒体类型:', mediaInfo.mediaType);

      try {
        // 使用TOS客户端处理媒体文件：下载 → 上传 → 生成公开URL
        console.log('📥 开始从Twilio下载并上传到TOS...');
        const mediaResult = await tosClient.processWhatsAppMedia(
          mediaInfo.mediaUrl,
          config.twilio.accountSid,
          config.twilio.authToken
        );

        console.log('✅ 媒体文件处理成功:');
        console.log('   原始URL:', mediaResult.originalUrl);
        console.log('   公开URL:', mediaResult.publicUrl);
        console.log('   文件名:', mediaResult.fileName);
        console.log('   文件大小:', mediaResult.size, 'bytes');

        // 更新媒体信息，使用公开URL替换原始URL
        processedMediaInfo = {
          ...mediaInfo,
          mediaUrl: mediaResult.publicUrl,
          originalUrl: mediaResult.originalUrl,
          fileName: mediaResult.fileName,
          fileSize: mediaResult.size,
          bucket: mediaResult.bucket,
          processed: true
        };

        console.log('🔄 媒体信息已更新，将使用公开URL调用Coze API');

      } catch (mediaError) {
        console.error('❌ 媒体文件处理失败:', mediaError.message);
        console.log('⚠️ 回退到原始媒体URL（可能无法被Coze API访问）');

        // 标记处理失败，但继续使用原始媒体信息
        processedMediaInfo = {
          ...mediaInfo,
          processed: false,
          processingError: mediaError.message
        };
      }
    }

    // 使用智能回复系统生成回复
    console.log(`📤 处理消息 (用户: ${userId}):`);
    console.log(`   消息: ${message}`);
    console.log(`   用户名: ${userName || 'Unknown'}`);
    if (processedMediaInfo && processedMediaInfo.numMedia > 0) {
      console.log(`   媒体类型: ${processedMediaInfo.mediaType}`);
      console.log(`   媒体URL: ${processedMediaInfo.mediaUrl}`);
    }

    const aiResponse = await smartReplySystem.generateSmartReply(message, userId, userName, processedMediaInfo);

    // 提取回复文本
    const replyText = aiResponse.text;
    console.log(`📥 AI回复 (${aiResponse.source}):`);
    console.log(`   ${replyText.substring(0, 100)}${replyText.length > 100 ? '...' : ''}`);

    // 发送回复到WhatsApp
    await sendWhatsAppMessage(from, replyText);

    return {
      success: true,
      reply: replyText,
      source: aiResponse.source
    };

  } catch (error) {
    console.error('❌ AI处理失败:', error.message);
    throw error;
  }
}

// 发送WhatsApp消息
async function sendWhatsAppMessage(to, body) {
  try {
    console.log(`📤 发送WhatsApp消息到 ${to}:`);
    console.log(`   ${body.substring(0, 100)}${body.length > 100 ? '...' : ''}`);
    
    const message = await twilioClient.messages.create({
      from: 'whatsapp:+14155238886',
      body: body,
      to: to
    });
    
    console.log(`✅ 消息发送成功: ${message.sid}`);
    return message;
    
  } catch (error) {
    console.error('❌ 发送WhatsApp消息失败:', error.message);
    throw error;
  }
}

// 健康检查端点
app.get('/health', async (req, res) => {
  try {
    // 检查智能回复系统状态
    const aiStatus = { success: true, system: 'Smart Reply System' };
    
    // 检查Twilio连接
    let twilioStatus = { success: false, error: 'Not checked' };
    try {
      const account = await twilioClient.api.accounts(process.env.TWILIO_ACCOUNT_SID).fetch();
      twilioStatus = {
        success: true,
        status: account.status,
        type: account.type
      };
    } catch (twilioError) {
      twilioStatus = {
        success: false,
        error: twilioError.message
      };
    }
    
    res.json({
      status: 'healthy',
      service: 'WhatsApp Smart AI Bot',
      uptime: process.uptime(),
      timestamp: new Date().toISOString(),
      activeSessions: Object.keys(userSessions).length,
      ai: aiStatus,
      twilio: twilioStatus
    });
  } catch (error) {
    res.status(500).json({
      status: 'unhealthy',
      error: error.message
    });
  }
});

// 查看活跃会话
app.get('/sessions', (req, res) => {
  res.json({
    total: Object.keys(userSessions).length,
    sessions: userSessions,
    timestamp: new Date().toISOString()
  });
});

// 清除特定会话
app.post('/clear-session', (req, res) => {
  const { userId } = req.body;
  
  if (!userId) {
    return res.status(400).json({
      success: false,
      error: 'Missing userId parameter'
    });
  }
  
  if (userSessions[userId]) {
    delete userSessions[userId];
    smartReplySystem.clearUserSession(userId);
    
    console.log(`🗑️ 已清除用户 ${userId} 的会话`);
    
    res.json({
      success: true,
      message: `Session for user ${userId} cleared`
    });
  } else {
    res.status(404).json({
      success: false,
      error: `No session found for user ${userId}`
    });
  }
});

// 启动服务器
async function startServer() {
  try {
    // 初始化TOS客户端
    console.log('🔥 初始化火山引擎TOS客户端...');
    const tosInitResult = await tosClient.initialize();

    app.listen(PORT, 'localhost', () => {
      console.log('🚀 WhatsApp智能AI机器人服务启动成功!');
      console.log('=' .repeat(60));
      console.log(`📡 监听端口: ${PORT}`);
      console.log(`🔗 Webhook URL: http://localhost:${PORT}/whatsapp-webhook`);
      console.log(`🏥 健康检查: http://localhost:${PORT}/health`);
      console.log(`📊 会话查看: http://localhost:${PORT}/sessions`);
      console.log('');
      console.log('🧠 智能回复系统:');
      console.log(`   Coze AI集成: 已配置`);
      console.log(`   本地AI回复: 已启用`);
      console.log(`   智能回复模式: 混合模式`);
      console.log('');
      console.log('📱 WhatsApp配置:');
      console.log(`   沙盒号码: +14155238886`);
      console.log('');
      console.log('🖼️ 媒体处理:');
      console.log(`   火山引擎TOS: ${tosInitResult ? '✅ 已配置' : '❌ 配置失败'}`);
      console.log(`   图片消息支持: ${tosInitResult ? '✅ 已启用' : '⚠️ 降级模式'}`);
      console.log('');
      console.log('💡 使用ngrok暴露到公网:');
      console.log(`   ngrok http ${PORT}`);
      console.log('');
      console.log('⏳ 等待接收WhatsApp消息...');
    });

  } catch (error) {
    console.error('❌ 服务启动失败:', error.message);
    process.exit(1);
  }
}

startServer();

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n📊 服务器关闭统计:');
  console.log(`   活跃会话: ${Object.keys(userSessions).length}`);
  console.log(`   运行时间: ${Math.floor(process.uptime())} 秒`);
  console.log('👋 WhatsApp智能AI机器人服务已关闭');
  process.exit(0);
});
