// 测试Twilio WhatsApp沙盒消息
require('dotenv').config();
const twilio = require('twilio');

async function testSandboxMessage(userWhatsAppNumber) {
  console.log('🎉 测试Twilio WhatsApp沙盒消息发送...\n');

  try {
    const accountSid = process.env.TWILIO_ACCOUNT_SID;
    const authToken = process.env.TWILIO_AUTH_TOKEN;
    const client = twilio(accountSid, authToken);

    // Twilio WhatsApp沙盒号码（固定）
    const sandboxNumber = 'whatsapp:+***********';
    
    // 格式化用户号码
    let userNumber;
    if (userWhatsAppNumber.startsWith('whatsapp:')) {
      userNumber = userWhatsAppNumber;
    } else if (userWhatsAppNumber.startsWith('+')) {
      userNumber = `whatsapp:${userWhatsAppNumber}`;
    } else {
      userNumber = `whatsapp:+${userWhatsAppNumber}`;
    }

    console.log('📋 沙盒消息详情:');
    console.log('   发送方:', sandboxNumber, '(<PERSON>wi<PERSON>沙盒)');
    console.log('   接收方:', userNumber, '(您的WhatsApp)');
    console.log('   状态: 沙盒已验证 ✅');
    console.log('   会话: 24小时窗口已激活');
    console.log('   时间:', new Date().toLocaleString());
    console.log('');

    // 发送测试消息
    console.log('🚀 正在发送沙盒测试消息...');
    
    const testMessage = `🎉 Twilio WhatsApp沙盒测试成功！

✅ 您的设置状态：
   • WhatsApp号码已验证
   • 沙盒权限已激活
   • 双向通信已开启

📱 功能测试：
   • 消息发送：正常 ✅
   • 消息接收：等待测试
   • 状态跟踪：已启用

🔄 下一步测试：
请回复任意消息到这个号码 (+1 415 523 8886)，我们将验证双向通信功能。

⏰ 会话信息：
   • 24小时自由消息窗口已开启
   • 发送时间: ${new Date().toLocaleString()}
   • 消息ID: 将在发送后显示

💡 提示：这是Twilio官方沙盒环境，完全安全可靠！`;

    const message = await client.messages.create({
      from: sandboxNumber,
      to: userNumber,
      body: testMessage
    });

    console.log('✅ 沙盒消息发送成功！');
    console.log('   消息SID:', message.sid);
    console.log('   初始状态:', message.status);
    console.log('   消息长度:', testMessage.length, '字符');
    console.log('   发送时间:', new Date().toISOString());
    console.log('');

    // 等待状态更新
    console.log('⏳ 等待消息状态更新（5秒）...');
    
    setTimeout(async () => {
      try {
        const updatedMessage = await client.messages(message.sid).fetch();
        
        console.log('📊 消息状态更新:');
        console.log('   当前状态:', updatedMessage.status);
        console.log('   发送时间:', updatedMessage.dateSent || 'Pending');
        console.log('   错误代码:', updatedMessage.errorCode || 'None');
        console.log('   错误信息:', updatedMessage.errorMessage || 'None');
        console.log('   价格:', updatedMessage.price || 'Free (Sandbox)', updatedMessage.priceUnit || '');
        
        console.log('\n🎯 测试结果分析:');
        
        switch (updatedMessage.status) {
          case 'queued':
            console.log('   📤 状态：消息已排队，正在处理中');
            break;
          case 'sending':
            console.log('   🔄 状态：消息正在发送到WhatsApp服务器');
            break;
          case 'sent':
            console.log('   ✅ 状态：消息已成功发送到WhatsApp');
            console.log('   💡 请检查您的WhatsApp应用！');
            break;
          case 'delivered':
            console.log('   📱 状态：消息已送达到您的设备');
            console.log('   🎉 沙盒测试完全成功！');
            break;
          case 'failed':
            console.log('   ❌ 状态：消息发送失败');
            console.log('   🔧 请检查沙盒设置');
            break;
          default:
            console.log('   ⏳ 状态：', updatedMessage.status);
        }

        console.log('\n📱 下一步操作:');
        console.log('   1. 检查您的WhatsApp应用');
        console.log('   2. 查看来自 +1 415 523 8886 的消息');
        console.log('   3. 回复任意消息测试双向通信');
        console.log('   4. 观察我们的webhook接收功能');
        
        console.log('\n🔄 双向通信测试:');
        console.log('   • 发送消息到您：✅ 已完成');
        console.log('   • 接收您的回复：⏳ 等待您回复');
        console.log('   • Webhook处理：⏳ 等待设置');

      } catch (statusError) {
        console.log('⚠️  状态查询失败:', statusError.message);
      }
    }, 5000);

    return {
      success: true,
      messageId: message.sid,
      status: message.status,
      timestamp: new Date().toISOString()
    };

  } catch (error) {
    console.error('❌ 沙盒消息发送失败:', error.message);
    
    if (error.code === 63016) {
      console.error('\n🔧 可能的问题:');
      console.error('   1. 您的号码可能未正确加入沙盒');
      console.error('   2. 沙盒会话可能已过期');
      console.error('\n💡 解决方案:');
      console.error('   1. 重新发送 "join <keyword>" 到 +1 415 523 8886');
      console.error('   2. 等待新的确认消息');
      console.error('   3. 确保号码格式正确');
    } else if (error.code === 21211) {
      console.error('   错误: 号码格式无效');
      console.error('   请使用格式: +国家代码+号码 (如: +8613800138000)');
    } else {
      console.error('   错误代码:', error.code);
      console.error('   详细信息:', error.moreInfo || 'N/A');
    }

    return {
      success: false,
      error: error.message,
      code: error.code
    };
  }
}

// 主函数
async function main() {
  console.log('📱 Twilio WhatsApp沙盒测试工具');
  console.log('=' .repeat(60));
  console.log('🎯 目标: 验证沙盒设置并发送测试消息');
  console.log('');
  
  // 从命令行参数获取号码
  const userNumber = process.argv[2];
  
  if (!userNumber) {
    console.log('❌ 请提供您的WhatsApp号码');
    console.log('\n📋 使用方法:');
    console.log('   node test-sandbox-message.js +8613800138000');
    console.log('   node test-sandbox-message.js 8613800138000');
    console.log('   node test-sandbox-message.js whatsapp:+8613800138000');
    console.log('\n✅ 确保您已完成沙盒设置:');
    console.log('   1. 发送 "join <keyword>" 到 +1 415 523 8886');
    console.log('   2. 收到确认消息');
    console.log('   3. 现在可以测试消息发送');
    process.exit(1);
  }

  try {
    const result = await testSandboxMessage(userNumber);
    
    if (result.success) {
      console.log('\n🎉 沙盒测试成功完成！');
      console.log('   消息ID:', result.messageId);
      console.log('   发送时间:', result.timestamp);
    } else {
      console.log('\n❌ 沙盒测试失败');
      console.log('   请检查设置后重试');
    }
    
  } catch (error) {
    console.log('\n💥 测试过程中发生错误:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = { testSandboxMessage };
