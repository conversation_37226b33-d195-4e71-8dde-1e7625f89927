// 测试修复后的错误处理
const smartReplySystem = require('./smart-reply-system');

async function testFixedErrorHandling() {
  console.log('🧪 测试修复后的错误处理');
  console.log('=' .repeat(50));
  
  const userId = 'fixed_error_test_user';
  
  try {
    // 测试图片消息（可能触发API错误）
    console.log('\n📸 测试图片消息错误处理');
    console.log('👤 用户: 这是什么？[附带无效图片URL]');
    
    const mediaInfo = {
      numMedia: 1,
      mediaUrl: 'https://api.twilio.com/invalid-image-url',
      mediaType: 'image/jpeg'
    };
    
    const startTime = Date.now();
    const result = await smartReplySystem.generateSmartReply('这是什么？', userId, 'TestUser', mediaInfo);
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    console.log('🤖 机器人:', result.text);
    console.log('📊 回复来源:', result.source);
    console.log('✅ 成功状态:', result.success);
    console.log(`⏱️ 处理时间: ${duration}ms`);
    
    // 检查是否是系统繁忙提示
    if (result.text.includes('系统当前繁忙') || result.text.includes('联系人工客服')) {
      console.log('✅ 正确触发了系统繁忙提示');
    } else if (result.source === 'Local AI') {
      console.log('✅ 正确回退到本地AI回复');
    } else {
      console.log('⚠️ 未按预期处理错误');
    }
    
    // 验证处理时间合理（不应该超过30秒）
    if (duration < 30000) {
      console.log('✅ 处理时间合理，没有无限轮询');
    } else {
      console.log('❌ 处理时间过长，可能存在轮询问题');
    }
    
    console.log('\n✅ 错误处理测试完成');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

// 测试普通文本消息（确保正常功能不受影响）
async function testNormalMessage() {
  console.log('\n🧪 测试普通文本消息');
  console.log('-'.repeat(30));
  
  const userId = 'normal_test_user';
  
  try {
    console.log('👤 用户: 你好');
    
    const startTime = Date.now();
    const result = await smartReplySystem.generateSmartReply('你好', userId, 'TestUser');
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    console.log('🤖 机器人:', result.text.substring(0, 80) + '...');
    console.log('📊 回复来源:', result.source);
    console.log('✅ 成功状态:', result.success);
    console.log(`⏱️ 处理时间: ${duration}ms`);
    
    if (result.success && result.source === 'Coze AI v3') {
      console.log('✅ 正常消息处理成功');
    } else if (result.source === 'Local AI') {
      console.log('✅ 回退到本地AI，功能正常');
    } else {
      console.log('⚠️ 消息处理异常');
    }
    
  } catch (error) {
    console.error('❌ 普通消息测试失败:', error.message);
  }
}

// 主函数
async function main() {
  await testFixedErrorHandling();
  await testNormalMessage();
  
  console.log('\n🎯 测试总结:');
  console.log('1. 图片消息错误处理 - 应该快速失败并提供友好提示');
  console.log('2. 普通文本消息 - 应该正常工作');
  console.log('3. 处理时间 - 应该在合理范围内，不会无限轮询');
}

main();
