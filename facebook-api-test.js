// Facebook API 测试脚本
// 使用方法: node facebook-api-test.js

import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:3002';

// 测试函数
async function testAPI() {
  console.log('🧪 开始测试 Facebook API...\n');

  // 1. 检查服务器状态
  try {
    console.log('1️⃣ 检查服务器状态...');
    const statusRes = await fetch(`${BASE_URL}/status`);
    const status = await statusRes.json();
    console.log('✅ 服务器状态:', JSON.stringify(status, null, 2));
    console.log('');
  } catch (err) {
    console.error('❌ 服务器连接失败:', err.message);
    return;
  }

  // 2. 测试获取长期token (需要手动提供短期token)
  const shortToken = 'YOUR_SHORT_TERM_TOKEN_HERE'; // 替换为实际的短期token
  if (shortToken !== 'YOUR_SHORT_TERM_TOKEN_HERE') {
    try {
      console.log('2️⃣ 获取长期用户token...');
      const longTokenRes = await fetch(`${BASE_URL}/get-long-term-token`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ short_token: shortToken })
      });
      const longTokenData = await longTokenRes.json();
      console.log('✅ 长期token结果:', longTokenData);
      console.log('');

      // 3. 获取页面token
      if (longTokenData.access_token) {
        console.log('3️⃣ 获取页面token...');
        const pageTokenRes = await fetch(`${BASE_URL}/get-page-token`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ user_access_token: longTokenData.access_token })
        });
        const pageTokenData = await pageTokenRes.json();
        console.log('✅ 页面token结果:', pageTokenData);
        console.log('');

        // 4. 发布测试文章
        if (pageTokenData.access_token) {
          console.log('4️⃣ 发布测试文章...');
          const postRes = await fetch(`${BASE_URL}/post`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              message: '🎉 这是一条通过API发布的测试文章！\n\n测试时间: ' + new Date().toLocaleString('zh-CN'),
              link: 'https://developers.facebook.com/docs/graph-api'
            })
          });
          const postData = await postRes.json();
          console.log('✅ 发帖结果:', postData);
        }
      }
    } catch (err) {
      console.error('❌ API调用失败:', err.message);
    }
  } else {
    console.log('⚠️  请先在代码中设置有效的短期token');
    console.log('   获取短期token的步骤:');
    console.log('   1. 访问 Facebook Graph API Explorer');
    console.log('   2. 选择你的应用和页面');
    console.log('   3. 获取用户访问令牌');
    console.log('   4. 将token替换到此文件的 shortToken 变量中');
  }
}

// 手动发帖测试函数
async function testPost(message, link = null) {
  try {
    console.log('📝 发布文章:', message);
    const postRes = await fetch(`${BASE_URL}/post`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ message, link })
    });
    const result = await postRes.json();
    console.log('结果:', result);
    return result;
  } catch (err) {
    console.error('❌ 发帖失败:', err.message);
  }
}

// 运行测试
if (import.meta.url === `file://${process.argv[1]}`) {
  testAPI();
}

// 导出函数供其他文件使用
export { testAPI, testPost };
