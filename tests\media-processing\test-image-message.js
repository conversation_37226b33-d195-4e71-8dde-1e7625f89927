// 测试图片消息处理
const coze = require('./coze-api-v3-client');

async function testImageMessage() {
  console.log('🧪 测试图片消息处理');
  console.log('=' .repeat(50));
  
  const userId = 'image_test_user';
  
  try {
    // 模拟图片消息
    const mediaInfo = {
      numMedia: 1,
      mediaUrl: 'https://api.twilio.com/2010-04-01/Accounts/AC7657552c992e2a3737961532e7e609d1/Messages/MMaa6c44a7ff79f4ad4ce1f49781ddfdbf/Media/ME10a86f9b7de3168ed95543d5f99ea554',
      mediaType: 'image/jpeg'
    };
    
    console.log('\n📸 发送图片消息');
    console.log('👤 用户: [发送了一张图片]');
    console.log('📎 媒体URL:', mediaInfo.mediaUrl);
    console.log('📎 媒体类型:', mediaInfo.mediaType);
    
    const result1 = await coze.sendMessage('这是什么？', userId, null, mediaInfo);
    console.log('🤖 机器人:', result1.text.substring(0, 100) + '...');
    
    // 显示聊天历史
    const history1 = coze.getChatHistory(userId);
    console.log(`📚 聊天历史: ${history1.length} 条记录`);
    
    // 显示最后一条用户消息的详情
    const lastUserMessage = history1[history1.length - 2]; // 倒数第二条是用户消息
    console.log('\n📋 用户消息详情:');
    console.log('   角色:', lastUserMessage.role);
    console.log('   内容:', lastUserMessage.content);
    console.log('   类型:', lastUserMessage.content_type);
    console.log('   文件URL:', lastUserMessage.file_url || 'N/A');
    
    // 等待2秒
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 发送后续文本消息
    console.log('\n💬 发送后续文本消息');
    console.log('👤 用户: 能详细说明一下吗？');
    
    const result2 = await coze.sendMessage('能详细说明一下吗？', userId);
    console.log('🤖 机器人:', result2.text.substring(0, 100) + '...');
    
    // 显示完整聊天历史
    const finalHistory = coze.getChatHistory(userId);
    console.log(`📚 最终聊天历史: ${finalHistory.length} 条记录`);
    
    console.log('\n📋 完整对话历史:');
    finalHistory.forEach((msg, index) => {
      const role = msg.role === 'user' ? '👤 用户' : '🤖 机器人';
      const content = msg.content.length > 60 ? msg.content.substring(0, 60) + '...' : msg.content;
      const type = msg.content_type === 'object_string' ? ' [图片]' : '';
      console.log(`   ${index + 1}. ${role}: ${content}${type}`);
      if (msg.file_url) {
        console.log(`      📎 文件: ${msg.file_url.substring(0, 50)}...`);
      }
    });
    
    console.log('\n✅ 图片消息测试完成');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.error('错误详情:', error);
  }
}

// 测试纯文本消息（对比）
async function testTextMessage() {
  console.log('\n🧪 测试纯文本消息（对比）');
  console.log('-'.repeat(30));
  
  const userId = 'text_test_user';
  
  try {
    console.log('👤 用户: 你好，我想了解一下产品');
    const result = await coze.sendMessage('你好，我想了解一下产品', userId);
    console.log('🤖 机器人:', result.text.substring(0, 100) + '...');
    
    const history = coze.getChatHistory(userId);
    const lastUserMessage = history[history.length - 2];
    
    console.log('\n📋 文本消息详情:');
    console.log('   角色:', lastUserMessage.role);
    console.log('   内容:', lastUserMessage.content);
    console.log('   类型:', lastUserMessage.content_type);
    console.log('   文件URL:', lastUserMessage.file_url || 'N/A');
    
  } catch (error) {
    console.error('❌ 文本消息测试失败:', error.message);
  }
}

// 主函数
async function main() {
  await testImageMessage();
  await testTextMessage();
}

main();
