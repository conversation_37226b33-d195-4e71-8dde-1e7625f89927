// 测试修复后的TOS上传功能
const VolcengineTOSClient = require('./volcengine-tos-client');
const fs = require('fs');
const path = require('path');

async function testFixedTOSUpload() {
  console.log('🧪 测试修复后的TOS上传功能');
  console.log('=' .repeat(50));
  
  try {
    // 1. 初始化TOS客户端
    console.log('\n1️⃣ 初始化TOS客户端...');
    const tosClient = new VolcengineTOSClient();
    
    const initResult = await tosClient.initialize();
    console.log('初始化结果:', initResult ? '✅ 成功' : '❌ 失败');
    
    // 2. 创建测试数据
    console.log('\n2️⃣ 准备测试数据...');
    const testData = Buffer.from('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==', 'base64');
    const fileName = `test-upload-fix-${Date.now()}.png`;
    const contentType = 'image/png';
    
    console.log('📋 测试数据:');
    console.log('   文件名:', fileName);
    console.log('   内容类型:', contentType);
    console.log('   数据大小:', testData.length, 'bytes');
    
    // 3. 测试直接上传
    console.log('\n3️⃣ 测试直接上传到TOS...');
    try {
      const uploadResult = await tosClient.uploadToTOS(testData, fileName, contentType);
      
      console.log('✅ 直接上传成功!');
      console.log('   公开URL:', uploadResult.url);
      console.log('   文件Key:', uploadResult.key);
      console.log('   存储桶:', uploadResult.bucket);
      
      // 验证URL格式
      const expectedUrlPattern = /^https:\/\/whatsapp\.tos-cn-beijing\.volces\.com\/.+/;
      const urlValid = expectedUrlPattern.test(uploadResult.url);
      console.log('   URL格式验证:', urlValid ? '✅ 正确' : '❌ 错误');
      
      return { success: true, uploadResult };
      
    } catch (uploadError) {
      console.error('❌ 直接上传失败:', uploadError.message);
      console.error('   错误详情:', uploadError);
      return { success: false, error: uploadError.message };
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    return { success: false, error: error.message };
  }
}

// 测试完整的媒体处理流程
async function testCompleteMediaFlow() {
  console.log('\n🧪 测试完整媒体处理流程');
  console.log('-'.repeat(40));
  
  try {
    const tosClient = new VolcengineTOSClient();
    await tosClient.initialize();
    
    // 模拟从Twilio下载的数据
    const mockMediaData = Buffer.from('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==', 'base64');
    const mockMediaUrl = 'https://api.twilio.com/test-media.png';
    const mockAccountSid = 'AC_test_sid';
    const mockAuthToken = 'test_token';
    
    console.log('📋 模拟媒体处理:');
    console.log('   媒体URL:', mockMediaUrl);
    console.log('   数据大小:', mockMediaData.length, 'bytes');
    
    // 测试文件名生成
    const fileName = tosClient.generateFileName(mockMediaUrl, 'image/png');
    console.log('   生成文件名:', fileName);
    
    // 测试上传
    const uploadResult = await tosClient.uploadToTOS(mockMediaData, fileName, 'image/png');
    
    console.log('✅ 完整流程成功:');
    console.log('   公开URL:', uploadResult.url);
    console.log('   文件Key:', uploadResult.key);
    
    return { success: true, uploadResult };
    
  } catch (error) {
    console.error('❌ 完整流程失败:', error.message);
    return { success: false, error: error.message };
  }
}

// 测试URL可访问性
async function testURLAccessibility(url) {
  console.log('\n🧪 测试URL可访问性');
  console.log('-'.repeat(30));
  
  if (!url) {
    console.log('⚠️ 没有URL可测试');
    return false;
  }
  
  try {
    const axios = require('axios');
    
    console.log('🔗 测试URL:', url);
    
    const response = await axios.head(url, {
      timeout: 10000,
      validateStatus: function (status) {
        return status < 500; // 接受所有小于500的状态码
      }
    });
    
    console.log('📊 访问结果:');
    console.log('   状态码:', response.status);
    console.log('   内容类型:', response.headers['content-type']);
    console.log('   内容长度:', response.headers['content-length']);
    
    if (response.status === 200) {
      console.log('✅ URL可正常访问');
      return true;
    } else if (response.status === 403) {
      console.log('⚠️ URL存在但访问被拒绝（可能需要权限设置）');
      return false;
    } else {
      console.log('⚠️ URL访问异常，状态码:', response.status);
      return false;
    }
    
  } catch (error) {
    console.error('❌ URL访问测试失败:', error.message);
    
    if (error.code === 'ENOTFOUND') {
      console.error('   DNS解析失败 - 检查URL格式');
    } else if (error.code === 'ECONNABORTED') {
      console.error('   连接超时 - 检查网络连接');
    }
    
    return false;
  }
}

// 主函数
async function main() {
  console.log('🎯 TOS上传修复验证测试');
  console.log('=' .repeat(60));
  
  // 测试修复后的上传功能
  const uploadTest = await testFixedTOSUpload();
  
  // 等待2秒
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // 测试完整流程
  const flowTest = await testCompleteMediaFlow();
  
  // 等待2秒
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // 测试URL可访问性
  const testUrl = uploadTest.uploadResult?.url || flowTest.uploadResult?.url;
  const urlTest = await testURLAccessibility(testUrl);
  
  // 测试总结
  console.log('\n🎊 修复验证总结:');
  console.log('=' .repeat(40));
  console.log('1. 直接上传测试:', uploadTest.success ? '✅ 成功' : '❌ 失败');
  console.log('2. 完整流程测试:', flowTest.success ? '✅ 成功' : '❌ 失败');
  console.log('3. URL可访问性:', urlTest ? '✅ 可访问' : '⚠️ 不可访问');
  
  if (uploadTest.success || flowTest.success) {
    console.log('\n📊 上传统计:');
    const result = uploadTest.uploadResult || flowTest.uploadResult;
    if (result) {
      console.log('   公开URL:', result.url);
      console.log('   文件Key:', result.key);
      console.log('   存储桶:', result.bucket);
    }
  }
  
  if (!uploadTest.success && !flowTest.success) {
    console.log('\n❌ 主要问题:');
    console.log('   上传错误:', uploadTest.error);
    console.log('   流程错误:', flowTest.error);
  }
  
  console.log('\n📝 修复状态:');
  if (uploadTest.success && flowTest.success) {
    console.log('🎉 TOS上传功能已完全修复！');
  } else if (uploadTest.success || flowTest.success) {
    console.log('⚠️ TOS上传部分修复，需要进一步调试');
  } else {
    console.log('❌ TOS上传仍有问题，需要检查配置');
  }
  
  return uploadTest.success && flowTest.success;
}

main().then(success => {
  process.exit(success ? 0 : 1);
}).catch(error => {
  console.error('💥 测试执行失败:', error);
  process.exit(1);
});
