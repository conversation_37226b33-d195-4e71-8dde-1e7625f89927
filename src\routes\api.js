// API路由 - 为React前端提供数据服务
const express = require('express');
const router = express.Router();
const MessageHistory = require('../database/messageHistory');
const WebhookLogger = require('../database/webhookLogger');
const KnowledgeGraphService = require('../services/knowledgeGraphService');

// 初始化数据库服务
const messageHistory = new MessageHistory();
const webhookLogger = new WebhookLogger();
const knowledgeService = new KnowledgeGraphService();

// 中间件：添加CORS头
router.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
  
  if (req.method === 'OPTIONS') {
    res.sendStatus(200);
  } else {
    next();
  }
});

// 中间件：错误处理
const asyncHandler = (fn) => (req, res, next) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};

// 中间件：参数验证
const validatePagination = (req, res, next) => {
  const { page = 1, limit = 50 } = req.query;
  const pageNum = parseInt(page);
  const limitNum = parseInt(limit);
  
  if (isNaN(pageNum) || pageNum < 1) {
    return res.status(400).json({ error: 'Invalid page parameter' });
  }
  
  if (isNaN(limitNum) || limitNum < 1 || limitNum > 100) {
    return res.status(400).json({ error: 'Invalid limit parameter (1-100)' });
  }
  
  req.pagination = {
    page: pageNum,
    limit: limitNum,
    offset: (pageNum - 1) * limitNum
  };
  
  next();
};

// API根路径
router.get('/', (req, res) => {
  res.json({
    message: 'Webhook Chat History API',
    version: '1.0.0',
    endpoints: {
      conversations: '/api/conversations',
      messages: '/api/conversations/:id/messages',
      users: '/api/users',
      stats: '/api/stats',
      search: '/api/search',
      webhookLogs: '/api/webhook-logs',
      knowledge: '/api/knowledge'
    }
  });
});

// 获取对话列表
router.get('/conversations', validatePagination, asyncHandler(async (req, res) => {
  const { platform, startDate, endDate } = req.query;
  const { limit, offset } = req.pagination;
  
  console.log(`📋 API请求 - 获取对话列表: platform=${platform}, limit=${limit}, offset=${offset}`);
  
  try {
    const conversations = await messageHistory.getConversations(platform, limit, offset);
    
    // 格式化响应数据
    const formattedConversations = conversations.map(conv => ({
      id: conv.id,
      platform: conv.platform,
      channelId: conv.channel_id,
      user: {
        displayName: conv.display_name || conv.platform_user_id,
        platformUserId: conv.platform_user_id,
        phoneNumber: conv.phone_number
      },
      messageCount: conv.message_count || 0,
      lastMessage: conv.last_message,
      lastMessageTime: conv.last_message_time,
      createdAt: conv.created_at,
      updatedAt: conv.updated_at
    }));
    
    res.json({
      success: true,
      data: formattedConversations,
      pagination: {
        page: req.pagination.page,
        limit: req.pagination.limit,
        total: formattedConversations.length
      }
    });
    
    console.log(`✅ 对话列表获取成功 - 数量: ${formattedConversations.length}`);
  } catch (error) {
    console.error('❌ 获取对话列表失败:', error.message);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch conversations',
      message: error.message
    });
  }
}));

// 获取对话中的消息
router.get('/conversations/:id/messages', validatePagination, asyncHandler(async (req, res) => {
  const conversationId = parseInt(req.params.id);
  const { limit, offset } = req.pagination;
  
  if (isNaN(conversationId)) {
    return res.status(400).json({ error: 'Invalid conversation ID' });
  }
  
  console.log(`📋 API请求 - 获取对话消息: conversationId=${conversationId}, limit=${limit}, offset=${offset}`);
  
  try {
    const messages = await messageHistory.getConversationMessages(conversationId, limit, offset);
    
    // 格式化响应数据
    const formattedMessages = messages.map(msg => ({
      id: msg.id,
      conversationId: msg.conversation_id,
      messageType: msg.message_type,
      content: msg.content,
      senderId: msg.sender_id,
      recipientId: msg.recipient_id,
      messageSid: msg.message_sid,
      timestamp: msg.timestamp,
      metadata: msg.metadata ? JSON.parse(msg.metadata) : {},
      sender: {
        name: msg.sender_name,
        platformId: msg.sender_platform_id
      },
      createdAt: msg.created_at
    }));
    
    res.json({
      success: true,
      data: formattedMessages,
      pagination: {
        page: req.pagination.page,
        limit: req.pagination.limit,
        total: formattedMessages.length
      }
    });
    
    console.log(`✅ 对话消息获取成功 - 对话ID: ${conversationId}, 消息数: ${formattedMessages.length}`);
  } catch (error) {
    console.error('❌ 获取对话消息失败:', error.message);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch conversation messages',
      message: error.message
    });
  }
}));

// 获取用户列表
router.get('/users', validatePagination, asyncHandler(async (req, res) => {
  const { platform } = req.query;
  const { limit, offset } = req.pagination;
  
  console.log(`📋 API请求 - 获取用户列表: platform=${platform}, limit=${limit}, offset=${offset}`);
  
  try {
    const users = await messageHistory.getUsers(platform, limit, offset);
    
    // 格式化响应数据
    const formattedUsers = users.map(user => ({
      id: user.id,
      platformUserId: user.platform_user_id,
      platform: user.platform,
      displayName: user.display_name,
      phoneNumber: user.phone_number,
      profileData: user.profile_data ? JSON.parse(user.profile_data) : {},
      conversationCount: user.conversation_count || 0,
      messageCount: user.message_count || 0,
      lastActivity: user.last_activity,
      createdAt: user.created_at,
      updatedAt: user.updated_at
    }));
    
    res.json({
      success: true,
      data: formattedUsers,
      pagination: {
        page: req.pagination.page,
        limit: req.pagination.limit,
        total: formattedUsers.length
      }
    });
    
    console.log(`✅ 用户列表获取成功 - 数量: ${formattedUsers.length}`);
  } catch (error) {
    console.error('❌ 获取用户列表失败:', error.message);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch users',
      message: error.message
    });
  }
}));

// 搜索消息
router.get('/search', asyncHandler(async (req, res) => {
  const { q: query, platform, startDate, endDate, limit = 100 } = req.query;
  
  if (!query || query.trim().length < 2) {
    return res.status(400).json({ error: 'Search query must be at least 2 characters' });
  }
  
  const searchLimit = Math.min(parseInt(limit) || 100, 500);
  
  console.log(`📋 API请求 - 搜索消息: query="${query}", platform=${platform}, limit=${searchLimit}`);
  
  try {
    const messages = await messageHistory.searchMessages(query, platform, startDate, endDate, searchLimit);
    
    // 格式化响应数据
    const formattedMessages = messages.map(msg => ({
      id: msg.id,
      conversationId: msg.conversation_id,
      messageType: msg.message_type,
      content: msg.content,
      senderId: msg.sender_id,
      recipientId: msg.recipient_id,
      messageSid: msg.message_sid,
      timestamp: msg.timestamp,
      metadata: msg.metadata ? JSON.parse(msg.metadata) : {},
      platform: msg.platform,
      user: {
        displayName: msg.display_name,
        platformUserId: msg.platform_user_id
      },
      createdAt: msg.created_at
    }));
    
    res.json({
      success: true,
      data: formattedMessages,
      query: query,
      total: formattedMessages.length
    });
    
    console.log(`✅ 消息搜索成功 - 查询: "${query}", 结果数: ${formattedMessages.length}`);
  } catch (error) {
    console.error('❌ 搜索消息失败:', error.message);
    res.status(500).json({
      success: false,
      error: 'Failed to search messages',
      message: error.message
    });
  }
}));

// 获取统计信息
router.get('/stats', asyncHandler(async (req, res) => {
  const { platform, days = 30 } = req.query;
  const statsDays = Math.min(parseInt(days) || 30, 365);

  console.log(`📋 API请求 - 获取统计信息: platform=${platform}, days=${statsDays}`);

  try {
    const [generalStats, dailyStats, platformStats] = await Promise.all([
      messageHistory.getStatistics(platform, statsDays),
      messageHistory.getDailyMessageStats(platform, statsDays),
      messageHistory.getPlatformStats()
    ]);

    res.json({
      success: true,
      data: {
        general: {
          totalUsers: generalStats.total_users || 0,
          totalConversations: generalStats.total_conversations || 0,
          totalMessages: generalStats.total_messages || 0,
          incomingMessages: generalStats.incoming_messages || 0,
          outgoingMessages: generalStats.outgoing_messages || 0,
          recentMessages: generalStats.recent_messages || 0
        },
        daily: dailyStats.map(stat => ({
          date: stat.date,
          totalMessages: stat.total_messages || 0,
          incomingMessages: stat.incoming_messages || 0,
          outgoingMessages: stat.outgoing_messages || 0,
          activeConversations: stat.active_conversations || 0
        })),
        platforms: platformStats.map(stat => ({
          platform: stat.platform,
          userCount: stat.user_count || 0,
          conversationCount: stat.conversation_count || 0,
          messageCount: stat.message_count || 0,
          lastActivity: stat.last_activity
        }))
      },
      period: {
        days: statsDays,
        platform: platform || 'all'
      }
    });

    console.log(`✅ 统计信息获取成功 - 平台: ${platform || 'all'}, 天数: ${statsDays}`);
  } catch (error) {
    console.error('❌ 获取统计信息失败:', error.message);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch statistics',
      message: error.message
    });
  }
}));

// 获取webhook日志
router.get('/webhook-logs', validatePagination, asyncHandler(async (req, res) => {
  const { webhookType, processed, startDate, endDate } = req.query;
  const { limit, offset } = req.pagination;

  console.log(`📋 API请求 - 获取webhook日志: type=${webhookType}, limit=${limit}, offset=${offset}`);

  try {
    // 这里需要在WebhookLogger中添加获取日志的方法
    // 暂时返回空数据
    res.json({
      success: true,
      data: [],
      pagination: {
        page: req.pagination.page,
        limit: req.pagination.limit,
        total: 0
      }
    });

    console.log(`✅ Webhook日志获取成功`);
  } catch (error) {
    console.error('❌ 获取webhook日志失败:', error.message);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch webhook logs',
      message: error.message
    });
  }
}));

// ==================== 知识图谱管理 API ====================

// 获取知识图谱状态
router.get('/knowledge/status', asyncHandler(async (req, res) => {
  console.log('📋 API请求 - 获取知识图谱状态');

  try {
    const status = await knowledgeService.checkKnowledgeBaseStatus();

    res.json({
      success: true,
      data: status
    });

    console.log('✅ 知识图谱状态获取成功');
  } catch (error) {
    console.error('❌ 获取知识图谱状态失败:', error.message);
    res.status(500).json({
      success: false,
      error: 'Failed to get knowledge graph status',
      message: error.message
    });
  }
}));

// 获取知识图谱数据
router.get('/knowledge/data', asyncHandler(async (req, res) => {
  console.log('📋 API请求 - 获取知识图谱数据');

  try {
    // 获取所有知识数据
    const knowledgeData = await knowledgeService.debugListKnowledgeGraph();

    res.json({
      success: true,
      data: {
        edges: knowledgeData?.edges || [],
        count: knowledgeData?.edges?.length || 0
      }
    });

    console.log(`✅ 知识图谱数据获取成功 - 数量: ${knowledgeData?.edges?.length || 0}`);
  } catch (error) {
    console.error('❌ 获取知识图谱数据失败:', error.message);
    res.status(500).json({
      success: false,
      error: 'Failed to get knowledge graph data',
      message: error.message
    });
  }
}));

// 搜索知识图谱
router.get('/knowledge/search', asyncHandler(async (req, res) => {
  const { q: query } = req.query;

  if (!query || query.trim().length < 2) {
    return res.status(400).json({
      success: false,
      error: 'Search query must be at least 2 characters'
    });
  }

  console.log(`📋 API请求 - 搜索知识图谱: "${query}"`);

  try {
    const searchResult = await knowledgeService.searchKnowledge(query);

    res.json({
      success: true,
      data: searchResult
    });

    console.log(`✅ 知识图谱搜索成功 - 找到: ${searchResult.found}, 方法: ${searchResult.searchMethod}`);
  } catch (error) {
    console.error('❌ 搜索知识图谱失败:', error.message);
    res.status(500).json({
      success: false,
      error: 'Failed to search knowledge graph',
      message: error.message
    });
  }
}));

// 重置知识图谱
router.post('/knowledge/reset', asyncHandler(async (req, res) => {
  console.log('📋 API请求 - 重置知识图谱');

  try {
    const result = await knowledgeService.resetKnowledgeBase();

    if (result) {
      res.json({
        success: true,
        message: 'Knowledge graph reset successfully'
      });
      console.log('✅ 知识图谱重置成功');
    } else {
      res.status(500).json({
        success: false,
        error: 'Failed to reset knowledge graph'
      });
    }
  } catch (error) {
    console.error('❌ 重置知识图谱失败:', error.message);
    res.status(500).json({
      success: false,
      error: 'Failed to reset knowledge graph',
      message: error.message
    });
  }
}));

// 更新知识图谱
router.post('/knowledge/update', asyncHandler(async (req, res) => {
  console.log('📋 API请求 - 更新知识图谱');

  try {
    const result = await knowledgeService.updateKnowledgeBase();

    if (result) {
      res.json({
        success: true,
        message: 'Knowledge graph updated successfully'
      });
      console.log('✅ 知识图谱更新成功');
    } else {
      res.status(500).json({
        success: false,
        error: 'Failed to update knowledge graph'
      });
    }
  } catch (error) {
    console.error('❌ 更新知识图谱失败:', error.message);
    res.status(500).json({
      success: false,
      error: 'Failed to update knowledge graph',
      message: error.message
    });
  }
}));

// 错误处理中间件
router.use((error, req, res, next) => {
  console.error('❌ API错误:', error.message);
  res.status(500).json({
    success: false,
    error: 'Internal server error',
    message: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong'
  });
});

module.exports = router;
