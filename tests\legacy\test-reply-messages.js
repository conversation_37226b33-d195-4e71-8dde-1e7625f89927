// 测试回复消息的脚本
require('dotenv').config();
const twilio = require('twilio');

// 预定义的测试回复消息
const testReplies = [
  {
    trigger: 'hello',
    response: '👋 你好！欢迎使用Twilio WhatsApp Business API！\n\n✨ 这是一个自动回复测试\n🔄 双向通信正常工作\n⏰ 时间: {timestamp}'
  },
  {
    trigger: 'test',
    response: '🧪 系统测试报告:\n\n✅ API连接: 正常\n✅ 消息发送: 正常\n✅ 消息接收: 正常\n✅ 自动回复: 正常\n\n📊 测试时间: {timestamp}'
  },
  {
    trigger: 'help',
    response: '🆘 帮助菜单:\n\n📱 可用命令:\n• hello - 问候测试\n• test - 系统测试\n• status - 查看状态\n• info - 系统信息\n• demo - 功能演示\n\n💡 发送任意文本都会收到回复！'
  },
  {
    trigger: 'status',
    response: '📊 系统状态:\n\n🟢 服务状态: 在线\n🟢 API状态: 正常\n🟢 消息队列: 正常\n🟢 响应时间: <1秒\n\n📈 统计信息:\n• 处理消息: {messageCount}条\n• 最后更新: {timestamp}'
  },
  {
    trigger: 'info',
    response: 'ℹ️ 系统信息:\n\n🏢 服务: Twilio WhatsApp Business API\n🔧 环境: 沙盒测试\n📱 沙盒号码: +1 415 523 8886\n🌐 Webhook: 已配置\n⚡ 实时响应: 已启用\n\n🎯 这是一个完整的双向通信测试环境！'
  },
  {
    trigger: 'demo',
    response: '🎬 功能演示:\n\n1️⃣ 消息发送 ✅\n2️⃣ 消息接收 ✅\n3️⃣ 自动回复 ✅\n4️⃣ 状态跟踪 ✅\n5️⃣ 错误处理 ✅\n\n🚀 所有核心功能都已实现并正常工作！\n\n💡 尝试发送不同的消息测试各种功能。'
  }
];

// 消息计数器
let messageCount = 0;

// 发送回复消息
async function sendReplyMessage(toNumber, messageBody, triggerWord = null) {
  try {
    const accountSid = process.env.TWILIO_ACCOUNT_SID;
    const authToken = process.env.TWILIO_AUTH_TOKEN;
    const client = twilio(accountSid, authToken);

    messageCount++;

    // 查找匹配的回复模板
    let replyTemplate = null;
    if (triggerWord) {
      replyTemplate = testReplies.find(reply => 
        reply.trigger.toLowerCase() === triggerWord.toLowerCase()
      );
    }

    // 如果没有找到特定回复，使用默认回复
    let responseMessage;
    if (replyTemplate) {
      responseMessage = replyTemplate.response
        .replace('{timestamp}', new Date().toLocaleString())
        .replace('{messageCount}', messageCount);
    } else {
      responseMessage = `📨 收到您的消息: "${messageBody}"\n\n🤖 这是自动回复，证明系统正常工作！\n\n💡 尝试发送: hello, test, help, status, info, demo\n\n⏰ 回复时间: ${new Date().toLocaleString()}`;
    }

    // 发送回复
    const message = await client.messages.create({
      from: 'whatsapp:+***********',
      to: toNumber,
      body: responseMessage
    });

    console.log('✅ 回复消息发送成功:');
    console.log('   消息ID:', message.sid);
    console.log('   发送到:', toNumber);
    console.log('   触发词:', triggerWord || '通用回复');
    console.log('   回复内容:', responseMessage.substring(0, 50) + '...');
    console.log('   发送时间:', new Date().toLocaleString());

    return {
      success: true,
      messageId: message.sid,
      trigger: triggerWord,
      response: responseMessage
    };

  } catch (error) {
    console.error('❌ 回复消息发送失败:', error.message);
    return {
      success: false,
      error: error.message,
      code: error.code
    };
  }
}

// 分析消息内容，确定回复类型
function analyzeMessage(messageBody) {
  if (!messageBody) return null;

  const body = messageBody.toLowerCase().trim();
  
  // 查找匹配的触发词
  for (const reply of testReplies) {
    if (body.includes(reply.trigger)) {
      return reply.trigger;
    }
  }

  return null; // 没有特定匹配，使用默认回复
}

// 模拟接收消息并自动回复
async function simulateIncomingMessage(fromNumber, messageBody) {
  console.log('\n📱 模拟接收到消息:');
  console.log('   发送方:', fromNumber);
  console.log('   内容:', messageBody);
  console.log('   时间:', new Date().toLocaleString());

  // 分析消息内容
  const triggerWord = analyzeMessage(messageBody);
  console.log('   触发词:', triggerWord || '无特定触发词');

  // 发送回复
  const result = await sendReplyMessage(fromNumber, messageBody, triggerWord);
  
  if (result.success) {
    console.log('   ✅ 自动回复已发送');
  } else {
    console.log('   ❌ 自动回复发送失败:', result.error);
  }

  return result;
}

// 批量测试所有回复类型
async function testAllReplies(testNumber) {
  console.log('🧪 开始批量测试所有回复类型...\n');

  const results = [];

  for (const reply of testReplies) {
    console.log(`📤 测试触发词: "${reply.trigger}"`);
    
    const result = await simulateIncomingMessage(testNumber, reply.trigger);
    results.push(result);

    // 等待1秒避免发送过快
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  // 测试通用回复
  console.log('📤 测试通用回复');
  const genericResult = await simulateIncomingMessage(testNumber, '这是一条测试消息');
  results.push(genericResult);

  // 显示测试结果
  console.log('\n📊 测试结果汇总:');
  console.log('   总测试数:', results.length);
  console.log('   成功数:', results.filter(r => r.success).length);
  console.log('   失败数:', results.filter(r => !r.success).length);

  return results;
}

// 主函数
async function main() {
  console.log('🔄 WhatsApp回复消息测试工具');
  console.log('=' .repeat(50));

  const command = process.argv[2];
  const testNumber = process.argv[3];

  if (!testNumber) {
    console.log('❌ 请提供测试号码');
    console.log('\n使用方法:');
    console.log('   node test-reply-messages.js single +8613800138000 hello');
    console.log('   node test-reply-messages.js batch +8613800138000');
    console.log('   node test-reply-messages.js simulate +8613800138000 "test message"');
    console.log('\n命令说明:');
    console.log('   single - 发送单个回复');
    console.log('   batch - 批量测试所有回复类型');
    console.log('   simulate - 模拟接收消息并回复');
    return;
  }

  // 格式化号码
  let formattedNumber;
  if (testNumber.startsWith('whatsapp:')) {
    formattedNumber = testNumber;
  } else if (testNumber.startsWith('+')) {
    formattedNumber = `whatsapp:${testNumber}`;
  } else {
    formattedNumber = `whatsapp:+${testNumber}`;
  }

  try {
    switch (command) {
      case 'single':
        const triggerWord = process.argv[4] || 'hello';
        console.log(`📤 发送单个回复测试 (触发词: ${triggerWord})`);
        await sendReplyMessage(formattedNumber, triggerWord, triggerWord);
        break;

      case 'batch':
        console.log('📤 开始批量测试');
        await testAllReplies(formattedNumber);
        break;

      case 'simulate':
        const message = process.argv[4] || 'test message';
        console.log(`📤 模拟接收消息: "${message}"`);
        await simulateIncomingMessage(formattedNumber, message);
        break;

      default:
        console.log('❌ 未知命令:', command);
        console.log('可用命令: single, batch, simulate');
    }

  } catch (error) {
    console.error('💥 测试过程中发生错误:', error.message);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = {
  sendReplyMessage,
  analyzeMessage,
  simulateIncomingMessage,
  testAllReplies,
  testReplies
};
