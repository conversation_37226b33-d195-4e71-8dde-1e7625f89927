<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>获取Facebook Token</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f6fa;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .step {
            margin: 20px 0;
            padding: 15px;
            border-left: 4px solid #4267B2;
            background: #f8f9fa;
        }
        .code {
            background: #f1f3f4;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            margin: 10px 0;
        }
        .btn {
            background: #4267B2;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 10px 5px;
        }
        .btn:hover {
            background: #365899;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔑 获取Facebook短期Token指南</h1>
        
        <div class="step">
            <h3>步骤1: 访问Graph API Explorer</h3>
            <p>点击下面的链接访问Facebook Graph API Explorer：</p>
            <a href="https://developers.facebook.com/tools/explorer/" target="_blank" class="btn">
                打开Graph API Explorer
            </a>
        </div>

        <div class="step">
            <h3>步骤2: 选择您的应用</h3>
            <p>在页面右上角的"Facebook App"下拉菜单中选择您的应用。</p>
            <div class="warning">
                ⚠️ 如果没有应用，请先到 <a href="https://developers.facebook.com/apps/" target="_blank">Facebook开发者控制台</a> 创建一个应用。
            </div>
        </div>

        <div class="step">
            <h3>步骤3: 获取用户访问令牌</h3>
            <p>1. 点击"Get Token"按钮</p>
            <p>2. 选择"Get User Access Token"</p>
            <p>3. 在权限列表中勾选以下权限：</p>
            <ul>
                <li><code>pages_manage_posts</code> - 发布内容到页面</li>
                <li><code>pages_read_engagement</code> - 读取页面数据</li>
                <li><code>pages_show_list</code> - 列出管理的页面</li>
            </ul>
        </div>

        <div class="step">
            <h3>步骤4: 生成并复制Token</h3>
            <p>1. 点击"Generate Access Token"</p>
            <p>2. 登录Facebook并授权应用</p>
            <p>3. 复制生成的访问令牌</p>
            <p>4. 将Token粘贴到Web测试界面的"短期用户Token"输入框中</p>
        </div>

        <div class="step">
            <h3>步骤5: 使用Token</h3>
            <p>回到测试界面 <a href="http://localhost:3002" target="_blank">http://localhost:3002</a></p>
            <p>将获取的短期token粘贴到相应输入框，然后：</p>
            <ol>
                <li>点击"获取长期Token"</li>
                <li>点击"获取页面Token"</li>
                <li>现在可以发布文章了！</li>
            </ol>
        </div>

        <div class="warning">
            <h4>📝 重要提示：</h4>
            <ul>
                <li>短期token通常1-2小时后过期</li>
                <li>长期token可以使用60天</li>
                <li>页面token通常不会过期（除非撤销权限）</li>
                <li>请妥善保管您的token，不要泄露给他人</li>
            </ul>
        </div>

        <div class="step">
            <h3>🔧 如果遇到问题：</h3>
            <p><strong>权限不足：</strong> 确保您是页面的管理员</p>
            <p><strong>应用未审核：</strong> 开发模式下只能管理测试用户的页面</p>
            <p><strong>Token无效：</strong> 检查应用配置和权限设置</p>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <a href="http://localhost:3002" class="btn">返回测试界面</a>
            <a href="https://developers.facebook.com/docs/facebook-login/guides/access-tokens/" target="_blank" class="btn">查看官方文档</a>
        </div>
    </div>

    <script>
        // 检查URL中是否有access_token参数（OAuth回调）
        const urlParams = new URLSearchParams(window.location.search);
        const accessToken = urlParams.get('access_token');
        
        if (accessToken) {
            alert('检测到访问令牌！\n\n' + accessToken + '\n\n请复制此token到测试界面使用。');
        }
    </script>
</body>
</html>
