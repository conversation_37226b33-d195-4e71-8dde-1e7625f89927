# WhatsApp双向通信测试指南

## 🎯 目标
测试从您的WhatsApp发送消息到Twilio沙盒号码，并接收自动回复。

## ✅ 前提条件
- [x] 已完成Twilio WhatsApp沙盒设置
- [x] 收到确认消息："You are all set! The sandbox can now send/receive messages..."
- [x] 安装了ngrok：`npm install -g ngrok`
- [x] 安装了项目依赖：`npm install`

## 🚀 快速开始

### 方法1：一键启动（Windows）
```bash
# 双击运行
start-webhook-test.bat
```

### 方法2：手动步骤

#### 步骤1：启动Webhook接收服务器
```bash
# 在终端1中运行
node webhook-receiver.js
```

#### 步骤2：启动ngrok隧道
```bash
# 在终端2中运行
ngrok http 3002
```

#### 步骤3：配置Twilio Webhook
1. 复制ngrok提供的HTTPS URL（如：`https://abc123.ngrok.io`）
2. 访问：https://console.twilio.com/us1/develop/sms/try-it-out/whatsapp-learn
3. 找到"When a message comes in"字段
4. 输入：`https://abc123.ngrok.io/whatsapp-webhook`
5. 点击"Save Configuration"

#### 步骤4：测试消息发送
从您的WhatsApp发送消息到：`+1 415 523 8886`

## 📱 测试消息示例

### 基础测试消息
- `hello` - 问候回复
- `test` - 系统测试
- `help` - 帮助菜单
- `status` - 系统状态
- `info` - 系统信息
- `demo` - 功能演示

### 预期回复示例

**发送：** `hello`
**回复：**
```
👋 你好！欢迎使用Twilio WhatsApp Business API！

✨ 这是一个自动回复测试
🔄 双向通信正常工作
⏰ 时间: 2024-01-20 15:30:25
```

**发送：** `test`
**回复：**
```
🧪 系统测试报告:

✅ API连接: 正常
✅ 消息发送: 正常
✅ 消息接收: 正常
✅ 自动回复: 正常

📊 测试时间: 2024-01-20 15:30:25
```

**发送：** `status`
**回复：**
```
📊 系统状态:

🟢 服务状态: 在线
🟢 API状态: 正常
🟢 消息队列: 正常
🟢 响应时间: <1秒

📈 统计信息:
• 处理消息: 5条
• 最后更新: 2024-01-20 15:30:25
```

## 🔧 高级测试

### 1. 批量测试所有回复类型
```bash
node test-reply-messages.js batch +您的WhatsApp号码
```

### 2. 测试单个回复
```bash
node test-reply-messages.js single +您的WhatsApp号码 hello
```

### 3. 模拟接收消息
```bash
node test-reply-messages.js simulate +您的WhatsApp号码 "自定义测试消息"
```

## 📊 监控和调试

### 实时监控
- **健康检查：** http://localhost:3002/health
- **查看消息：** http://localhost:3002/messages
- **清空记录：** POST http://localhost:3002/clear-messages

### 检查服务器状态
```bash
node setup-webhook.js check
```

### 查看接收的消息
```bash
node setup-webhook.js messages
```

## 🔍 控制台输出示例

当您发送消息时，webhook-receiver.js会显示：

```
📱 收到WhatsApp消息!
==================================================
📋 消息详情:
   消息ID: SM1234567890abcdef
   发送方: whatsapp:+8613800138000
   接收方: whatsapp:+***********
   内容: hello
   发送者姓名: Your Name
   WhatsApp ID: 8613800138000
   媒体文件数: 0
   接收时间: 2024-01-20 15:30:25

🤖 准备发送自动回复: 👋 你好！欢迎使用Twilio WhatsApp Business API！...
✅ 自动回复发送成功: SM0987654321fedcba

📊 消息统计:
   总接收消息数: 1
   最近5条消息:
   1. hello... (2024-01-20T07:30:25.000Z)
```

## 🛠️ 故障排除

### 常见问题

1. **收不到消息**
   - 检查ngrok URL是否正确配置在Twilio控制台
   - 确认webhook-receiver.js正在运行
   - 验证您的号码仍在沙盒中

2. **自动回复不工作**
   - 检查Twilio凭据是否正确
   - 确认账户余额充足
   - 查看控制台错误信息

3. **ngrok连接问题**
   - 重启ngrok：`ngrok http 3002`
   - 更新Twilio控制台中的URL
   - 确保端口3002未被占用

### 调试命令

```bash
# 检查端口占用
netstat -ano | findstr :3002

# 测试本地webhook
curl -X POST http://localhost:3002/whatsapp-webhook -d "Body=test&From=whatsapp:+1234567890"

# 查看ngrok状态
curl http://localhost:4040/api/tunnels
```

## 📈 成功指标

✅ **完全成功的测试应该包括：**
1. Webhook服务器成功启动
2. ngrok隧道建立成功
3. Twilio Webhook配置成功
4. 从WhatsApp发送消息被接收
5. 控制台显示消息详情
6. 自动回复消息发送成功
7. 在WhatsApp中收到回复

## 🎉 下一步

测试成功后，您可以：
1. 自定义回复消息内容
2. 添加更多触发词和回复
3. 集成到您的业务系统
4. 设置生产环境Webhook
5. 申请WhatsApp Business API正式账户

---

**准备好开始测试了吗？** 运行 `start-webhook-test.bat` 或按照手动步骤开始！
