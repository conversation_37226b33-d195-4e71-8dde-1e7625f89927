const statusTrackingService = require('../services/statusTrackingService');
const logger = require('../config/logger');
const Joi = require('joi');

// 验证schemas
const messageStatusSchema = Joi.object({
  messageId: Joi.string().required(),
});

const syncStatusSchema = Joi.object({
  twilioSid: Joi.string().required(),
});

const conversationStatusSchema = Joi.object({
  conversationId: Joi.string().required(),
});

const deliveryReportSchema = Joi.object({
  startDate: Joi.date().iso().optional(),
  endDate: Joi.date().iso().optional(),
  phoneNumber: Joi.string().pattern(/^\+[1-9]\d{1,14}$/).optional(),
  campaignId: Joi.string().optional(),
  status: Joi.string().valid('queued', 'sending', 'sent', 'delivered', 'undelivered', 'failed').optional(),
  direction: Joi.string().valid('outbound', 'inbound').default('outbound'),
});

const realTimeStatusSchema = Joi.object({
  messageIds: Joi.array().items(Joi.string()).min(1).max(50).required(),
});

const batchSyncSchema = Joi.object({
  twilioSids: Joi.array().items(Joi.string()).min(1).max(20).required(),
});

class StatusController {

  /**
   * 获取消息状态
   */
  async getMessageStatus(req, res) {
    try {
      // 验证请求参数
      const { error, value } = messageStatusSchema.validate(req.params);
      if (error) {
        return res.status(400).json({
          success: false,
          error: 'Validation error',
          details: error.details[0].message,
        });
      }

      // 获取消息状态
      const status = await statusTrackingService.getMessageStatus(value.messageId);

      res.status(200).json({
        success: true,
        data: status,
      });

    } catch (error) {
      logger.error('Get message status API error:', error);
      
      if (error.message === 'Message not found') {
        return res.status(404).json({
          success: false,
          error: 'Message not found',
        });
      }

      res.status(500).json({
        success: false,
        error: 'Failed to get message status',
        message: error.message,
      });
    }
  }

  /**
   * 从Twilio同步消息状态
   */
  async syncMessageStatus(req, res) {
    try {
      // 验证请求数据
      const { error, value } = syncStatusSchema.validate(req.body);
      if (error) {
        return res.status(400).json({
          success: false,
          error: 'Validation error',
          details: error.details[0].message,
        });
      }

      // 同步状态
      const status = await statusTrackingService.syncMessageStatusFromTwilio(value.twilioSid);

      logger.info('Message status synced via API', { 
        twilioSid: value.twilioSid,
        ip: req.ip 
      });

      res.status(200).json({
        success: true,
        data: status,
      });

    } catch (error) {
      logger.error('Sync message status API error:', error);
      
      res.status(500).json({
        success: false,
        error: 'Failed to sync message status',
        message: error.message,
      });
    }
  }

  /**
   * 获取会话状态
   */
  async getConversationStatus(req, res) {
    try {
      // 验证请求参数
      const { error, value } = conversationStatusSchema.validate(req.params);
      if (error) {
        return res.status(400).json({
          success: false,
          error: 'Validation error',
          details: error.details[0].message,
        });
      }

      // 获取会话状态
      const status = await statusTrackingService.getConversationStatus(value.conversationId);

      res.status(200).json({
        success: true,
        data: status,
      });

    } catch (error) {
      logger.error('Get conversation status API error:', error);
      
      if (error.message === 'Conversation not found') {
        return res.status(404).json({
          success: false,
          error: 'Conversation not found',
        });
      }

      res.status(500).json({
        success: false,
        error: 'Failed to get conversation status',
        message: error.message,
      });
    }
  }

  /**
   * 获取传递报告
   */
  async getDeliveryReport(req, res) {
    try {
      // 验证请求参数
      const { error, value } = deliveryReportSchema.validate(req.query);
      if (error) {
        return res.status(400).json({
          success: false,
          error: 'Validation error',
          details: error.details[0].message,
        });
      }

      // 生成报告
      const report = await statusTrackingService.getDeliveryReport(value);

      res.status(200).json({
        success: true,
        data: report,
      });

    } catch (error) {
      logger.error('Get delivery report API error:', error);
      
      res.status(500).json({
        success: false,
        error: 'Failed to generate delivery report',
        message: error.message,
      });
    }
  }

  /**
   * 获取实时状态
   */
  async getRealTimeStatus(req, res) {
    try {
      // 验证请求数据
      const { error, value } = realTimeStatusSchema.validate(req.body);
      if (error) {
        return res.status(400).json({
          success: false,
          error: 'Validation error',
          details: error.details[0].message,
        });
      }

      // 获取实时状态
      const statusUpdates = await statusTrackingService.getRealTimeStatus(value.messageIds);

      res.status(200).json({
        success: true,
        data: statusUpdates,
      });

    } catch (error) {
      logger.error('Get real-time status API error:', error);
      
      res.status(500).json({
        success: false,
        error: 'Failed to get real-time status',
        message: error.message,
      });
    }
  }

  /**
   * 批量同步状态
   */
  async batchSyncStatus(req, res) {
    try {
      // 验证请求数据
      const { error, value } = batchSyncSchema.validate(req.body);
      if (error) {
        return res.status(400).json({
          success: false,
          error: 'Validation error',
          details: error.details[0].message,
        });
      }

      // 批量同步
      const result = await statusTrackingService.batchSyncStatus(value.twilioSids);

      logger.info('Batch status sync completed via API', { 
        total: value.twilioSids.length,
        success: result.success,
        failed: result.failed,
        ip: req.ip 
      });

      res.status(200).json({
        success: true,
        data: result,
      });

    } catch (error) {
      logger.error('Batch sync status API error:', error);
      
      res.status(500).json({
        success: false,
        error: 'Failed to batch sync status',
        message: error.message,
      });
    }
  }
}

module.exports = new StatusController();
