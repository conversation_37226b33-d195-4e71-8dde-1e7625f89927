const { ZepClient } = require('@getzep/zep-cloud');
const fs = require('fs').promises;
const path = require('path');
require('dotenv').config();

/**
 * Zep 知识库管理工具
 * 提供知识库的创建、导入、搜索等功能
 */

class ZepKnowledgeManager {
  constructor() {
    if (!process.env.ZEP_API_KEY) {
      throw new Error('❌ 缺少 ZEP_API_KEY 环境变量');
    }

    this.zepClient = new ZepClient({
      apiKey: process.env.ZEP_API_KEY
    });
  }

  // 创建知识库集合
  async createCollection(collectionName, description = '') {
    console.log(`📁 创建知识库集合: ${collectionName}`);
    
    try {
      const collection = await this.zepClient.document.addCollection({
        name: collectionName,
        description: description || `知识库集合 - ${collectionName}`,
        metadata: {
          created: new Date().toISOString(),
          type: 'knowledge-base'
        }
      });
      
      console.log(`✅ 集合创建成功: ${collection.name}`);
      return collection;
    } catch (error) {
      if (error.message.includes('already exists')) {
        console.log('📁 集合已存在，使用现有集合');
        return await this.zepClient.document.getCollection(collectionName);
      } else {
        throw error;
      }
    }
  }

  // 从文件导入知识
  async importFromFile(collectionName, filePath) {
    console.log(`📄 从文件导入知识: ${filePath}`);
    
    try {
      const content = await fs.readFile(filePath, 'utf-8');
      const fileName = path.basename(filePath, path.extname(filePath));
      
      const document = await this.zepClient.document.addDocument(collectionName, {
        documentId: `file-${fileName}-${Date.now()}`,
        content: content,
        metadata: {
          source: 'file-import',
          fileName: fileName,
          filePath: filePath,
          imported: new Date().toISOString()
        }
      });
      
      console.log(`✅ 文件导入成功: ${fileName}`);
      return document;
    } catch (error) {
      console.error(`❌ 文件导入失败: ${error.message}`);
      throw error;
    }
  }

  // 导入文本内容
  async importText(collectionName, documentId, content, metadata = {}) {
    console.log(`📝 导入文本内容: ${documentId}`);
    
    try {
      const document = await this.zepClient.document.addDocument(collectionName, {
        documentId: documentId,
        content: content,
        metadata: {
          ...metadata,
          imported: new Date().toISOString(),
          type: 'text-import'
        }
      });
      
      console.log(`✅ 文本导入成功: ${documentId}`);
      return document;
    } catch (error) {
      console.error(`❌ 文本导入失败: ${error.message}`);
      throw error;
    }
  }

  // 批量导入预定义知识
  async importPredefinedKnowledge(collectionName) {
    console.log('📚 导入预定义知识库...');
    
    const knowledgeBase = {
      'company-info': {
        content: `# 公司信息

## 公司简介
我们是一家专注于人工智能和通信技术的创新公司，致力于为客户提供智能化的解决方案。

## 主要业务
1. **智能客服系统**: 基于AI的自动化客户服务
2. **通信集成服务**: Twilio、WhatsApp等平台集成
3. **知识管理系统**: 企业知识库和问答系统
4. **AI咨询服务**: 人工智能技术咨询和实施

## 技术栈
- 后端: Node.js, Express, MongoDB
- AI/ML: OpenAI, 火山方舟, Zep Cloud
- 通信: Twilio, WhatsApp Business API
- 前端: React, Vue.js
- 云服务: AWS, 阿里云

## 联系方式
- 邮箱: <EMAIL>
- 电话: 400-123-4567
- 地址: 北京市朝阳区科技园区`,
        metadata: { category: '公司信息', tags: ['公司', '业务', '技术栈'] }
      },

      'ai-services': {
        content: `# AI服务介绍

## 智能客服机器人
我们的智能客服机器人具备以下特性：
- 24/7全天候服务
- 多语言支持
- 上下文理解能力
- 情感分析
- 人工客服无缝转接

## 知识库问答系统
基于Zep Cloud构建的企业级知识库：
- 文档自动索引和检索
- 语义搜索能力
- 多模态内容支持
- 实时知识更新
- 权限管理

## 对话式AI
- 自然语言理解
- 多轮对话管理
- 个性化回复
- 业务流程自动化
- 数据分析和洞察

## 技术优势
1. **先进的NLP技术**: 使用最新的大语言模型
2. **高可用性**: 99.9%的服务可用性保证
3. **快速响应**: 平均响应时间<200ms
4. **安全可靠**: 企业级安全保障`,
        metadata: { category: 'AI服务', tags: ['AI', '客服', '知识库', 'NLP'] }
      },

      'technical-support': {
        content: `# 技术支持指南

## 常见问题解答

### API集成问题
Q: 如何获取API密钥？
A: 请联系我们的技术支持团队，我们会为您提供专用的API密钥和详细的集成文档。

Q: API调用频率限制是多少？
A: 标准版本每分钟1000次调用，企业版可根据需求定制。

Q: 支持哪些编程语言？
A: 我们提供Node.js、Python、Java、PHP等多种语言的SDK。

### 功能使用问题
Q: 如何训练自定义模型？
A: 您可以通过我们的管理后台上传训练数据，系统会自动进行模型训练和优化。

Q: 是否支持私有化部署？
A: 是的，我们提供私有化部署方案，确保数据安全和合规性。

## 技术支持渠道
- 技术支持邮箱: <EMAIL>
- 在线客服: 工作日9:00-18:00
- 技术文档: https://docs.company.com
- 开发者社区: https://community.company.com

## 服务等级协议(SLA)
- 响应时间: 工作时间内2小时响应
- 解决时间: 一般问题24小时内解决
- 紧急问题: 4小时内响应，12小时内解决`,
        metadata: { category: '技术支持', tags: ['FAQ', '支持', 'API', 'SLA'] }
      },

      'pricing-plans': {
        content: `# 产品定价方案

## 基础版 (免费)
- 每月1000次API调用
- 基础客服机器人
- 邮件支持
- 适合个人开发者和小型项目

## 专业版 (¥999/月)
- 每月50000次API调用
- 高级AI功能
- 知识库管理
- 电话和邮件支持
- 适合中小企业

## 企业版 (¥2999/月)
- 无限API调用
- 定制化AI模型
- 私有化部署选项
- 专属技术支持
- SLA保障
- 适合大型企业

## 定制版 (联系销售)
- 完全定制化解决方案
- 专业实施服务
- 培训和咨询
- 长期技术支持合同

## 付费方式
- 月付/年付
- 支持支付宝、微信支付、银行转账
- 企业客户支持发票

## 升级说明
- 随时可以升级套餐
- 按比例退还未使用费用
- 企业版用户享受优先支持`,
        metadata: { category: '定价', tags: ['价格', '套餐', '付费', '企业'] }
      }
    };

    const results = [];
    for (const [docId, docData] of Object.entries(knowledgeBase)) {
      try {
        const result = await this.importText(
          collectionName, 
          docId, 
          docData.content, 
          docData.metadata
        );
        results.push(result);
      } catch (error) {
        console.error(`导入 ${docId} 失败:`, error.message);
      }
    }

    console.log(`✅ 预定义知识库导入完成，成功导入 ${results.length} 个文档`);
    return results;
  }

  // 搜索知识库
  async searchKnowledge(collectionName, query, limit = 5) {
    console.log(`🔍 搜索知识库: "${query}"`);
    
    try {
      const searchResults = await this.zepClient.document.search(collectionName, {
        text: query,
        limit: limit,
        searchType: 'similarity'
      });

      if (searchResults.results && searchResults.results.length > 0) {
        console.log(`找到 ${searchResults.results.length} 个相关结果:`);
        
        searchResults.results.forEach((result, index) => {
          console.log(`\n${index + 1}. 文档: ${result.document.documentId}`);
          console.log(`   相似度: ${result.score?.toFixed(3)}`);
          console.log(`   内容预览: ${result.content.substring(0, 150)}...`);
          
          if (result.document.metadata) {
            const metadata = result.document.metadata;
            if (metadata.category) console.log(`   分类: ${metadata.category}`);
            if (metadata.tags) console.log(`   标签: ${metadata.tags.join(', ')}`);
          }
        });
        
        return searchResults.results;
      } else {
        console.log('未找到相关结果');
        return [];
      }
    } catch (error) {
      console.error(`搜索失败: ${error.message}`);
      return [];
    }
  }

  // 获取集合信息
  async getCollectionInfo(collectionName) {
    try {
      const collection = await this.zepClient.document.getCollection(collectionName);
      console.log('\n📊 知识库信息:');
      console.log(`   名称: ${collection.name}`);
      console.log(`   描述: ${collection.description}`);
      console.log(`   文档数量: ${collection.documentCount || '未知'}`);
      console.log(`   创建时间: ${collection.createdAt || '未知'}`);
      return collection;
    } catch (error) {
      console.error(`获取集合信息失败: ${error.message}`);
      return null;
    }
  }

  // 删除知识库集合
  async deleteCollection(collectionName) {
    console.log(`🗑️ 删除知识库集合: ${collectionName}`);
    
    try {
      await this.zepClient.document.deleteCollection(collectionName);
      console.log(`✅ 集合删除成功: ${collectionName}`);
    } catch (error) {
      console.error(`删除集合失败: ${error.message}`);
      throw error;
    }
  }
}

// 命令行工具
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  
  if (!command) {
    console.log(`
📚 Zep知识库管理工具

使用方法:
  node zep-knowledge-manager.js create <集合名称> [描述]     # 创建知识库集合
  node zep-knowledge-manager.js import <集合名称>           # 导入预定义知识
  node zep-knowledge-manager.js search <集合名称> <查询>    # 搜索知识库
  node zep-knowledge-manager.js info <集合名称>             # 查看集合信息
  node zep-knowledge-manager.js delete <集合名称>           # 删除集合

示例:
  node zep-knowledge-manager.js create my-kb "我的知识库"
  node zep-knowledge-manager.js import my-kb
  node zep-knowledge-manager.js search my-kb "AI服务"
    `);
    return;
  }

  const manager = new ZepKnowledgeManager();

  try {
    switch (command) {
      case 'create':
        const collectionName = args[1];
        const description = args[2] || '';
        if (!collectionName) {
          console.error('❌ 请提供集合名称');
          return;
        }
        await manager.createCollection(collectionName, description);
        break;

      case 'import':
        const importCollection = args[1];
        if (!importCollection) {
          console.error('❌ 请提供集合名称');
          return;
        }
        await manager.importPredefinedKnowledge(importCollection);
        break;

      case 'search':
        const searchCollection = args[1];
        const query = args.slice(2).join(' ');
        if (!searchCollection || !query) {
          console.error('❌ 请提供集合名称和搜索查询');
          return;
        }
        await manager.searchKnowledge(searchCollection, query);
        break;

      case 'info':
        const infoCollection = args[1];
        if (!infoCollection) {
          console.error('❌ 请提供集合名称');
          return;
        }
        await manager.getCollectionInfo(infoCollection);
        break;

      case 'delete':
        const deleteCollection = args[1];
        if (!deleteCollection) {
          console.error('❌ 请提供集合名称');
          return;
        }
        await manager.deleteCollection(deleteCollection);
        break;

      default:
        console.error(`❌ 未知命令: ${command}`);
    }
  } catch (error) {
    console.error(`❌ 操作失败: ${error.message}`);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(console.error);
}

module.exports = ZepKnowledgeManager;
