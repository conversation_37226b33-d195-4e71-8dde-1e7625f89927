const crypto = require('crypto');
const moment = require('moment');

/**
 * 生成随机字符串
 * @param {number} length - 字符串长度
 * @returns {string} 随机字符串
 */
const generateRandomString = (length = 32) => {
  return crypto.randomBytes(length).toString('hex');
};

/**
 * 生成会话ID
 * @param {string} phone1 - 电话号码1
 * @param {string} phone2 - 电话号码2
 * @returns {string} 会话ID
 */
const generateConversationId = (phone1, phone2) => {
  const phones = [phone1, phone2].sort();
  const hash = crypto.createHash('md5').update(phones.join('-')).digest('hex');
  return `conv_${hash.substring(0, 16)}`;
};

/**
 * 格式化电话号码显示
 * @param {string} phoneNumber - 电话号码
 * @returns {string} 格式化后的电话号码
 */
const formatPhoneNumber = (phoneNumber) => {
  if (!phoneNumber) return '';
  
  // 移除+号进行格式化
  const cleaned = phoneNumber.replace(/^\+/, '');
  
  // 美国号码格式化
  if (cleaned.length === 11 && cleaned.startsWith('1')) {
    return `+1 (${cleaned.substring(1, 4)}) ${cleaned.substring(4, 7)}-${cleaned.substring(7)}`;
  }
  
  // 其他国家保持原格式
  return phoneNumber;
};

/**
 * 计算消息发送成本
 * @param {Array} messages - 消息数组
 * @returns {Object} 成本统计
 */
const calculateMessageCosts = (messages) => {
  const costs = {
    total: 0,
    count: 0,
    byStatus: {},
    currency: 'USD'
  };
  
  messages.forEach(message => {
    if (message.price && message.priceUnit) {
      const price = parseFloat(message.price);
      if (!isNaN(price)) {
        costs.total += price;
        costs.count += 1;
        
        if (!costs.byStatus[message.status]) {
          costs.byStatus[message.status] = { total: 0, count: 0 };
        }
        costs.byStatus[message.status].total += price;
        costs.byStatus[message.status].count += 1;
      }
    }
  });
  
  costs.total = parseFloat(costs.total.toFixed(4));
  
  return costs;
};

/**
 * 格式化时间差
 * @param {Date} startTime - 开始时间
 * @param {Date} endTime - 结束时间
 * @returns {string} 格式化的时间差
 */
const formatTimeDifference = (startTime, endTime = new Date()) => {
  const duration = moment.duration(moment(endTime).diff(moment(startTime)));
  
  if (duration.asSeconds() < 60) {
    return `${Math.floor(duration.asSeconds())}s`;
  } else if (duration.asMinutes() < 60) {
    return `${Math.floor(duration.asMinutes())}m`;
  } else if (duration.asHours() < 24) {
    return `${Math.floor(duration.asHours())}h`;
  } else {
    return `${Math.floor(duration.asDays())}d`;
  }
};

/**
 * 分页计算
 * @param {number} total - 总数
 * @param {number} limit - 每页数量
 * @param {number} offset - 偏移量
 * @returns {Object} 分页信息
 */
const calculatePagination = (total, limit, offset) => {
  const totalPages = Math.ceil(total / limit);
  const currentPage = Math.floor(offset / limit) + 1;
  
  return {
    total,
    limit,
    offset,
    totalPages,
    currentPage,
    hasNext: currentPage < totalPages,
    hasPrev: currentPage > 1,
    nextOffset: currentPage < totalPages ? offset + limit : null,
    prevOffset: currentPage > 1 ? Math.max(0, offset - limit) : null,
  };
};

/**
 * 安全的JSON解析
 * @param {string} jsonString - JSON字符串
 * @param {*} defaultValue - 默认值
 * @returns {*} 解析结果或默认值
 */
const safeJsonParse = (jsonString, defaultValue = null) => {
  try {
    return JSON.parse(jsonString);
  } catch {
    return defaultValue;
  }
};

/**
 * 延迟执行
 * @param {number} ms - 延迟毫秒数
 * @returns {Promise} Promise对象
 */
const delay = (ms) => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

/**
 * 重试执行函数
 * @param {Function} fn - 要执行的函数
 * @param {number} maxRetries - 最大重试次数
 * @param {number} delayMs - 重试间隔
 * @returns {Promise} 执行结果
 */
const retryAsync = async (fn, maxRetries = 3, delayMs = 1000) => {
  let lastError;
  
  for (let i = 0; i <= maxRetries; i++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;
      if (i < maxRetries) {
        await delay(delayMs * Math.pow(2, i)); // 指数退避
      }
    }
  }
  
  throw lastError;
};

/**
 * 清理对象中的空值
 * @param {Object} obj - 原始对象
 * @returns {Object} 清理后的对象
 */
const removeEmptyValues = (obj) => {
  const cleaned = {};
  
  for (const [key, value] of Object.entries(obj)) {
    if (value !== null && value !== undefined && value !== '') {
      if (typeof value === 'object' && !Array.isArray(value)) {
        const cleanedNested = removeEmptyValues(value);
        if (Object.keys(cleanedNested).length > 0) {
          cleaned[key] = cleanedNested;
        }
      } else {
        cleaned[key] = value;
      }
    }
  }
  
  return cleaned;
};

/**
 * 生成API响应格式
 * @param {boolean} success - 是否成功
 * @param {*} data - 数据
 * @param {string} error - 错误信息
 * @param {Object} meta - 元数据
 * @returns {Object} API响应对象
 */
const createApiResponse = (success, data = null, error = null, meta = {}) => {
  const response = { success };
  
  if (success && data !== null) {
    response.data = data;
  }
  
  if (!success && error) {
    response.error = error;
  }
  
  if (Object.keys(meta).length > 0) {
    response.meta = meta;
  }
  
  return response;
};

module.exports = {
  generateRandomString,
  generateConversationId,
  formatPhoneNumber,
  calculateMessageCosts,
  formatTimeDifference,
  calculatePagination,
  safeJsonParse,
  delay,
  retryAsync,
  removeEmptyValues,
  createApiResponse,
};
