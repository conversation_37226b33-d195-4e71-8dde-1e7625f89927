// Slack Bot 诊断工具
require('dotenv').config();
const axios = require('axios');

async function diagnoseSlackBot() {
  console.log('🔍 Slack Bot 诊断工具');
  console.log('=' .repeat(50));

  // 检查环境变量
  console.log('🔧 检查环境变量:');
  console.log(`   SLACK_BOT_TOKEN: ${process.env.SLACK_BOT_TOKEN ? '✅ 已配置' : '❌ 未配置'}`);
  console.log(`   SLACK_CLIENT_ID: ${process.env.SLACK_CLIENT_ID ? '✅ 已配置' : '❌ 未配置'}`);
  console.log(`   SLACK_CLIENT_SECRET: ${process.env.SLACK_CLIENT_SECRET ? '✅ 已配置' : '❌ 未配置'}`);
  console.log('');

  if (!process.env.SLACK_BOT_TOKEN) {
    console.log('❌ 请配置 SLACK_BOT_TOKEN');
    return;
  }

  try {
    // 1. 测试 Bot 身份验证
    console.log('🤖 测试 Bot 身份验证...');
    const authResponse = await axios.post('https://slack.com/api/auth.test', {}, {
      headers: {
        'Authorization': `Bearer ${process.env.SLACK_BOT_TOKEN}`,
        'Content-Type': 'application/json'
      }
    });

    if (authResponse.data.ok) {
      console.log('✅ Bot 身份验证成功');
      console.log(`   Bot ID: ${authResponse.data.bot_id}`);
      console.log(`   用户ID: ${authResponse.data.user_id}`);
      console.log(`   团队: ${authResponse.data.team}`);
      console.log(`   用户: ${authResponse.data.user}`);
    } else {
      console.log('❌ Bot 身份验证失败:', authResponse.data.error);
      return;
    }

    // 2. 检查 Bot 权限
    console.log('');
    console.log('🔐 检查 Bot 权限...');
    const scopesResponse = await axios.post('https://slack.com/api/auth.test', {}, {
      headers: {
        'Authorization': `Bearer ${process.env.SLACK_BOT_TOKEN}`,
        'Content-Type': 'application/json'
      }
    });

    // 3. 获取 Bot 信息
    console.log('');
    console.log('📋 获取 Bot 详细信息...');
    const botInfoResponse = await axios.post('https://slack.com/api/bots.info', {
      bot: authResponse.data.bot_id
    }, {
      headers: {
        'Authorization': `Bearer ${process.env.SLACK_BOT_TOKEN}`,
        'Content-Type': 'application/json'
      }
    });

    if (botInfoResponse.data.ok) {
      const bot = botInfoResponse.data.bot;
      console.log('✅ Bot 信息获取成功');
      console.log(`   名称: ${bot.name}`);
      console.log(`   显示名: ${bot.display_name || 'N/A'}`);
      console.log(`   状态: ${bot.deleted ? '已删除' : '活跃'}`);
    } else {
      console.log('⚠️ 无法获取 Bot 信息:', botInfoResponse.data.error);
    }

    // 4. 列出 Bot 可访问的频道
    console.log('');
    console.log('📺 检查 Bot 可访问的频道...');
    const channelsResponse = await axios.post('https://slack.com/api/conversations.list', {
      types: 'public_channel,private_channel,im,mpim',
      limit: 10
    }, {
      headers: {
        'Authorization': `Bearer ${process.env.SLACK_BOT_TOKEN}`,
        'Content-Type': 'application/json'
      }
    });

    if (channelsResponse.data.ok) {
      console.log('✅ 频道列表获取成功');
      const channels = channelsResponse.data.channels;
      
      if (channels.length > 0) {
        console.log('   可访问的频道:');
        channels.slice(0, 5).forEach(channel => {
          console.log(`   - ${channel.name || channel.id} (${channel.id}) - ${channel.is_member ? '已加入' : '未加入'}`);
        });
        
        if (channels.length > 5) {
          console.log(`   ... 还有 ${channels.length - 5} 个频道`);
        }
      } else {
        console.log('   ⚠️ Bot 没有访问任何频道的权限');
      }
    } else {
      console.log('❌ 无法获取频道列表:', channelsResponse.data.error);
      
      if (channelsResponse.data.error === 'missing_scope') {
        console.log('   💡 建议: Bot 需要以下权限之一:');
        console.log('      - channels:read (公开频道)');
        console.log('      - groups:read (私有频道)');
        console.log('      - im:read (私信)');
      }
    }

    // 5. 测试打开私聊通道
    const testUserId = 'U053CTYEARZ'; // 从错误日志中获取的用户ID

    console.log('');
    console.log(`🔗 测试打开与用户 ${testUserId} 的私聊通道...`);

    const openDMResponse = await axios.post('https://slack.com/api/conversations.open', {
      users: testUserId
    }, {
      headers: {
        'Authorization': `Bearer ${process.env.SLACK_BOT_TOKEN}`,
        'Content-Type': 'application/json'
      }
    });

    let dmChannelId = null;
    if (openDMResponse.data.ok) {
      dmChannelId = openDMResponse.data.channel.id;
      console.log('✅ 私聊通道打开成功');
      console.log(`   通道ID: ${dmChannelId}`);
    } else {
      console.log('❌ 打开私聊通道失败:', openDMResponse.data.error);

      if (openDMResponse.data.error === 'missing_scope') {
        console.log('   💡 解决方案: 需要添加 im:write 权限');
      }
    }

    // 6. 测试发送消息到私聊通道
    if (dmChannelId) {
      console.log('');
      console.log(`💬 测试发送消息到私聊通道 ${dmChannelId}...`);

      const testMessageResponse = await axios.post('https://slack.com/api/chat.postMessage', {
        channel: dmChannelId,
        text: '🧪 这是一条测试消息，用于验证 Bot 私聊权限'
      }, {
        headers: {
          'Authorization': `Bearer ${process.env.SLACK_BOT_TOKEN}`,
          'Content-Type': 'application/json'
        }
      });

      if (testMessageResponse.data.ok) {
        console.log('✅ 私聊消息发送成功');
        console.log(`   消息时间戳: ${testMessageResponse.data.ts}`);
      } else {
        console.log('❌ 私聊消息发送失败:', testMessageResponse.data.error);

        switch (testMessageResponse.data.error) {
          case 'channel_not_found':
            console.log('   💡 解决方案: 私聊通道不存在');
            console.log('      1. 确保用户ID正确');
            console.log('      2. 检查 Bot 是否有权限与该用户私聊');
            break;
          case 'missing_scope':
            console.log('   💡 解决方案: Bot 缺少必要权限');
            console.log('      1. 添加 chat:write 权限');
            console.log('      2. 添加 im:write 权限');
            console.log('      3. 重新安装 Bot 到工作区');
            break;
          default:
            console.log('   💡 请检查 Bot 配置和权限设置');
        }
      }
    }

    // 7. 测试发送消息到原始频道ID（用于对比）
    const originalChannelId = 'D097W5SPBLM'; // 从错误日志中获取的原始频道ID

    console.log('');
    console.log(`📺 测试发送消息到原始频道 ${originalChannelId}...`);

    const originalTestResponse = await axios.post('https://slack.com/api/chat.postMessage', {
      channel: originalChannelId,
      text: '🧪 测试原始频道ID发送消息'
    }, {
      headers: {
        'Authorization': `Bearer ${process.env.SLACK_BOT_TOKEN}`,
        'Content-Type': 'application/json'
      }
    });

    if (originalTestResponse.data.ok) {
      console.log('✅ 原始频道消息发送成功');
      console.log(`   消息时间戳: ${originalTestResponse.data.ts}`);
    } else {
      console.log('❌ 原始频道消息发送失败:', originalTestResponse.data.error);
      console.log('   💡 这证实了需要使用 conversations.open 来处理私聊');
    }

  } catch (error) {
    console.error('❌ 诊断过程中出现错误:', error.message);
    
    if (error.response) {
      console.log('   HTTP状态:', error.response.status);
      console.log('   错误详情:', error.response.data);
    }
  }

  console.log('');
  console.log('📋 诊断完成！');
  console.log('');
  console.log('🔧 常见问题解决方案:');
  console.log('1. Bot 权限不足 (推荐解决方案):');
  console.log('   - 检查 Slack App 的 OAuth & Permissions 设置');
  console.log('   - 确保有以下权限:');
  console.log('     * chat:write (发送消息)');
  console.log('     * channels:read (读取频道)');
  console.log('     * groups:read (读取私有频道)');
  console.log('     * im:read (读取私信)');
  console.log('     * im:write (发送私信) - 重要!');
  console.log('     * users:read (读取用户信息)');
  console.log('   - 重新安装 App 到工作区');
  console.log('');
  console.log('2. 私聊通道处理:');
  console.log('   - 对于私信，需要先调用 conversations.open');
  console.log('   - 然后使用返回的通道ID发送消息');
  console.log('   - 这是 Slack API 的标准流程');
  console.log('');
  console.log('3. Bot 未被邀请到频道:');
  console.log('   - 在频道中输入: /invite @your-bot-name');
  console.log('   - 或在频道设置中手动添加 Bot');
  console.log('');
  console.log('4. Token 配置错误:');
  console.log('   - 确保使用的是 Bot User OAuth Token (xoxb- 开头)');
  console.log('   - 检查 Token 是否已过期或被撤销');
}

// 运行诊断
diagnoseSlackBot();
