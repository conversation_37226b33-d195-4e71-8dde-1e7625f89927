const { Message, Conversation } = require('../models');
const logger = require('../config/logger');
const { v4: uuidv4 } = require('uuid');

class WebhookService {

  /**
   * 处理消息状态更新webhook
   * @param {Object} webhookData - Twilio webhook数据
   */
  async handleMessageStatus(webhookData) {
    try {
      const {
        MessageSid,
        MessageStatus,
        ErrorCode,
        ErrorMessage,
        Price,
        PriceUnit,
        From,
        To,
        Body,
      } = webhookData;

      logger.info('Processing message status webhook', {
        messageSid: MessageSid,
        status: MessageStatus,
        errorCode: ErrorCode,
      });

      // 查找消息记录
      const message = await Message.findOne({ twilioSid: MessageSid });
      
      if (!message) {
        logger.warn('Message not found for status update', { messageSid: MessageSid });
        return { success: false, error: 'Message not found' };
      }

      // 更新消息状态
      const updateData = {
        status: MessageStatus,
        price: Price,
        priceUnit: PriceUnit,
      };

      // 处理错误信息
      if (ErrorCode) {
        updateData.errorCode = ErrorCode;
        updateData.errorMessage = ErrorMessage;
      }

      await message.updateStatus(MessageStatus, updateData);

      logger.info('Message status updated successfully', {
        messageId: message._id,
        messageSid: MessageSid,
        oldStatus: message.status,
        newStatus: MessageStatus,
      });

      return { success: true, messageId: message._id };

    } catch (error) {
      logger.error('Failed to handle message status webhook:', error);
      throw error;
    }
  }

  /**
   * 处理入站消息webhook
   * @param {Object} webhookData - Twilio webhook数据
   */
  async handleIncomingMessage(webhookData) {
    try {
      const {
        MessageSid,
        From,
        To,
        Body,
        NumMedia,
        MediaUrl0,
        MediaUrl1,
        MediaUrl2,
        MediaUrl3,
        MediaUrl4,
        MediaContentType0,
        MediaContentType1,
        MediaContentType2,
        MediaContentType3,
        MediaContentType4,
      } = webhookData;

      logger.info('Processing incoming message webhook', {
        messageSid: MessageSid,
        from: From,
        to: To,
        hasMedia: parseInt(NumMedia) > 0,
      });

      // 收集媒体文件URL
      const mediaUrls = [];
      for (let i = 0; i < parseInt(NumMedia || 0); i++) {
        const mediaUrl = webhookData[`MediaUrl${i}`];
        if (mediaUrl) {
          mediaUrls.push(mediaUrl);
        }
      }

      // 获取或创建会话ID
      const conversationId = await this.getOrCreateConversationId(From, To);

      // 创建入站消息记录
      const message = new Message({
        twilioSid: MessageSid,
        direction: 'inbound',
        from: From,
        to: To,
        body: Body || '',
        mediaUrls: mediaUrls,
        messageType: mediaUrls.length > 0 ? 'media' : 'text',
        status: 'received',
        conversationId: conversationId,
        sentAt: new Date(),
      });

      await message.save();

      // 更新会话
      await this.updateConversation(conversationId, message);

      logger.info('Incoming message processed successfully', {
        messageId: message._id,
        messageSid: MessageSid,
        conversationId: conversationId,
      });

      // 可以在这里添加自动回复逻辑
      await this.processAutoReply(message);

      return { 
        success: true, 
        messageId: message._id,
        conversationId: conversationId,
      };

    } catch (error) {
      logger.error('Failed to handle incoming message webhook:', error);
      throw error;
    }
  }

  /**
   * 获取或创建会话ID
   */
  async getOrCreateConversationId(customerPhone, businessPhone) {
    try {
      // 查找现有会话
      let conversation = await Conversation.findOne({
        'participants.phoneNumber': { $all: [customerPhone, businessPhone] },
        status: 'active',
      });

      if (!conversation) {
        // 创建新会话
        const conversationId = uuidv4();
        conversation = new Conversation({
          conversationId: conversationId,
          participants: [
            { phoneNumber: businessPhone, role: 'business' },
            { phoneNumber: customerPhone, role: 'customer' },
          ],
          businessInfo: {
            source: 'inbound',
          },
        });
        await conversation.save();
        
        logger.info('New conversation created', {
          conversationId: conversationId,
          customerPhone: customerPhone,
          businessPhone: businessPhone,
        });
      }

      return conversation.conversationId;
    } catch (error) {
      logger.error('Failed to get or create conversation:', error);
      throw error;
    }
  }

  /**
   * 更新会话信息
   */
  async updateConversation(conversationId, message) {
    try {
      const conversation = await Conversation.findOne({ conversationId });
      if (conversation) {
        await conversation.updateLastMessage(message);
        logger.debug('Conversation updated', { conversationId });
      }
    } catch (error) {
      logger.error('Failed to update conversation:', error);
      // 不抛出错误，避免影响主流程
    }
  }

  /**
   * 处理自动回复逻辑
   */
  async processAutoReply(incomingMessage) {
    try {
      // 这里可以实现自动回复逻辑
      // 例如：关键词回复、营业时间回复等
      
      const body = incomingMessage.body.toLowerCase();
      
      // 示例：简单的关键词回复
      let autoReplyText = null;
      
      if (body.includes('hello') || body.includes('hi')) {
        autoReplyText = 'Hello! Thank you for contacting us. How can we help you today?';
      } else if (body.includes('hours') || body.includes('time')) {
        autoReplyText = 'Our business hours are Monday-Friday 9AM-6PM EST. We\'ll respond to your message during business hours.';
      } else if (body.includes('help')) {
        autoReplyText = 'We\'re here to help! Please describe your question or concern and we\'ll get back to you soon.';
      }

      if (autoReplyText) {
        // 这里可以调用消息发送服务发送自动回复
        logger.info('Auto-reply triggered', {
          incomingMessageId: incomingMessage._id,
          replyText: autoReplyText,
        });
        
        // 注意：实际实现时需要避免循环调用
        // 可以添加标记来识别自动回复消息
      }

    } catch (error) {
      logger.error('Failed to process auto-reply:', error);
      // 不抛出错误，避免影响主流程
    }
  }

  /**
   * 验证Twilio webhook签名
   * @param {string} signature - Twilio签名
   * @param {string} url - webhook URL
   * @param {Object} params - 请求参数
   */
  validateWebhookSignature(signature, url, params) {
    try {
      const twilio = require('twilio');
      const authToken = process.env.TWILIO_AUTH_TOKEN;
      
      return twilio.validateRequest(authToken, signature, url, params);
    } catch (error) {
      logger.error('Failed to validate webhook signature:', error);
      return false;
    }
  }
}

module.exports = new WebhookService();
