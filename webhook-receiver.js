// WhatsApp消息接收Webhook服务器
const express = require('express');
const twilio = require('twilio');
require('dotenv').config();

const app = express();
const PORT = process.env.WEBHOOK_PORT || 3002;

// 解析表单数据（Twilio发送的是form-encoded数据）
app.use(express.urlencoded({ extended: true }));
app.use(express.json());

// 存储接收到的消息（在实际应用中应该使用数据库）
const receivedMessages = [];

// Webhook端点 - 接收WhatsApp消息状态更新
app.post('/whatsapp-status', (req, res) => {
  console.log('\n📊 收到消息状态更新!');
  console.log('=' .repeat(50));

  const {
    MessageSid,
    MessageStatus,
    From,
    To,
    ErrorCode,
    ErrorMessage
  } = req.body;

  console.log('📋 状态更新详情:');
  console.log('   消息ID:', MessageSid);
  console.log('   状态:', MessageStatus);
  console.log('   发送方:', From);
  console.log('   接收方:', To);
  if (ErrorCode) {
    console.log('   错误代码:', ErrorCode);
    console.log('   错误信息:', ErrorMessage);
  }
  console.log('   更新时间:', new Date().toLocaleString());
  console.log('');

  // 返回200状态码确认接收
  res.status(200).send('OK');
});

// Webhook端点 - 接收WhatsApp消息
app.post('/whatsapp-webhook', (req, res) => {
  console.log('\n📱 收到WhatsApp消息!');
  console.log('=' .repeat(50));
  
  const {
    MessageSid,
    From,
    To,
    Body,
    NumMedia,
    MediaUrl0,
    MediaContentType0,
    ProfileName,
    WaId
  } = req.body;

  // 记录消息详情
  const messageData = {
    sid: MessageSid,
    from: From,
    to: To,
    body: Body,
    numMedia: NumMedia || 0,
    mediaUrl: MediaUrl0,
    mediaType: MediaContentType0,
    profileName: ProfileName,
    waId: WaId,
    timestamp: new Date().toISOString(),
    rawData: req.body
  };

  receivedMessages.push(messageData);

  console.log('📋 消息详情:');
  console.log('   消息ID:', MessageSid);
  console.log('   发送方:', From);
  console.log('   接收方:', To);
  console.log('   内容:', Body);
  console.log('   发送者姓名:', ProfileName || 'Unknown');
  console.log('   WhatsApp ID:', WaId);
  console.log('   媒体文件数:', NumMedia || 0);
  if (MediaUrl0) {
    console.log('   媒体URL:', MediaUrl0);
    console.log('   媒体类型:', MediaContentType0);
  }
  console.log('   接收时间:', new Date().toLocaleString());
  console.log('');

  // 分析消息内容并生成自动回复
  const autoReply = generateAutoReply(Body, ProfileName);
  
  if (autoReply) {
    console.log('🤖 准备发送自动回复:', autoReply);
    sendAutoReply(From, autoReply);
  }

  // 显示统计信息
  console.log('📊 消息统计:');
  console.log('   总接收消息数:', receivedMessages.length);
  console.log('   最近5条消息:');
  receivedMessages.slice(-5).forEach((msg, index) => {
    console.log(`   ${index + 1}. ${msg.body?.substring(0, 30)}... (${msg.timestamp})`);
  });
  console.log('');

  // 返回TwiML响应（可选）
  const twiml = new twilio.twiml.MessagingResponse();
  
  // 可以在这里添加即时回复
  // twiml.message('感谢您的消息！我们已收到。');

  res.writeHead(200, {'Content-Type': 'text/xml'});
  res.end(twiml.toString());
});

// 生成自动回复
function generateAutoReply(messageBody, senderName) {
  if (!messageBody) return null;

  const body = messageBody.toLowerCase().trim();
  const name = senderName || '朋友';

  // 根据消息内容生成不同的回复
  if (body.includes('hello') || body.includes('hi') || body.includes('你好')) {
    return `👋 你好 ${name}！感谢您测试我们的WhatsApp API系统！`;
  }
  
  if (body.includes('test') || body.includes('测试')) {
    return `✅ 测试成功！您的消息已被我们的系统接收和处理。\n\n📊 系统状态：正常运行\n⏰ 处理时间：${new Date().toLocaleString()}`;
  }
  
  if (body.includes('help') || body.includes('帮助')) {
    return `🆘 帮助信息：\n\n📱 这是Twilio WhatsApp沙盒测试环境\n🔄 您可以发送任意消息测试双向通信\n✅ 系统会自动记录和回复您的消息\n\n💡 尝试发送：hello, test, status, info`;
  }
  
  if (body.includes('status') || body.includes('状态')) {
    return `📊 系统状态报告：\n\n✅ WhatsApp API：正常\n✅ 消息接收：正常\n✅ 自动回复：正常\n📈 已处理消息：${receivedMessages.length}条\n⏰ 最后更新：${new Date().toLocaleString()}`;
  }
  
  if (body.includes('info') || body.includes('信息')) {
    return `ℹ️ 系统信息：\n\n🏢 服务：Twilio WhatsApp Business API\n🔧 环境：沙盒测试\n📱 号码：+1 ************\n🌐 Webhook：已配置\n⚡ 响应时间：实时`;
  }

  // 默认回复
  return `📨 消息已收到！\n\n您说："${messageBody}"\n\n🤖 这是自动回复，证明双向通信正常工作。\n⏰ 时间：${new Date().toLocaleString()}`;
}

// 发送自动回复
async function sendAutoReply(toNumber, message) {
  try {
    const accountSid = process.env.TWILIO_ACCOUNT_SID;
    const authToken = process.env.TWILIO_AUTH_TOKEN;
    const client = twilio(accountSid, authToken);

    const response = await client.messages.create({
      from: 'whatsapp:+***********',
      to: toNumber,
      body: message
    });

    console.log('✅ 自动回复发送成功:', response.sid);
    
  } catch (error) {
    console.error('❌ 自动回复发送失败:', error.message);
  }
}

// 健康检查端点
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    service: 'WhatsApp Webhook Receiver',
    uptime: process.uptime(),
    receivedMessages: receivedMessages.length,
    timestamp: new Date().toISOString()
  });
});

// 查看接收到的消息
app.get('/messages', (req, res) => {
  res.json({
    total: receivedMessages.length,
    messages: receivedMessages.slice(-10), // 最近10条消息
    timestamp: new Date().toISOString()
  });
});

// 清空消息记录
app.post('/clear-messages', (req, res) => {
  const count = receivedMessages.length;
  receivedMessages.length = 0;
  
  console.log(`🗑️ 已清空 ${count} 条消息记录`);
  
  res.json({
    success: true,
    cleared: count,
    timestamp: new Date().toISOString()
  });
});

// 启动服务器
app.listen(PORT, () => {
  console.log('🚀 WhatsApp Webhook接收服务器启动成功!');
  console.log('=' .repeat(60));
  console.log(`📡 监听端口: ${PORT}`);
  console.log(`🔗 Webhook URL: http://localhost:${PORT}/whatsapp-webhook`);
  console.log(`🏥 健康检查: http://localhost:${PORT}/health`);
  console.log(`📨 查看消息: http://localhost:${PORT}/messages`);
  console.log('');
  console.log('💡 使用ngrok暴露到公网:');
  console.log(`   ngrok http ${PORT}`);
  console.log('   然后在Twilio控制台配置Webhook URL');
  console.log('');
  console.log('📱 测试步骤:');
  console.log('   1. 启动ngrok暴露服务');
  console.log('   2. 在Twilio控制台配置Webhook');
  console.log('   3. 从WhatsApp发送消息到 +1 ************');
  console.log('   4. 观察此控制台的消息接收日志');
  console.log('   5. 查看自动回复功能');
  console.log('');
  console.log('⏳ 等待接收WhatsApp消息...');
});

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n📊 服务器关闭统计:');
  console.log(`   总接收消息: ${receivedMessages.length} 条`);
  console.log(`   运行时间: ${Math.floor(process.uptime())} 秒`);
  console.log('👋 Webhook服务器已关闭');
  process.exit(0);
});

module.exports = app;
