const twilioService = require('../config/twilio');
const { Message, Conversation } = require('../models');
const logger = require('../config/logger');
const { v4: uuidv4 } = require('uuid');

class MessageService {
  
  /**
   * 发送Business-Initiated消息
   * @param {Object} messageData - 消息数据
   * @param {string} messageData.to - 接收方号码
   * @param {string} messageData.body - 消息内容
   * @param {Array} messageData.mediaUrls - 媒体文件URL数组
   * @param {string} messageData.campaignId - 活动ID
   * @param {Object} messageData.metadata - 元数据
   */
  async sendMessage(messageData) {
    try {
      const { to, body, mediaUrls = [], campaignId, metadata = {} } = messageData;
      
      // 验证必需参数
      if (!to) {
        throw new Error('Recipient phone number is required');
      }
      
      if (!body && (!mediaUrls || mediaUrls.length === 0)) {
        throw new Error('Message body or media is required');
      }

      const twilioClient = twilioService.getClient();
      const fromNumber = twilioService.getPhoneNumber();

      // 生成会话ID
      const conversationId = await this.getOrCreateConversationId(fromNumber, to, campaignId);

      // 创建消息记录
      const message = new Message({
        direction: 'outbound',
        from: fromNumber,
        to: to,
        body: body || '',
        mediaUrls: mediaUrls,
        messageType: mediaUrls.length > 0 ? 'media' : 'text',
        status: 'queued',
        conversationId: conversationId,
        campaignId: campaignId,
        metadata: metadata,
      });

      await message.save();

      // 准备Twilio消息参数
      const twilioParams = {
        from: fromNumber,
        to: to,
        body: body,
      };

      // 添加媒体文件
      if (mediaUrls && mediaUrls.length > 0) {
        twilioParams.mediaUrl = mediaUrls;
      }

      // 设置状态回调URL
      twilioParams.statusCallback = `${process.env.WEBHOOK_BASE_URL}/webhook/message-status`;

      // 发送消息
      logger.info('Sending message via Twilio', { 
        messageId: message._id, 
        to: to, 
        hasMedia: mediaUrls.length > 0 
      });

      const twilioMessage = await twilioClient.messages.create(twilioParams);

      // 更新消息记录
      message.twilioSid = twilioMessage.sid;
      message.status = twilioMessage.status;
      message.price = twilioMessage.price;
      message.priceUnit = twilioMessage.priceUnit;
      
      await message.save();

      // 更新会话
      await this.updateConversation(conversationId, message);

      logger.info('Message sent successfully', { 
        messageId: message._id, 
        twilioSid: twilioMessage.sid,
        status: twilioMessage.status 
      });

      return {
        success: true,
        messageId: message._id,
        twilioSid: twilioMessage.sid,
        status: twilioMessage.status,
        conversationId: conversationId,
      };

    } catch (error) {
      logger.error('Failed to send message:', error);
      
      // 如果消息记录已创建，更新状态为失败
      if (error.messageId) {
        try {
          await Message.findByIdAndUpdate(error.messageId, {
            status: 'failed',
            errorMessage: error.message,
          });
        } catch (updateError) {
          logger.error('Failed to update message status:', updateError);
        }
      }

      throw error;
    }
  }

  /**
   * 批量发送消息
   * @param {Array} messages - 消息数组
   */
  async sendBulkMessages(messages) {
    const results = [];
    const errors = [];

    for (const messageData of messages) {
      try {
        const result = await this.sendMessage(messageData);
        results.push(result);
      } catch (error) {
        errors.push({
          messageData,
          error: error.message,
        });
      }
    }

    return {
      success: results.length,
      failed: errors.length,
      results,
      errors,
    };
  }

  /**
   * 获取或创建会话ID
   */
  async getOrCreateConversationId(from, to, campaignId) {
    try {
      // 查找现有会话
      let conversation = await Conversation.findOne({
        'participants.phoneNumber': { $all: [from, to] },
        status: 'active',
      });

      if (!conversation) {
        // 创建新会话
        const conversationId = uuidv4();
        conversation = new Conversation({
          conversationId: conversationId,
          participants: [
            { phoneNumber: from, role: 'business' },
            { phoneNumber: to, role: 'customer' },
          ],
          businessInfo: {
            campaignId: campaignId,
          },
        });
        await conversation.save();
      }

      return conversation.conversationId;
    } catch (error) {
      logger.error('Failed to get or create conversation:', error);
      throw error;
    }
  }

  /**
   * 更新会话信息
   */
  async updateConversation(conversationId, message) {
    try {
      const conversation = await Conversation.findOne({ conversationId });
      if (conversation) {
        await conversation.updateLastMessage(message);
      }
    } catch (error) {
      logger.error('Failed to update conversation:', error);
      // 不抛出错误，避免影响主流程
    }
  }

  /**
   * 获取消息历史
   */
  async getMessageHistory(phoneNumber, limit = 50, offset = 0) {
    try {
      const messages = await Message.find({
        $or: [
          { from: phoneNumber },
          { to: phoneNumber }
        ]
      })
      .sort({ createdAt: -1 })
      .limit(limit)
      .skip(offset)
      .lean();

      return messages;
    } catch (error) {
      logger.error('Failed to get message history:', error);
      throw error;
    }
  }

  /**
   * 获取会话消息
   */
  async getConversationMessages(conversationId, limit = 50, offset = 0) {
    try {
      const messages = await Message.find({ conversationId })
        .sort({ createdAt: 1 })
        .limit(limit)
        .skip(offset)
        .lean();

      return messages;
    } catch (error) {
      logger.error('Failed to get conversation messages:', error);
      throw error;
    }
  }
}

module.exports = new MessageService();
