// 设置WhatsApp Webhook的快速指南脚本
const { spawn } = require('child_process');
const axios = require('axios');

async function setupWebhook() {
  console.log('🔧 WhatsApp Webhook设置助手');
  console.log('=' .repeat(50));
  console.log('');

  console.log('📋 设置步骤概览:');
  console.log('   1. 启动Webhook接收服务器');
  console.log('   2. 使用ngrok暴露到公网');
  console.log('   3. 在Twilio控制台配置Webhook URL');
  console.log('   4. 测试消息接收');
  console.log('');

  // 步骤1：检查依赖
  console.log('1️⃣ 检查依赖...');
  
  try {
    // 检查ngrok是否安装
    const ngrokCheck = spawn('ngrok', ['version'], { stdio: 'pipe' });
    
    ngrokCheck.on('close', (code) => {
      if (code === 0) {
        console.log('   ✅ ngrok已安装');
      } else {
        console.log('   ⚠️  ngrok未安装，请先安装:');
        console.log('      npm install -g ngrok');
        console.log('      或访问: https://ngrok.com/download');
      }
    });

    ngrokCheck.on('error', () => {
      console.log('   ⚠️  ngrok未安装，请先安装:');
      console.log('      npm install -g ngrok');
      console.log('      或访问: https://ngrok.com/download');
    });

  } catch (error) {
    console.log('   ⚠️  无法检查ngrok状态');
  }

  // 步骤2：启动说明
  console.log('\n2️⃣ 启动Webhook服务器:');
  console.log('   在新的终端窗口运行:');
  console.log('   node webhook-receiver.js');
  console.log('');

  // 步骤3：ngrok设置
  console.log('3️⃣ 暴露服务到公网:');
  console.log('   在另一个新终端窗口运行:');
  console.log('   ngrok http 3002');
  console.log('');
  console.log('   ngrok会显示类似这样的URL:');
  console.log('   https://abc123.ngrok.io');
  console.log('');

  // 步骤4：Twilio配置
  console.log('4️⃣ 配置Twilio Webhook:');
  console.log('   1. 访问: https://console.twilio.com/us1/develop/sms/try-it-out/whatsapp-learn');
  console.log('   2. 找到 "Sandbox Configuration" 部分');
  console.log('   3. 在 "When a message comes in" 字段输入:');
  console.log('      https://your-ngrok-url.ngrok.io/whatsapp-webhook');
  console.log('   4. 点击 "Save Configuration"');
  console.log('');

  // 步骤5：测试说明
  console.log('5️⃣ 测试双向通信:');
  console.log('   1. 从您的WhatsApp发送消息到: +1 415 523 8886');
  console.log('   2. 观察webhook-receiver.js的控制台输出');
  console.log('   3. 查看自动回复消息');
  console.log('');

  // 测试消息建议
  console.log('📱 建议测试消息:');
  console.log('   • "hello" - 测试问候回复');
  console.log('   • "test" - 测试系统状态');
  console.log('   • "help" - 获取帮助信息');
  console.log('   • "status" - 查看系统状态');
  console.log('   • "info" - 获取系统信息');
  console.log('   • 任意其他文本 - 测试通用回复');
  console.log('');

  // 故障排除
  console.log('🔧 故障排除:');
  console.log('   • 如果收不到消息，检查ngrok URL是否正确配置');
  console.log('   • 确保webhook-receiver.js正在运行');
  console.log('   • 检查Twilio控制台的错误日志');
  console.log('   • 确认您的号码仍在沙盒中（24小时有效期）');
  console.log('');

  // 监控端点
  console.log('📊 监控端点:');
  console.log('   • 健康检查: http://localhost:3002/health');
  console.log('   • 查看消息: http://localhost:3002/messages');
  console.log('   • 清空记录: POST http://localhost:3002/clear-messages');
  console.log('');

  console.log('🎯 准备就绪！按照上述步骤设置即可测试双向WhatsApp通信。');
}

// 检查服务器状态
async function checkWebhookServer() {
  try {
    const response = await axios.get('http://localhost:3002/health');
    console.log('✅ Webhook服务器运行正常');
    console.log('   状态:', response.data.status);
    console.log('   运行时间:', Math.floor(response.data.uptime), '秒');
    console.log('   接收消息数:', response.data.receivedMessages);
    return true;
  } catch (error) {
    console.log('❌ Webhook服务器未运行');
    console.log('   请先运行: node webhook-receiver.js');
    return false;
  }
}

// 主函数
async function main() {
  const command = process.argv[2];

  if (command === 'check') {
    console.log('🔍 检查Webhook服务器状态...');
    await checkWebhookServer();
  } else if (command === 'messages') {
    try {
      const response = await axios.get('http://localhost:3002/messages');
      console.log('📨 最近接收的消息:');
      console.log('   总数:', response.data.total);
      response.data.messages.forEach((msg, index) => {
        console.log(`   ${index + 1}. ${msg.body} (${msg.timestamp})`);
      });
    } catch (error) {
      console.log('❌ 无法获取消息，请确保服务器正在运行');
    }
  } else {
    await setupWebhook();
  }
}

if (require.main === module) {
  main();
}

module.exports = { setupWebhook, checkWebhookServer };
