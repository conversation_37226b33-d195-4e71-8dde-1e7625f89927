# Facebook API 配置和使用指南

## 🔧 配置步骤

### 1. 修改配置信息
在 `test-send-facebook.js` 文件中修改以下配置：

```javascript
const VERIFY_TOKEN = "jianglai"; // 自定义的验证字符串
const PAGE_ID = "ccjr.tti";      // 你的粉丝专页 ID
const APP_ID = "你的AppID";       // Facebook App ID
const APP_SECRET = "你的AppSecret"; // Facebook App Secret
```

### 2. 获取 Facebook App ID 和 App Secret
1. 访问 [Facebook Developers](https://developers.facebook.com/)
2. 创建或选择一个应用
3. 在应用设置中找到 App ID 和 App Secret

### 3. 获取页面 ID
1. 访问你的 Facebook 页面
2. 在页面设置中找到页面 ID
3. 或者使用 Graph API Explorer 查询

## 🚀 启动服务器

```bash
npm install express body-parser node-fetch
node test-send-facebook.js
```

服务器将在 http://localhost:3002 启动

## 📡 Webhook 配置

### 在 Facebook 开发者控制台配置 Webhook：
1. 进入你的 Facebook 应用
2. 添加 Webhooks 产品
3. 设置回调 URL: `http://你的域名:3002/webhook`
4. 设置验证令牌: `jianglai` (或你自定义的值)
5. 订阅页面事件

## 🔑 获取访问令牌流程

### 方法1: 使用 API 端点
1. **获取短期用户令牌**
   - 访问 [Graph API Explorer](https://developers.facebook.com/tools/explorer/)
   - 选择你的应用
   - 获取用户访问令牌

2. **转换为长期令牌**
   ```bash
   curl -X POST http://localhost:3002/get-long-term-token \
     -H "Content-Type: application/json" \
     -d '{"short_token":"你的短期token"}'
   ```

3. **获取页面访问令牌**
   ```bash
   curl -X POST http://localhost:3002/get-page-token \
     -H "Content-Type: application/json" \
     -d '{"user_access_token":"你的长期用户token"}'
   ```

### 方法2: 直接在 Graph API Explorer 获取
1. 在 Graph API Explorer 中选择你的页面
2. 生成页面访问令牌
3. 手动更新代码中的 `PAGE_ACCESS_TOKEN`

## 📝 发布文章

### 使用 API 发布
```bash
curl -X POST http://localhost:3002/post \
  -H "Content-Type: application/json" \
  -d '{
    "message": "你的文章内容",
    "link": "https://example.com"
  }'
```

### 使用测试脚本
```bash
node facebook-api-test.js
```

## 🔍 API 端点说明

| 方法 | 端点 | 说明 |
|------|------|------|
| GET | `/webhook` | Webhook 验证 |
| POST | `/webhook` | 接收 Facebook 事件 |
| POST | `/get-long-term-token` | 获取长期用户令牌 |
| POST | `/get-page-token` | 获取页面访问令牌 |
| POST | `/post` | 发布文章 |
| GET | `/status` | 查看配置状态 |

## ⚠️ 注意事项

1. **权限要求**: 确保你的应用有 `pages_manage_posts` 权限
2. **令牌安全**: 不要在代码中硬编码敏感信息
3. **HTTPS**: 生产环境需要使用 HTTPS
4. **速率限制**: 注意 Facebook API 的调用限制

## 🐛 常见问题

### Webhook 验证失败
- 检查 VERIFY_TOKEN 是否匹配
- 确认使用 GET 方法进行验证

### 发帖失败
- 检查页面访问令牌是否有效
- 确认应用权限是否足够
- 查看错误信息进行调试

### 无法获取令牌
- 检查 App ID 和 App Secret 是否正确
- 确认用户已授权必要权限
