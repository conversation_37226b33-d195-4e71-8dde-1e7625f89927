// 意图识别服务 - 使用LLM进行智能意图判断
require('dotenv').config();
const OpenAI = require('openai');

class IntentRecognitionService {
  constructor() {
    // 初始化 ARK API 客户端
    this.openaiClient = new OpenAI({
      apiKey: process.env.ARK_API_KEY,
      baseURL: 'https://ark.cn-beijing.volces.com/api/v3',
    });

    // 意图识别提示词
    this.intentPrompt = `你是一个专业的意图识别系统，专门为畅游网络随身路由器业务服务。

## 业务范围：
畅游网络是一家专业的随身路由器公司，主要业务包括：
- 产品：畅游 Mini (¥299)、畅游 Pro (¥599)、畅游 Max (¥999)
- 流量套餐：按量计费、月度套餐、年度套餐
- 服务：售前咨询、售后支持、技术服务

## 意图分类：
请将用户消息分类为以下意图之一：

1. **business_inquiry** - 明确的业务咨询
   - 询问产品信息、价格、规格
   - 询问流量套餐、资费
   - 询问服务政策、售后支持
   - 产品比较、推荐需求

2. **business_related** - 可能相关的业务咨询
   - 模糊的产品需求
   - 间接的业务相关问题
   - 需要进一步澄清的咨询

3. **greeting** - 问候语
   - 你好、早上好等问候
   - 初次接触的礼貌用语

4. **chitchat** - 闲聊
   - 天气、时间等日常话题
   - 与业务无关的个人话题

5. **general** - 其他一般性询问
   - 不明确归类的问题
   - 需要通用回复的内容

## 输出格式：
请以JSON格式回复，包含：
{
  "intent": "意图类别",
  "confidence": 0.0-1.0的置信度,
  "reasoning": "判断理由",
  "needsKnowledgeSearch": true/false,
  "businessCategories": ["相关业务类别"],
  "searchKeywords": ["搜索关键词"]
}

请分析用户消息并返回意图识别结果。`;
  }

  // 主要意图识别方法 - 使用LLM进行智能判断
  async recognizeIntent(message) {
    try {
      console.log(`🎯 使用LLM分析用户意图: "${message}"`);

      // 构建完整的提示词
      const fullPrompt = `${this.intentPrompt}

用户消息: "${message}"

请分析这条消息的意图并返回JSON格式的结果。`;

      // 调用 ARK API 进行意图识别
      const response = await this.openaiClient.chat.completions.create({
        model: process.env.VOLCENGINE_MODEL_ENDPOINT || 'doubao-seed-1-6-flash-250615',
        messages: [
          {
            role: 'user',
            content: fullPrompt
          }
        ],
        temperature: 0.3, // 较低的温度确保结果稳定
        max_tokens: 500
      });

      const aiResponse = response.choices[0].message.content;
      console.log(`🤖 LLM原始回复: ${aiResponse}`);

      // 解析JSON响应
      let intentResult;
      try {
        // 尝试提取JSON部分
        const jsonMatch = aiResponse.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          intentResult = JSON.parse(jsonMatch[0]);
        } else {
          throw new Error('未找到JSON格式的响应');
        }
      } catch (parseError) {
        console.log('⚠️ JSON解析失败，使用后备解析方法');
        intentResult = this.parseIntentFromText(aiResponse, message);
      }

      // 验证和标准化结果
      const standardizedResult = this.standardizeIntentResult(intentResult, message);

      console.log(`✅ 意图识别结果: ${standardizedResult.intent} (置信度: ${standardizedResult.confidence})`);

      return standardizedResult;

    } catch (error) {
      console.error('❌ LLM意图识别失败:', error.message);

      // 降级到简单的关键词匹配
      console.log('🔄 降级到关键词匹配方法');
      return this.fallbackIntentRecognition(message);
    }
  }

  // 从文本中解析意图（当JSON解析失败时使用）
  parseIntentFromText(aiResponse, message) {
    const lowerResponse = aiResponse.toLowerCase();

    // 尝试从文本中提取意图
    let intent = 'general';
    let confidence = 0.5;
    let reasoning = '基于文本解析的结果';

    if (lowerResponse.includes('business_inquiry')) {
      intent = 'business_inquiry';
      confidence = 0.8;
    } else if (lowerResponse.includes('business_related')) {
      intent = 'business_related';
      confidence = 0.6;
    } else if (lowerResponse.includes('greeting')) {
      intent = 'greeting';
      confidence = 0.9;
    } else if (lowerResponse.includes('chitchat')) {
      intent = 'chitchat';
      confidence = 0.7;
    }

    return {
      intent: intent,
      confidence: confidence,
      reasoning: reasoning,
      needsKnowledgeSearch: intent.includes('business'),
      businessCategories: intent.includes('business') ? ['products'] : [],
      searchKeywords: [message]
    };
  }

  // 标准化意图识别结果
  standardizeIntentResult(intentResult, message) {
    // 确保所有必需字段存在
    const standardized = {
      message: message,
      intent: intentResult.intent || 'general',
      confidence: Math.min(Math.max(intentResult.confidence || 0.5, 0), 1), // 限制在0-1之间
      reasoning: intentResult.reasoning || '意图识别结果',
      needsKnowledgeSearch: intentResult.needsKnowledgeSearch !== false &&
                           (intentResult.intent === 'business_inquiry' ||
                            intentResult.intent === 'business_related'),
      businessCategories: intentResult.businessCategories || [],
      searchKeywords: intentResult.searchKeywords || [message],
      analysis: {
        isBusiness: intentResult.intent === 'business_inquiry' || intentResult.intent === 'business_related',
        isGreeting: intentResult.intent === 'greeting',
        isChitChat: intentResult.intent === 'chitchat',
        llmUsed: true,
        reasoning: intentResult.reasoning
      }
    };

    return standardized;
  }

  // 降级的意图识别方法（当LLM失败时使用）
  fallbackIntentRecognition(message) {
    const lowerMessage = message.toLowerCase();

    // 简单的关键词匹配
    const businessKeywords = [
      '畅游', 'mini', 'pro', 'max', '路由器', '价格', '多少钱',
      '流量', '套餐', '包月', '包年', '服务', '售后', '购买'
    ];

    const greetingKeywords = [
      '你好', 'hello', 'hi', '您好', '早上好', '下午好', '晚上好'
    ];

    const chitchatKeywords = [
      '天气', '今天', '明天', '时间', '日期', '心情'
    ];

    let intent = 'general';
    let confidence = 0.5;
    let needsKnowledgeSearch = false;

    // 检查业务关键词
    const businessMatches = businessKeywords.filter(keyword => lowerMessage.includes(keyword));
    if (businessMatches.length > 0) {
      intent = businessMatches.length > 2 ? 'business_inquiry' : 'business_related';
      confidence = Math.min(0.3 + (businessMatches.length * 0.2), 0.9);
      needsKnowledgeSearch = true;
    }

    // 检查问候语
    const greetingMatches = greetingKeywords.filter(keyword => lowerMessage.includes(keyword));
    if (greetingMatches.length > 0) {
      intent = 'greeting';
      confidence = 0.8;
      needsKnowledgeSearch = false;
    }

    // 检查闲聊
    const chitchatMatches = chitchatKeywords.filter(keyword => lowerMessage.includes(keyword));
    if (chitchatMatches.length > 0) {
      intent = 'chitchat';
      confidence = 0.7;
      needsKnowledgeSearch = false;
    }

    return {
      message: message,
      intent: intent,
      confidence: confidence,
      reasoning: '使用关键词匹配的降级方法',
      needsKnowledgeSearch: needsKnowledgeSearch,
      businessCategories: needsKnowledgeSearch ? ['products'] : [],
      searchKeywords: needsKnowledgeSearch ? businessMatches : [message],
      analysis: {
        isBusiness: needsKnowledgeSearch,
        isGreeting: intent === 'greeting',
        isChitChat: intent === 'chitchat',
        llmUsed: false,
        fallbackMethod: true
      }
    };
  }

  // 生成搜索查询（用于知识图谱搜索）
  generateSearchQuery(message, intentResult) {
    // 检查是否需要知识搜索
    if (!intentResult || !intentResult.needsKnowledgeSearch) {
      console.log(`🚫 不需要知识搜索: 意图为 ${intentResult?.intent || 'unknown'}`);
      return null;
    }

    // 优先使用LLM提供的搜索关键词
    if (intentResult.searchKeywords && intentResult.searchKeywords.length > 0) {
      const query = intentResult.searchKeywords.join(' ');
      console.log(`🔍 使用LLM关键词生成搜索查询: "${query}"`);
      return query;
    }

    // 如果没有特定关键词，使用原始消息
    if (message && message.trim()) {
      console.log(`🔍 使用原始消息生成搜索查询: "${message}"`);
      return message;
    }

    // 如果消息也为空，返回null
    console.log(`⚠️ 无法生成搜索查询: 消息为空`);
    return null;
  }

  // 获取意图分析报告
  getIntentAnalysisReport(intentResult) {
    return {
      intent: intentResult.intent,
      confidence: intentResult.confidence,
      reasoning: intentResult.reasoning || '意图识别结果',
      needsKnowledge: intentResult.needsKnowledgeSearch,
      businessCategories: intentResult.businessCategories || [],
      searchKeywords: intentResult.searchKeywords || [],
      llmUsed: intentResult.analysis?.llmUsed || false,
      recommendation: this.getRecommendation(intentResult)
    };
  }

  // 获取处理建议
  getRecommendation(intentResult) {
    switch (intentResult.intent) {
      case 'business_inquiry':
        return 'search_knowledge_and_respond';
      case 'business_related':
        return 'search_knowledge_and_respond';
      case 'greeting':
        return 'friendly_greeting';
      case 'chitchat':
        return 'polite_redirect';
      default:
        return 'general_response';
    }
  }

  // 测试LLM意图识别功能
  async testIntentRecognition() {
    const testMessages = [
      '你好',
      '畅游Pro的价格是多少？',
      '有什么流量套餐推荐吗？',
      '售后服务怎么样？',
      '今天天气真好',
      '我需要一个适合商务出差的路由器'
    ];

    console.log('🧪 测试LLM意图识别功能...');

    for (const message of testMessages) {
      console.log(`\n📝 测试消息: "${message}"`);
      try {
        const result = await this.recognizeIntent(message);
        console.log(`   意图: ${result.intent}`);
        console.log(`   置信度: ${result.confidence}`);
        console.log(`   需要知识搜索: ${result.needsKnowledgeSearch}`);
        console.log(`   推理: ${result.reasoning}`);
      } catch (error) {
        console.log(`   ❌ 测试失败: ${error.message}`);
      }
    }
  }
}

module.exports = IntentRecognitionService;
