# WhatsApp统一智能机器人

这是一个整合了原有 `webhook-receiver.js` 和 `whatsapp-coze-bot.js` 功能的统一WhatsApp智能机器人服务。

## 🚀 主要功能

### 核心功能
- **消息接收**: 接收WhatsApp用户发送的文本和图片消息
- **智能回复**: 集成Coze API v3进行智能对话
- **图片处理**: 自动下载、上传图片到火山引擎TOS，生成公开URL供Coze API使用
- **状态监控**: 接收和记录消息发送状态更新
- **会话管理**: 维护用户会话状态和对话历史

### API端点
- `POST /whatsapp-webhook` - 接收WhatsApp消息
- `POST /whatsapp-status` - 接收消息状态更新
- `GET /health` - 健康检查
- `GET /messages` - 查看最近接收的消息
- `GET /sessions` - 查看活跃用户会话
- `POST /clear-messages` - 清空消息记录
- `POST /clear-session` - 清除特定用户会话

## 📋 环境要求

### 必需的环境变量
```env
# Twilio配置
TWILIO_ACCOUNT_SID=your_account_sid
TWILIO_AUTH_TOKEN=your_auth_token

# Coze API配置
COZE_API_TOKEN=your_coze_api_token
COZE_BOT_ID=your_bot_id

# 服务端口（可选，默认3002）
WEBHOOK_PORT=3002
```

### 依赖服务
- **Twilio WhatsApp Business API**: 消息收发
- **Coze API v3**: 智能对话生成
- **火山引擎TOS**: 图片存储和处理
- **智能回复系统**: 本地回复备用方案

## 🛠️ 安装和启动

### 1. 安装依赖
```bash
npm install
```

### 2. 配置环境变量
创建 `.env` 文件并配置必要的环境变量。

### 3. 启动服务
```bash
# 使用启动脚本（推荐）
node start-unified-bot.js

# 或直接启动
node whatsapp-unified-bot.js
```

### 4. 配置Webhook
使用ngrok暴露本地服务到公网：
```bash
ngrok http 3002
```

然后在Twilio控制台配置Webhook URL：
- **消息Webhook**: `https://your-ngrok-url.ngrok.io/whatsapp-webhook`
- **状态Webhook**: `https://your-ngrok-url.ngrok.io/whatsapp-status`

## 🖼️ 图片消息处理流程

1. **接收图片**: 用户发送图片到WhatsApp
2. **下载图片**: 从Twilio媒体URL下载图片文件
3. **上传TOS**: 将图片上传到火山引擎对象存储
4. **生成公开URL**: 创建可公开访问的图片URL
5. **调用Coze API**: 使用公开URL调用Coze API进行图片理解
6. **智能回复**: 基于图片内容生成智能回复

## 📊 监控和管理

### 健康检查
访问 `http://localhost:3002/health` 查看服务状态：
```json
{
  "status": "healthy",
  "service": "WhatsApp Unified Smart Bot",
  "uptime": 3600,
  "activeSessions": 5,
  "receivedMessages": 42,
  "ai": {"success": true},
  "twilio": {"success": true}
}
```

### 消息记录
访问 `http://localhost:3002/messages` 查看最近的消息。

### 会话管理
访问 `http://localhost:3002/sessions` 查看活跃的用户会话。

## 🔧 与原文件的区别

### 整合的功能
- **webhook-receiver.js** 的消息接收和状态处理
- **whatsapp-coze-bot.js** 的AI回复和图片处理
- 统一的错误处理和日志记录
- 完整的会话管理和消息存储

### 改进之处
- 代码去重，减少维护成本
- 统一的配置管理
- 更完善的错误处理
- 更详细的日志输出
- 更好的服务监控

## 🚨 故障排除

### 常见问题
1. **Coze API调用失败**: 检查API token和bot ID配置
2. **图片处理失败**: 检查火山引擎TOS配置
3. **消息发送失败**: 检查Twilio账户状态和余额
4. **Webhook接收失败**: 检查ngrok配置和Twilio Webhook设置

### 日志分析
服务会输出详细的日志信息，包括：
- 消息接收详情
- AI处理过程
- 图片处理状态
- 错误信息和堆栈

## 📝 使用建议

1. **生产环境**: 建议使用PM2或类似工具管理进程
2. **监控**: 定期检查健康检查端点
3. **日志**: 配置日志轮转避免日志文件过大
4. **安全**: 在生产环境中限制API端点的访问权限

## 🔄 迁移指南

如果您之前使用的是分离的文件：

1. 停止旧的服务
2. 更新Webhook配置指向新的统一服务
3. 启动新的统一机器人服务
4. 测试所有功能正常工作
5. 可选：删除旧的文件（建议先备份）
