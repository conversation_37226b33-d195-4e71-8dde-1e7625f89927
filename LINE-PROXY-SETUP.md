# LINE消息API代理配置指南

## 问题描述

在中国网络环境下，直接访问LINE API可能会遇到网络连接问题，导致"fetch failed"错误。本指南提供了为LINE MessagingApiClient配置本地代理的解决方案。

## 解决方案

### 1. 安装必要的依赖

```bash
npm install https-proxy-agent undici
```

### 2. 环境变量配置

在`.env`文件中添加以下配置：

```env
# Proxy Configuration (for China network environment)
# 设置为true以启用代理，在中国网络环境下建议启用
USE_PROXY=true
# 本地代理地址，默认为常用的本地代理端口
HTTPS_PROXY=http://127.0.0.1:7890
HTTP_PROXY=http://127.0.0.1:7890
```

### 3. 代码实现

在应用启动时配置全局代理：

```javascript
const { HttpsProxyAgent } = require('https-proxy-agent');
const axios = require('axios');

// 配置全局代理（用于LINE SDK和其他HTTPS请求）
const proxyUrl = process.env.HTTPS_PROXY || process.env.HTTP_PROXY || 'http://127.0.0.1:7890';
if (proxyUrl && (process.env.USE_PROXY === 'true' || process.env.NODE_ENV === 'development')) {
  console.log('🌐 配置全局HTTPS代理:', proxyUrl);
  
  // 创建代理agent
  const httpsAgent = new HttpsProxyAgent(proxyUrl);
  
  // 配置axios全局默认设置
  axios.defaults.httpsAgent = httpsAgent;
  axios.defaults.proxy = false; // 禁用axios内置代理，使用httpsAgent
  
  // 配置Node.js全局fetch代理（LINE SDK v10使用fetch）
  const { setGlobalDispatcher, ProxyAgent } = require('undici');
  try {
    const proxyAgent = new ProxyAgent(proxyUrl);
    setGlobalDispatcher(proxyAgent);
    console.log('✅ 全局fetch代理配置完成（undici）');
  } catch (undiciError) {
    console.log('⚠️ undici代理配置失败，尝试使用global-agent:', undiciError.message);
    
    // 回退到global-agent（适用于较老的Node.js版本）
    try {
      const globalAgent = require('global-agent');
      globalAgent.bootstrap();
      console.log('✅ 全局代理配置完成（global-agent）');
    } catch (globalAgentError) {
      console.log('⚠️ global-agent配置失败:', globalAgentError.message);
      console.log('💡 建议: 安装undici或global-agent来支持fetch代理');
    }
  }
  
  console.log('✅ 代理配置完成，LINE SDK和axios将使用此代理');
}

// 初始化LINE客户端（正常方式，代理已全局配置）
const lineClient = new line.messagingApi.MessagingApiClient({
  channelAccessToken: process.env.LINE_CHANNEL_ACCESS_TOKEN
});
```

## 技术原理

### LINE SDK v10的变化

LINE Bot SDK v10使用了Node.js内置的`fetch` API而不是axios，这就是为什么传统的axios代理配置对LINE SDK不起作用的原因。

### 双重代理配置

我们的解决方案配置了两种代理：

1. **axios代理**：使用`https-proxy-agent`为axios请求配置代理
2. **fetch代理**：使用`undici`的`ProxyAgent`为Node.js fetch API配置全局代理

### 代理服务器要求

- 支持HTTP CONNECT方法（用于HTTPS隧道）
- 本地代理服务器（如V2Ray、Clash等）运行在127.0.0.1:7890

## 测试验证

运行测试脚本验证配置：

```bash
node test-line-proxy.js
```

成功的输出应该显示：

```
🎉 所有测试通过！LINE代理配置正常工作
```

## 故障排除

### 1. 代理服务器未运行

**错误**: `fetch failed` 或 `ECONNREFUSED`

**解决**: 确保本地代理服务器正在运行并监听正确的端口

### 2. 代理配置错误

**错误**: 连接超时

**解决**: 检查代理URL格式是否正确，确保包含协议前缀（http://）

### 3. 环境变量未设置

**错误**: 代理未启用

**解决**: 确保`.env`文件中设置了`USE_PROXY=true`

## 生产环境注意事项

1. 在生产环境中，建议使用专用的代理服务器而不是本地代理
2. 可以通过环境变量动态控制是否启用代理
3. 监控代理连接状态，确保服务稳定性

## 相关文件

- `src/app.js` - 主应用文件，包含代理配置
- `test-line-proxy.js` - 代理配置测试脚本
- `.env` - 环境变量配置文件

## 更新日志

- 2025-01-04: 初始版本，支持LINE SDK v10代理配置
- 添加了undici支持，解决了fetch API代理问题
- 提供了完整的测试验证方案
