@echo off
echo 🚀 启动WhatsApp双向通信测试环境
echo =====================================
echo.

echo 📋 这个脚本将帮助您:
echo    1. 启动Webhook接收服务器
echo    2. 提供ngrok设置指导
echo    3. 指导Twilio配置
echo.

echo ⚠️  请确保您已经:
echo    ✅ 完成WhatsApp沙盒设置
echo    ✅ 安装了ngrok (npm install -g ngrok)
echo    ✅ 安装了项目依赖 (npm install)
echo.

pause

echo 🔧 启动Webhook接收服务器...
echo.

start "WhatsApp Webhook Server" cmd /k "node webhook-receiver.js"

timeout /t 3 /nobreak >nul

echo ✅ Webhook服务器已启动在新窗口中
echo.

echo 📡 下一步: 设置ngrok隧道
echo    请在新的命令行窗口运行:
echo    ngrok http 3002
echo.

echo 📋 然后复制ngrok提供的HTTPS URL
echo    例如: https://abc123.ngrok.io
echo.

echo 🔧 在Twilio控制台配置Webhook:
echo    1. 访问: https://console.twilio.com/us1/develop/sms/try-it-out/whatsapp-learn
echo    2. 找到 "When a message comes in" 字段
echo    3. 输入: https://your-ngrok-url.ngrok.io/whatsapp-webhook
echo    4. 保存配置
echo.

echo 📱 测试消息:
echo    从您的WhatsApp发送消息到: +1 ************
echo    尝试发送: hello, test, help, status, info
echo.

echo 📊 监控地址:
echo    健康检查: http://localhost:3002/health
echo    查看消息: http://localhost:3002/messages
echo.

echo 🎯 设置完成！观察Webhook服务器窗口的消息接收日志。
echo.

pause
