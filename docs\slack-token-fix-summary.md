# Slack Bot 令牌过期问题修复总结

## 问题描述

用户遇到以下错误：
```
⚠️ 团队 T0983BEJT4J 的Bot令牌已过期，尝试刷新...
🔄 刷新团队 T0983BEJT4J 的Bot令牌...
❌ 刷新团队 T0983BEJT4J 的Bot令牌失败: 刷新Bot令牌失败: invalid_code
❌ 根据用户ID获取Bot Token失败: 刷新Bot令牌失败: invalid_code
回复slack消息失败,请排查原因
```

## 根本原因分析

通过诊断工具分析，发现问题的根本原因：

1. **令牌已过期**：团队 T0983BEJT4J 的Bot令牌在 2025-08-04T13:40:50.533Z 过期
2. **刷新令牌无效**：存储的 refresh_token 返回 `invalid_code` 错误
3. **错误处理不完善**：系统没有优雅地处理刷新令牌失效的情况

## 解决方案

### 1. 改进令牌刷新逻辑 (`src/services/tokenManager.js`)

**主要改进：**
- 添加详细的错误分类和处理
- 当刷新令牌无效时，自动清理数据库中的无效令牌
- 提供更详细的日志信息
- 改进回退机制

**关键代码改进：**
```javascript
// 根据不同的错误类型提供更详细的处理
switch (error) {
  case 'invalid_code':
    console.error('💡 刷新令牌无效或已过期，需要重新授权');
    await this.markRefreshTokenInvalid(teamId);
    throw new Error(`刷新令牌无效，需要重新授权: ${error}`);
  // ... 其他错误类型
}
```

### 2. 增强错误处理 (`src/app.js`)

**主要改进：**
- 在获取令牌失败时提供更清晰的错误信息
- 添加重新授权的指导信息
- 改进回退机制

**关键代码改进：**
```javascript
// 如果是刷新令牌无效的错误，记录详细信息
if (error.message.includes('刷新令牌无效')) {
  console.error(`🔄 团队 ${team_id} 需要重新授权，刷新令牌已失效`);
  console.error(`💡 解决方案: 访问 /slack/oauth/start 重新授权该工作区`);
}
```

### 3. 创建诊断和监控工具

**诊断工具** (`scripts/diagnoseSlackTokens.js`)：
- 检查所有工作区的令牌状态
- 测试令牌有效性
- 尝试刷新过期令牌
- 提供详细的诊断报告

**监控工具** (`scripts/tokenMonitor.js`)：
- 实时监控令牌状态
- 提供令牌管理命令
- 生成重新授权链接
- 清理无效令牌

**测试工具** (`scripts/testTokenRefresh.js`)：
- 验证令牌刷新功能
- 测试错误处理机制
- 确保系统稳定性

## 使用方法

### 1. 诊断当前状态
```bash
# 检查所有工作区状态
node scripts/tokenMonitor.js status

# 检查特定团队
node scripts/tokenMonitor.js check T0983BEJT4J

# 运行完整诊断
node scripts/diagnoseSlackTokens.js
```

### 2. 解决令牌问题
```bash
# 尝试刷新令牌
node scripts/tokenMonitor.js refresh T0983BEJT4J

# 生成重新授权链接
node scripts/tokenMonitor.js reauth T0983BEJT4J

# 清理无效令牌
node scripts/tokenMonitor.js clean
```

### 3. 重新授权工作区
当刷新令牌无效时，需要重新授权：
1. 访问 `/slack/oauth/start` 端点
2. 完成 Slack OAuth 流程
3. 系统会自动保存新的令牌

## 预防措施

### 1. 定期监控
建议定期运行监控工具检查令牌状态：
```bash
# 每日检查
node scripts/tokenMonitor.js status
```

### 2. 自动化处理
系统现在会自动：
- 检测过期令牌
- 尝试刷新令牌
- 清理无效的刷新令牌
- 提供详细的错误信息

### 3. 日志监控
关注以下日志信息：
- `⚠️ 团队 XXX 的Bot令牌已过期`
- `❌ 刷新令牌无效，需要重新授权`
- `💡 解决方案: 访问 /slack/oauth/start 重新授权该工作区`

## 技术细节

### 令牌生命周期管理
1. **获取令牌**：检查数据库中的令牌
2. **验证有效性**：检查过期时间
3. **自动刷新**：使用 refresh_token 刷新过期令牌
4. **错误处理**：处理刷新失败的情况
5. **清理无效令牌**：移除无效的刷新令牌

### 错误分类
- `invalid_code`：刷新令牌无效或过期
- `invalid_client_id`：客户端ID配置错误
- `invalid_client_secret`：客户端密钥配置错误
- `invalid_grant_type`：授权类型错误

### 回退策略
1. 使用 TokenManager 获取令牌
2. 回退到数据库直接获取
3. 最终回退到全局令牌

## 总结

通过这次修复，系统现在能够：
- ✅ 正确识别和处理过期令牌
- ✅ 优雅地处理刷新令牌失效
- ✅ 提供清晰的错误信息和解决方案
- ✅ 自动清理无效的令牌数据
- ✅ 提供完整的诊断和监控工具

用户现在可以通过访问重新授权链接来解决令牌问题，系统也会提供更好的错误提示和处理机制。
