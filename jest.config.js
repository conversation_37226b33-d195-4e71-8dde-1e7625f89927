module.exports = {
  // 测试环境
  testEnvironment: 'node',

  // 测试文件匹配模式
  testMatch: [
    '**/tests/**/*.test.js',
    '**/__tests__/**/*.js',
    '**/?(*.)+(spec|test).js'
  ],

  // 设置文件
  setupFilesAfterEnv: ['<rootDir>/tests/setup.js'],

  // 覆盖率配置
  collectCoverage: true,
  collectCoverageFrom: [
    'src/**/*.js',
    '!src/app.js', // 排除主应用文件
    '!src/config/database.js', // 排除数据库配置
    '!**/node_modules/**',
    '!**/tests/**'
  ],
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html'],

  // 覆盖率阈值
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 70,
      lines: 70,
      statements: 70
    }
  },

  // 测试超时时间
  testTimeout: 30000,

  // 详细输出
  verbose: true,

  // 在测试失败时停止
  bail: false,

  // 清除模拟
  clearMocks: true,

  // 强制退出
  forceExit: true,

  // 检测打开的句柄
  detectOpenHandles: true
};
