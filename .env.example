# Twilio Configuration
TWILIO_ACCOUNT_SID=your_account_sid_here
TWILIO_AUTH_TOKEN=your_auth_token_here
TWILIO_PHONE_NUMBER=your_twilio_phone_number

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/twilio_messaging
MONGODB_TEST_URI=mongodb://localhost:27017/twilio_messaging_test

# Server Configuration
PORT=3000
NODE_ENV=development

# JWT Configuration
JWT_SECRET=your_jwt_secret_here
JWT_EXPIRES_IN=24h

# API Configuration
API_KEY=your_api_key_here

# Webhook Configuration
WEBHOOK_BASE_URL=https://your-domain.com
WEBHOOK_PORT=3002

# WhatsApp Configuration
WHATSAPP_SANDBOX_NUMBER=whatsapp:+***********
WHATSAPP_AUTO_REPLY=true

# Slack Configuration
SLACK_BOT_TOKEN=xoxb-your-bot-token-here
SLACK_SIGNING_SECRET=your-signing-secret-here
SLACK_APP_TOKEN=xapp-your-app-token-here
SLACK_CLIENT_ID=your-client-id-here
SLACK_CLIENT_SECRET=your-client-secret-here
SLACK_REDIRECT_URI=https://your-domain.com/slack/oauth/callback

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log
