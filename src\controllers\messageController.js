const messageService = require('../services/messageService');
const logger = require('../config/logger');
const Joi = require('joi');

// 验证schemas
const sendMessageSchema = Joi.object({
  to: Joi.string().required().pattern(/^\+[1-9]\d{1,14}$/),
  body: Joi.string().allow('').max(1600),
  mediaUrls: Joi.array().items(Joi.string().uri()),
  campaignId: Joi.string().optional(),
  metadata: Joi.object().optional(),
}).or('body', 'mediaUrls');

const bulkMessageSchema = Joi.object({
  messages: Joi.array().items(sendMessageSchema).min(1).max(100).required(),
});

const messageHistorySchema = Joi.object({
  phoneNumber: Joi.string().required().pattern(/^\+[1-9]\d{1,14}$/),
  limit: Joi.number().integer().min(1).max(100).default(50),
  offset: Joi.number().integer().min(0).default(0),
});

const conversationMessagesSchema = Joi.object({
  conversationId: Joi.string().required(),
  limit: Joi.number().integer().min(1).max(100).default(50),
  offset: Joi.number().integer().min(0).default(0),
});

class MessageController {
  
  /**
   * 发送单条消息
   */
  async sendMessage(req, res) {
    try {
      // 验证请求数据
      const { error, value } = sendMessageSchema.validate(req.body);
      if (error) {
        return res.status(400).json({
          success: false,
          error: 'Validation error',
          details: error.details[0].message,
        });
      }

      // 发送消息
      const result = await messageService.sendMessage(value);

      logger.info('Message sent via API', { 
        messageId: result.messageId,
        to: value.to,
        ip: req.ip 
      });

      res.status(200).json({
        success: true,
        data: result,
      });

    } catch (error) {
      logger.error('Send message API error:', error);
      
      res.status(500).json({
        success: false,
        error: 'Failed to send message',
        message: error.message,
      });
    }
  }

  /**
   * 批量发送消息
   */
  async sendBulkMessages(req, res) {
    try {
      // 验证请求数据
      const { error, value } = bulkMessageSchema.validate(req.body);
      if (error) {
        return res.status(400).json({
          success: false,
          error: 'Validation error',
          details: error.details[0].message,
        });
      }

      // 批量发送消息
      const result = await messageService.sendBulkMessages(value.messages);

      logger.info('Bulk messages sent via API', { 
        total: value.messages.length,
        success: result.success,
        failed: result.failed,
        ip: req.ip 
      });

      res.status(200).json({
        success: true,
        data: result,
      });

    } catch (error) {
      logger.error('Send bulk messages API error:', error);
      
      res.status(500).json({
        success: false,
        error: 'Failed to send bulk messages',
        message: error.message,
      });
    }
  }

  /**
   * 获取消息历史
   */
  async getMessageHistory(req, res) {
    try {
      // 验证请求参数
      const { error, value } = messageHistorySchema.validate(req.query);
      if (error) {
        return res.status(400).json({
          success: false,
          error: 'Validation error',
          details: error.details[0].message,
        });
      }

      // 获取消息历史
      const messages = await messageService.getMessageHistory(
        value.phoneNumber,
        value.limit,
        value.offset
      );

      res.status(200).json({
        success: true,
        data: {
          messages,
          pagination: {
            limit: value.limit,
            offset: value.offset,
            count: messages.length,
          },
        },
      });

    } catch (error) {
      logger.error('Get message history API error:', error);
      
      res.status(500).json({
        success: false,
        error: 'Failed to get message history',
        message: error.message,
      });
    }
  }

  /**
   * 获取会话消息
   */
  async getConversationMessages(req, res) {
    try {
      // 验证请求参数
      const { error, value } = conversationMessagesSchema.validate({
        ...req.params,
        ...req.query,
      });
      if (error) {
        return res.status(400).json({
          success: false,
          error: 'Validation error',
          details: error.details[0].message,
        });
      }

      // 获取会话消息
      const messages = await messageService.getConversationMessages(
        value.conversationId,
        value.limit,
        value.offset
      );

      res.status(200).json({
        success: true,
        data: {
          messages,
          pagination: {
            limit: value.limit,
            offset: value.offset,
            count: messages.length,
          },
        },
      });

    } catch (error) {
      logger.error('Get conversation messages API error:', error);
      
      res.status(500).json({
        success: false,
        error: 'Failed to get conversation messages',
        message: error.message,
      });
    }
  }
}

module.exports = new MessageController();
