// 智能回复系统 - 集成Coze API v3和本地回复
const cozeV3Client = require('./coze-api-v3-client');

class SmartReplySystem {
  constructor() {
    this.userSessions = {};
    this.useCoze = true; // 是否使用Coze API
    this.fallbackReplies = this.initializeFallbackReplies();
  }

  // 初始化本地回复库
  initializeFallbackReplies() {
    return {
      greetings: [
        '你好！很高兴与您对话。有什么我可以帮助您的吗？',
        '您好！我是您的AI助手，请问有什么需要帮助的？',
        '欢迎！我在这里为您提供帮助。'
      ],
      questions: [
        '这是一个很好的问题。让我为您详细解答...',
        '关于您的问题，我来为您分析一下...',
        '您提出的问题很有意思，我的看法是...'
      ],
      thanks: [
        '不客气！很高兴能帮助到您。',
        '您太客气了！有其他问题随时问我。',
        '很乐意为您服务！'
      ],
      help: [
        '我可以帮助您进行对话、回答问题、提供建议等。请告诉我您需要什么帮助。',
        '我是您的AI助手，可以协助您处理各种问题。请描述您的需求。',
        '我能够理解和回复您的消息。请随时向我提问或聊天。'
      ],
      default: [
        '我理解您的意思。让我想想如何更好地回复您...',
        '这很有趣！您能告诉我更多详情吗？',
        '我正在思考您的消息。请给我一点时间来组织回复。',
        '感谢您的消息。我会尽力为您提供有用的回复。',
        '您的消息很有意思。让我为您提供一些想法...'
      ]
    };
  }

  // 分析消息类型
  analyzeMessage(message) {
    const lowerMessage = message.toLowerCase();
    
    // 问候语
    if (lowerMessage.includes('你好') || lowerMessage.includes('hello') || 
        lowerMessage.includes('hi') || lowerMessage.includes('嗨')) {
      return 'greetings';
    }
    
    // 感谢
    if (lowerMessage.includes('谢谢') || lowerMessage.includes('thank') || 
        lowerMessage.includes('感谢')) {
      return 'thanks';
    }
    
    // 帮助请求
    if (lowerMessage.includes('帮助') || lowerMessage.includes('help') || 
        lowerMessage.includes('怎么') || lowerMessage.includes('如何')) {
      return 'help';
    }
    
    // 问题
    if (lowerMessage.includes('?') || lowerMessage.includes('？') || 
        lowerMessage.includes('什么') || lowerMessage.includes('为什么') ||
        lowerMessage.includes('怎样') || lowerMessage.includes('哪里')) {
      return 'questions';
    }
    
    return 'default';
  }

  // 生成本地回复
  generateLocalReply(message, userId) {
    const messageType = this.analyzeMessage(message);
    const replies = this.fallbackReplies[messageType];
    
    // 随机选择一个回复
    const randomReply = replies[Math.floor(Math.random() * replies.length)];
    
    // 个性化回复
    const session = this.userSessions[userId];
    if (session && session.name) {
      return randomReply.replace('您', session.name);
    }
    
    return randomReply;
  }

  // 主要的智能回复方法
  async generateSmartReply(message, userId, userName = null, mediaInfo = null) {
    console.log(`🧠 为用户 ${userId} 生成智能回复...`);
    console.log(`   用户消息: ${message}`);
    console.log(`   用户名称: ${userName || 'Unknown'}`);

    // 更新用户会话信息
    if (!this.userSessions[userId]) {
      this.userSessions[userId] = {
        userId: userId,
        name: userName,
        messageCount: 0,
        lastActivity: new Date(),
        conversationStarted: new Date()
      };
    }
    
    this.userSessions[userId].messageCount += 1;
    this.userSessions[userId].lastActivity = new Date();
    if (userName) {
      this.userSessions[userId].name = userName;
    }

    let reply = '';
    let source = '';

    // 尝试使用Coze API v3
    if (this.useCoze) {
      try {
        console.log('🤖 尝试使用Coze API v3...');
        reply = await cozeV3Client.generateReply(message, userId, false, mediaInfo);
        source = 'Coze AI v3';

        // 检查回复质量
        if (reply && reply.length > 10 && !reply.includes('很抱歉') && !reply.includes('无法') && !reply.includes('正在思考')) {
          console.log(`✅ Coze AI v3回复成功: ${reply.substring(0, 50)}...`);
          return {
            text: reply,
            source: source,
            success: true
          };
        } else {
          console.log('⚠️ Coze AI v3回复质量不佳，使用本地回复');
        }

      } catch (error) {
        console.log('❌ Coze API v3失败:', error.message);

        // 检查是否是系统内部错误
        if (error.message.includes('Model encountered an internal error') ||
            error.message.includes('system-level issue') ||
            error.message.includes('Request parameter error') ||
            error.message.includes('Coze处理失败')) {
          console.log('🚨 检测到系统级错误，提示联系人工客服');
          return {
            text: '抱歉，系统当前繁忙，请稍后再试或联系人工客服获得帮助。我们会尽快为您解决问题！',
            source: '系统繁忙提示',
            success: false
          };
        }
      }
    }

    // 使用本地智能回复
    console.log('🎯 使用本地智能回复系统...');
    reply = this.generateLocalReply(message, userId);
    source = 'Local AI';

    // 添加上下文信息
    const session = this.userSessions[userId];
    if (session.messageCount === 1) {
      reply = `${reply}\n\n这是我们的第一次对话，很高兴认识您！`;
    } else if (session.messageCount % 5 === 0) {
      reply = `${reply}\n\n我们已经聊了${session.messageCount}条消息了，感谢您的耐心！`;
    }

    console.log(`✅ 本地回复生成: ${reply.substring(0, 50)}...`);

    return {
      text: reply,
      source: source,
      success: true
    };
  }

  // 获取用户会话信息
  getUserSession(userId) {
    return this.userSessions[userId] || null;
  }

  // 清除用户会话
  clearUserSession(userId) {
    delete this.userSessions[userId];
    return true;
  }

  // 获取所有活跃会话
  getActiveSessions() {
    return this.userSessions;
  }

  // 设置是否使用Coze API
  setUseCoze(useCoze) {
    this.useCoze = useCoze;
    console.log(`🔧 Coze API使用状态: ${useCoze ? '启用' : '禁用'}`);
  }

  // 测试系统功能
  async testSystem() {
    console.log('🧪 测试智能回复系统...\n');

    const testMessages = [
      { message: '你好', expected: 'greetings' },
      { message: '你能帮我做什么？', expected: 'help' },
      { message: '今天天气怎么样？', expected: 'questions' },
      { message: '谢谢你的帮助', expected: 'thanks' },
      { message: '这是一条普通消息', expected: 'default' }
    ];

    const results = [];

    for (const test of testMessages) {
      console.log(`📝 测试消息: "${test.message}"`);
      
      const result = await this.generateSmartReply(test.message, 'test_user', 'Test User');
      
      console.log(`   回复: ${result.text}`);
      console.log(`   来源: ${result.source}`);
      console.log(`   成功: ${result.success ? '✅' : '❌'}`);
      console.log('');
      
      results.push({
        message: test.message,
        reply: result.text,
        source: result.source,
        success: result.success
      });

      // 等待1秒
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    console.log('📊 测试结果汇总:');
    console.log(`   总测试数: ${results.length}`);
    console.log(`   成功数: ${results.filter(r => r.success).length}`);
    console.log(`   Coze AI回复: ${results.filter(r => r.source === 'Coze AI').length}`);
    console.log(`   本地回复: ${results.filter(r => r.source === 'Local AI').length}`);

    return results;
  }
}

// 创建全局实例
const smartReplySystem = new SmartReplySystem();

module.exports = smartReplySystem;
