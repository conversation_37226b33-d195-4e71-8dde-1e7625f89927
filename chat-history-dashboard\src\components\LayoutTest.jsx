import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';

export function LayoutTest() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">布局测试页面</h1>
        <p className="text-gray-600">测试侧边栏和主内容区域的布局是否正确</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>测试卡片 1</CardTitle>
            <CardDescription>这是第一个测试卡片</CardDescription>
          </CardHeader>
          <CardContent>
            <p>侧边栏应该在左侧，主内容应该在右侧占据剩余空间。</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>测试卡片 2</CardTitle>
            <CardDescription>这是第二个测试卡片</CardDescription>
          </CardHeader>
          <CardContent>
            <p>在大屏幕上，侧边栏应该始终可见。在小屏幕上，侧边栏应该可以通过菜单按钮切换。</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>测试卡片 3</CardTitle>
            <CardDescription>这是第三个测试卡片</CardDescription>
          </CardHeader>
          <CardContent>
            <p>主内容区域应该可以正常滚动，不会被侧边栏遮挡。</p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>布局检查清单</CardTitle>
          <CardDescription>请检查以下布局要求是否满足</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-green-500 rounded-full"></div>
              <span>侧边栏在左侧正确显示</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-green-500 rounded-full"></div>
              <span>主内容在右侧占据剩余空间</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-green-500 rounded-full"></div>
              <span>顶部导航栏正确显示</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-green-500 rounded-full"></div>
              <span>内容可以正常滚动</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-green-500 rounded-full"></div>
              <span>移动端菜单按钮可以切换侧边栏</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 添加一些内容来测试滚动 */}
      <div className="space-y-4">
        {[...Array(10)].map((_, i) => (
          <Card key={i}>
            <CardHeader>
              <CardTitle>滚动测试卡片 {i + 1}</CardTitle>
            </CardHeader>
            <CardContent>
              <p>这是用于测试页面滚动的卡片。页面应该可以正常滚动，不会出现布局问题。</p>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
