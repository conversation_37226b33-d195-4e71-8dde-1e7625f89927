const logger = require('../config/logger');

/**
 * 全局错误处理中间件
 */
const errorHandler = (err, req, res, next) => {
  let error = { ...err };
  error.message = err.message;

  // 记录错误日志
  logger.error('Error occurred:', {
    message: err.message,
    stack: err.stack,
    url: req.originalUrl,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    body: req.body,
    params: req.params,
    query: req.query,
  });

  // Mongoose错误处理
  if (err.name === 'CastError') {
    const message = 'Invalid resource ID';
    error = { message, statusCode: 400 };
  }

  // Mongoose重复字段错误
  if (err.code === 11000) {
    const message = 'Duplicate field value entered';
    error = { message, statusCode: 400 };
  }

  // Mongoose验证错误
  if (err.name === 'ValidationError') {
    const message = Object.values(err.errors).map(val => val.message).join(', ');
    error = { message, statusCode: 400 };
  }

  // JWT错误
  if (err.name === 'JsonWebTokenError') {
    const message = 'Invalid token';
    error = { message, statusCode: 401 };
  }

  if (err.name === 'TokenExpiredError') {
    const message = 'Token expired';
    error = { message, statusCode: 401 };
  }

  // Twilio错误
  if (err.code && err.code.toString().startsWith('2')) {
    const message = `Twilio error: ${err.message}`;
    error = { message, statusCode: 400 };
  }

  // 语法错误
  if (err instanceof SyntaxError && err.status === 400 && 'body' in err) {
    const message = 'Invalid JSON format';
    error = { message, statusCode: 400 };
  }

  res.status(error.statusCode || 500).json({
    success: false,
    error: error.message || 'Server Error',
    ...(process.env.NODE_ENV === 'development' && { stack: err.stack }),
  });
};

/**
 * 404错误处理中间件
 */
const notFound = (req, res, next) => {
  const message = `Route ${req.originalUrl} not found`;
  
  logger.warn('Route not found:', {
    url: req.originalUrl,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
  });

  res.status(404).json({
    success: false,
    error: message,
  });
};

/**
 * 异步错误捕获包装器
 */
const asyncHandler = (fn) => (req, res, next) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};

/**
 * 验证错误处理
 */
const validationErrorHandler = (error, req, res, next) => {
  if (error.isJoi) {
    return res.status(400).json({
      success: false,
      error: 'Validation error',
      details: error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
      })),
    });
  }
  next(error);
};

/**
 * 数据库连接错误处理
 */
const databaseErrorHandler = (error, req, res, next) => {
  if (error.name === 'MongoNetworkError' || error.name === 'MongoTimeoutError') {
    logger.error('Database connection error:', error);
    return res.status(503).json({
      success: false,
      error: 'Database connection error. Please try again later.',
    });
  }
  next(error);
};

/**
 * 请求超时处理
 */
const timeoutHandler = (timeout = 30000) => {
  return (req, res, next) => {
    res.setTimeout(timeout, () => {
      logger.warn('Request timeout:', {
        url: req.originalUrl,
        method: req.method,
        ip: req.ip,
        timeout: timeout,
      });

      if (!res.headersSent) {
        res.status(408).json({
          success: false,
          error: 'Request timeout',
        });
      }
    });
    next();
  };
};

/**
 * 内存使用监控中间件
 */
const memoryMonitor = (req, res, next) => {
  const used = process.memoryUsage();
  const memoryUsageMB = Math.round(used.heapUsed / 1024 / 1024 * 100) / 100;
  
  // 如果内存使用超过阈值，记录警告
  if (memoryUsageMB > 500) { // 500MB阈值
    logger.warn('High memory usage detected:', {
      heapUsed: `${memoryUsageMB} MB`,
      url: req.originalUrl,
      method: req.method,
    });
  }

  next();
};

module.exports = {
  errorHandler,
  notFound,
  asyncHandler,
  validationErrorHandler,
  databaseErrorHandler,
  timeoutHandler,
  memoryMonitor,
};
