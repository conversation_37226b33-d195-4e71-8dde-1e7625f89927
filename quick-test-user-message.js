// 快速测试：向指定用户发送消息
require('dotenv').config();
const axios = require('axios');

async function quickTestUserMessage() {
  console.log('⚡ 快速测试：向用户 U0983BEK1HQ 发送消息');
  console.log('=' .repeat(50));

  const targetUserId = 'U0983BEK1HQ';
  const message = '🧪 快速测试消息\n\n您好！这是来自畅游网络智能客服的测试消息。\n\n如果您收到这条消息，说明 Slack 私聊功能已经正常工作了！';

  console.log(`📋 目标用户: ${targetUserId}`);
  console.log(`📝 消息内容: ${message.substring(0, 30)}...`);
  console.log('');

  try {
    // 步骤 1: 打开私聊通道
    const openRequestData = { users: targetUserId };
    const requestHeaders = {
      'Authorization': `Bearer ${process.env.SLACK_BOT_TOKEN}`,
      'Content-Type': 'application/json'
    };

    console.log('🔗 Slack API Request - conversations.open:');
    console.log('   URL:', 'https://slack.com/api/conversations.open');
    console.log('   Data:', JSON.stringify(openRequestData));
    console.log('   Headers:', { ...requestHeaders, 'Authorization': 'Bearer [HIDDEN]' });

    const openResponse = await axios.post('https://slack.com/api/conversations.open', openRequestData, {
      headers: requestHeaders
    });

    console.log('📥 Slack API Response - conversations.open:');
    console.log('   Status:', openResponse.status);
    console.log('   Data:', JSON.stringify(openResponse.data));

    if (!openResponse.data.ok) {
      throw new Error(`打开私聊通道失败: ${openResponse.data.error}`);
    }

    const channelId = openResponse.data.channel.id;

    // 步骤 2: 发送消息
    const messageRequestData = {
      channel: channelId,
      text: message
    };

    console.log('📤 Slack API Request - chat.postMessage:');
    console.log('   URL:', 'https://slack.com/api/chat.postMessage');
    console.log('   Data:', JSON.stringify(messageRequestData));
    console.log('   Headers:', { ...requestHeaders, 'Authorization': 'Bearer [HIDDEN]' });

    const messageResponse = await axios.post('https://slack.com/api/chat.postMessage', messageRequestData, {
      headers: requestHeaders
    });

    console.log('📥 Slack API Response - chat.postMessage:');
    console.log('   Status:', messageResponse.status);
    console.log('   Data:', JSON.stringify(messageResponse.data));

    if (!messageResponse.data.ok) {
      throw new Error(`发送消息失败: ${messageResponse.data.error}`);
    }

    console.log('🎉 测试成功！用户应该已经收到消息了。');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    
    if (error.message.includes('missing_scope')) {
      console.log('');
      console.log('🔧 需要添加权限:');
      console.log('   - im:write (发送私信)');
      console.log('   - chat:write (发送消息)');
      console.log('');
      console.log('📋 修复步骤:');
      console.log('1. 访问 https://api.slack.com/apps');
      console.log('2. 选择您的 App');
      console.log('3. 进入 "OAuth & Permissions"');
      console.log('4. 添加上述权限');
      console.log('5. 重新安装 App');
    }
  }
}

// 运行快速测试
quickTestUserMessage();
