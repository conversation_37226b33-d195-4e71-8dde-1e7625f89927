/**
 * 测试令牌刷新功能
 * 验证修复后的令牌管理系统是否正常工作
 */

require('dotenv').config();
const TokenManager = require('../src/services/tokenManager');
const SlackAuthDatabase = require('../src/database/slackAuth');

class TokenRefreshTester {
  constructor() {
    this.tokenManager = new TokenManager();
    this.db = new SlackAuthDatabase();
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('🧪 开始测试令牌刷新功能');
    console.log('=' .repeat(60));

    try {
      // 测试1: 获取现有令牌
      await this.testGetExistingToken();
      
      // 测试2: 处理过期令牌
      await this.testExpiredToken();
      
      // 测试3: 处理无效刷新令牌
      await this.testInvalidRefreshToken();
      
      // 测试4: 错误处理
      await this.testErrorHandling();

      console.log('\n✅ 所有测试完成');

    } catch (error) {
      console.error('❌ 测试过程中出现错误:', error.message);
    }
  }

  /**
   * 测试获取现有令牌
   */
  async testGetExistingToken() {
    console.log('\n🧪 测试1: 获取现有令牌');
    console.log('-' .repeat(40));

    try {
      const teamId = 'T0983BEJT4J';
      const token = await this.tokenManager.getBotToken(teamId);
      
      if (token) {
        console.log('✅ 成功获取令牌');
        console.log(`   令牌长度: ${token.length}`);
        console.log(`   令牌前缀: ${token.substring(0, 10)}...`);
      } else {
        console.log('⚠️ 未获取到令牌（可能需要重新授权）');
      }

    } catch (error) {
      console.log('❌ 获取令牌失败:', error.message);
    }
  }

  /**
   * 测试过期令牌处理
   */
  async testExpiredToken() {
    console.log('\n🧪 测试2: 过期令牌处理');
    console.log('-' .repeat(40));

    try {
      // 获取工作区信息
      const workspace = await this.db.getWorkspaceByTeamId('T0983BEJT4J');
      
      if (!workspace) {
        console.log('⚠️ 未找到工作区信息，跳过此测试');
        return;
      }

      if (workspace.bot_token_expires_at) {
        const expiresAt = new Date(workspace.bot_token_expires_at);
        const now = new Date();
        const isExpired = expiresAt <= now;
        
        console.log(`   过期时间: ${expiresAt.toISOString()}`);
        console.log(`   当前时间: ${now.toISOString()}`);
        console.log(`   是否过期: ${isExpired ? '是' : '否'}`);
        
        if (isExpired) {
          console.log('✅ 系统正确识别了过期令牌');
          
          if (workspace.bot_refresh_token) {
            console.log('   有刷新令牌，系统会尝试刷新');
          } else {
            console.log('   无刷新令牌，系统会返回原始令牌');
          }
        } else {
          console.log('✅ 令牌未过期，系统会直接返回');
        }
      } else {
        console.log('✅ 令牌无过期时间，被视为永久有效');
      }

    } catch (error) {
      console.log('❌ 测试过期令牌处理失败:', error.message);
    }
  }

  /**
   * 测试无效刷新令牌处理
   */
  async testInvalidRefreshToken() {
    console.log('\n🧪 测试3: 无效刷新令牌处理');
    console.log('-' .repeat(40));

    try {
      // 模拟一个无效的刷新令牌
      const fakeRefreshToken = 'xoxe-1-fake-refresh-token';
      
      console.log('   模拟使用无效刷新令牌...');
      
      try {
        await this.tokenManager.refreshBotToken('T0983BEJT4J', fakeRefreshToken);
        console.log('❌ 预期应该失败，但成功了');
      } catch (error) {
        if (error.message.includes('刷新令牌无效')) {
          console.log('✅ 系统正确处理了无效刷新令牌');
          console.log(`   错误信息: ${error.message}`);
        } else {
          console.log('⚠️ 错误处理可能需要改进');
          console.log(`   错误信息: ${error.message}`);
        }
      }

    } catch (error) {
      console.log('❌ 测试无效刷新令牌处理失败:', error.message);
    }
  }

  /**
   * 测试错误处理
   */
  async testErrorHandling() {
    console.log('\n🧪 测试4: 错误处理');
    console.log('-' .repeat(40));

    try {
      // 测试不存在的团队ID
      console.log('   测试不存在的团队ID...');
      const token = await this.tokenManager.getBotToken('FAKE_TEAM_ID');
      
      if (token === null) {
        console.log('✅ 正确处理了不存在的团队ID');
      } else {
        console.log('⚠️ 对不存在的团队ID返回了令牌，可能需要检查');
      }

      // 测试空团队ID
      console.log('   测试空团队ID...');
      const emptyToken = await this.tokenManager.getBotToken('');
      
      if (emptyToken === null) {
        console.log('✅ 正确处理了空团队ID');
      } else {
        console.log('⚠️ 对空团队ID返回了令牌，可能需要检查');
      }

    } catch (error) {
      console.log('❌ 测试错误处理失败:', error.message);
    }
  }

  /**
   * 显示系统状态
   */
  async showSystemStatus() {
    console.log('\n📊 系统状态');
    console.log('-' .repeat(40));

    try {
      const workspaces = await this.db.getAllWorkspaces();
      console.log(`   工作区数量: ${workspaces.length}`);
      
      for (const workspace of workspaces) {
        const status = this.getTokenStatus(workspace);
        console.log(`   ${workspace.team_name}: ${status}`);
      }

    } catch (error) {
      console.log('❌ 获取系统状态失败:', error.message);
    }
  }

  /**
   * 获取令牌状态
   */
  getTokenStatus(workspace) {
    if (!workspace.bot_user_access_token) {
      return '无令牌';
    }

    if (!workspace.bot_token_expires_at) {
      return '永久有效';
    }

    const now = Date.now();
    if (workspace.bot_token_expires_at <= now) {
      return workspace.bot_refresh_token ? '已过期(可刷新)' : '已过期(不可刷新)';
    }

    return '有效';
  }
}

// 运行测试
async function main() {
  const tester = new TokenRefreshTester();
  
  // 显示当前系统状态
  await tester.showSystemStatus();
  
  // 运行所有测试
  await tester.runAllTests();
  
  // 显示测试后的系统状态
  await tester.showSystemStatus();
  
  process.exit(0);
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = TokenRefreshTester;
