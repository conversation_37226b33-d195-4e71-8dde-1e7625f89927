# Webhook聊天历史管理系统

## 系统概述

这是一个完整的webhook日志记录和聊天历史管理系统，包含以下主要功能：

1. **数据库日志记录** - 记录所有webhook请求和消息数据
2. **React前端仪表板** - 查看和管理聊天历史
3. **RESTful API** - 为前端提供数据服务
4. **多平台支持** - WhatsApp、Slack、LINE

## 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Webhook       │    │   Node.js       │    │   React         │
│   Requests      │───▶│   Backend       │───▶│   Dashboard     │
│   (WhatsApp,    │    │   (Express +    │    │   (Vite +       │
│   Slack, LINE)  │    │   SQLite)       │    │   shadcn/ui)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 已实现的功能

### 1. 数据库架构扩展 ✅
- **webhook_logs表** - 存储所有webhook请求
- **users表** - 存储跨平台用户信息
- **conversations表** - 管理对话会话
- **messages表** - 存储所有消息记录
- **索引优化** - 提高查询性能

### 2. Webhook处理器修改 ✅
- **WhatsApp webhook** - 记录接收和发送的消息
- **Slack webhook** - 记录事件和消息
- **LINE webhook** - 记录用户交互
- **错误处理** - 完善的异常处理机制

### 3. RESTful API开发 ✅
- `GET /api/` - API信息
- `GET /api/conversations` - 获取对话列表
- `GET /api/conversations/:id/messages` - 获取对话消息
- `GET /api/users` - 获取用户列表
- `GET /api/search` - 搜索消息
- `GET /api/stats` - 获取统计信息
- `GET /api/webhook-logs` - 获取webhook日志
- `GET /api/knowledge/status` - 获取知识图谱状态
- `GET /api/knowledge/data` - 获取知识图谱数据
- `GET /api/knowledge/search` - 搜索知识图谱
- `POST /api/knowledge/reset` - 重置知识图谱
- `POST /api/knowledge/update` - 更新知识图谱

### 4. React前端应用 ✅
- **仪表板** - 显示统计信息和概览
- **对话列表** - 浏览所有对话
- **对话详情** - 查看具体消息记录
- **用户管理** - 查看用户信息和活动状态
- **消息搜索** - 全文搜索功能
- **知识图谱管理** - 管理和维护智能客服知识库
- **响应式设计** - 支持移动端和桌面端

## 技术栈

### 后端
- **Node.js** + **Express.js** - 服务器框架
- **SQLite3** - 数据库
- **现有Twilio集成** - WhatsApp、Slack、LINE

### 前端
- **React 18** - UI框架
- **Vite** - 构建工具
- **shadcn/ui** - UI组件库
- **Tailwind CSS** - 样式框架
- **React Router** - 路由管理
- **Axios** - HTTP客户端
- **date-fns** - 日期处理
- **Lucide React** - 图标库

## 项目结构

```
twilio/
├── src/
│   ├── app.js                 # 主应用文件（已修改）
│   ├── database/
│   │   ├── webhookLogger.js   # Webhook日志服务（新增）
│   │   └── messageHistory.js  # 消息历史服务（新增）
│   └── routes/
│       └── api.js             # API路由（新增）
├── chat-history-dashboard/    # React前端项目（新增）
│   ├── src/
│   │   ├── components/
│   │   │   ├── Layout.jsx
│   │   │   ├── Dashboard.jsx
│   │   │   ├── ConversationsList.jsx
│   │   │   ├── ConversationDetail.jsx
│   │   │   ├── UsersList.jsx
│   │   │   ├── SearchResults.jsx
│   │   │   └── ui/            # shadcn/ui组件
│   │   ├── services/
│   │   │   └── api.js         # API服务
│   │   └── App.jsx
│   ├── package.json
│   └── vite.config.js
├── data/
│   └── webhook_logs.db        # 新的数据库文件
├── test-api.js                # API测试脚本
├── test-webhook.js            # Webhook测试脚本
└── WEBHOOK_CHAT_HISTORY_README.md
```

## 启动指南

### 1. 启动后端服务器
```bash
cd twilio
node src/app.js
```
服务器将在 http://localhost:3002 启动

### 2. 启动前端开发服务器
```bash
cd chat-history-dashboard
npm run dev
```
前端将在 http://localhost:3001 启动

### 3. 访问应用
打开浏览器访问 http://localhost:3001 查看聊天历史仪表板

## API端点

| 端点 | 方法 | 描述 |
|------|------|------|
| `/api/` | GET | API信息 |
| `/api/conversations` | GET | 获取对话列表 |
| `/api/conversations/:id/messages` | GET | 获取对话消息 |
| `/api/users` | GET | 获取用户列表 |
| `/api/search` | GET | 搜索消息 |
| `/api/stats` | GET | 获取统计信息 |
| `/api/webhook-logs` | GET | 获取webhook日志 |

## 数据库表结构

### webhook_logs
- id, webhook_type, raw_payload, headers, timestamp, processed, processing_error

### users  
- id, platform_user_id, platform, display_name, phone_number, profile_data, created_at, updated_at

### conversations
- id, user_id, platform, channel_id, conversation_key, created_at, updated_at

### messages
- id, conversation_id, webhook_log_id, message_type, content, sender_id, recipient_id, message_sid, timestamp, metadata

## 测试

### 测试API端点
```bash
node test-api.js
```

### 测试Webhook
```bash
node test-webhook.js
```

## 功能特性

### 仪表板功能
- 📊 实时统计信息
- 📱 多平台支持状态
- 📈 消息趋势分析
- 👥 用户活动概览

### 对话管理
- 💬 对话列表浏览
- 🔍 平台筛选
- 📝 消息详情查看
- ⏰ 时间排序

### 用户管理
- 👤 用户信息展示
- 🟢 活动状态指示
- 📊 用户统计数据
- 🔄 实时更新

### 搜索功能
- 🔍 全文搜索
- 📅 日期范围筛选
- 🏷️ 平台筛选
- 💡 关键词高亮

### 知识图谱管理
- 🧠 知识库状态监控
- 📚 知识条目查看和编辑
- 🔍 知识搜索测试
- 🔄 同步到Zep云服务
- 🗑️ 知识库重置功能

## 安全考虑

- ✅ CORS配置
- ✅ 输入验证
- ✅ 错误处理
- ✅ SQL注入防护（使用参数化查询）

## 性能优化

- ✅ 数据库索引
- ✅ 分页查询
- ✅ 响应缓存
- ✅ 懒加载

## 未来扩展

- [ ] 实时WebSocket更新
- [ ] 数据导出功能
- [ ] 高级分析报表
- [ ] 用户权限管理
- [ ] 消息备份和恢复

## 注意事项

1. 确保端口3001（前端）和3002（后端）未被占用
2. SQLite数据库文件会自动创建在 `data/webhook_logs.db`
3. 所有webhook请求都会被自动记录到数据库
4. 前端通过API与后端通信，支持跨域请求

## 支持

如有问题，请检查：
1. 后端服务器是否正常启动
2. 数据库连接是否成功
3. API端点是否响应正常
4. 前端是否能正常访问后端API
