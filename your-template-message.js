// 基于您提供的代码发送模板消息
require('dotenv').config();

async function sendYourTemplateMessage() {
  console.log('📱 发送您的模板消息...\n');

  try {
    // 使用您的凭据（建议从环境变量读取）
    const accountSid = process.env.TWILIO_ACCOUNT_SID || 'AC7657552c992e2a3737961532e7e609d1';
    const authToken = process.env.TWILIO_AUTH_TOKEN; // 请在.env文件中设置
    
    if (!authToken) {
      console.error('❌ 请在.env文件中设置TWILIO_AUTH_TOKEN');
      return;
    }

    const client = require('twilio')(accountSid, authToken);

    console.log('📋 消息详情:');
    console.log('   Account SID:', accountSid);
    console.log('   发送方:', 'whatsapp:+*********** (Twilio沙盒)');
    console.log('   接收方:', 'whatsapp:+***********');
    console.log('   内容模板ID:', 'HXb5b62575e6e4ff6129ad7c8efe1f983e');
    console.log('   模板变量:');
    console.log('     变量1: "12/1" (可能是日期)');
    console.log('     变量2: "3pm" (可能是时间)');
    console.log('');

    console.log('🚀 正在发送模板消息...');

    // 使用您提供的确切代码结构
    const message = await client.messages.create({
      from: 'whatsapp:+***********',
      contentSid: 'HXb5b62575e6e4ff6129ad7c8efe1f983e',
      contentVariables: '{"1":"12/1","2":"3pm"}',
      to: 'whatsapp:+***********'
    });

    console.log('✅ 模板消息发送成功!');
    console.log('   消息SID:', message.sid);
    console.log('   初始状态:', message.status);
    console.log('   发送时间:', new Date().toISOString());
    console.log('');

    // 等待状态更新
    console.log('⏳ 等待消息状态更新（5秒）...');
    
    setTimeout(async () => {
      try {
        const updatedMessage = await client.messages(message.sid).fetch();
        
        console.log('📊 消息状态更新:');
        console.log('   当前状态:', updatedMessage.status);
        console.log('   发送时间:', updatedMessage.dateSent || 'Pending');
        console.log('   价格:', updatedMessage.price || 'N/A', updatedMessage.priceUnit || '');
        
        if (updatedMessage.errorCode) {
          console.log('   错误代码:', updatedMessage.errorCode);
          console.log('   错误信息:', updatedMessage.errorMessage);
        }

        // 状态解释
        console.log('\n📱 状态说明:');
        switch (updatedMessage.status) {
          case 'queued':
            console.log('   📤 消息已排队，等待发送');
            break;
          case 'sending':
            console.log('   🔄 消息正在发送中');
            break;
          case 'sent':
            console.log('   ✅ 消息已发送到WhatsApp');
            break;
          case 'delivered':
            console.log('   📱 消息已送达到目标设备');
            break;
          case 'read':
            console.log('   👀 消息已被阅读');
            break;
          case 'failed':
            console.log('   ❌ 消息发送失败');
            break;
          case 'undelivered':
            console.log('   ⚠️ 消息未能送达');
            break;
        }

        console.log('\n🎯 模板消息发送完成！');
        
      } catch (statusError) {
        console.log('⚠️ 状态查询失败:', statusError.message);
      }
    }, 5000);

    return message;

  } catch (error) {
    console.error('❌ 模板消息发送失败:', error.message);
    
    // 详细错误分析
    console.log('\n🔧 错误分析:');
    
    if (error.code === 63016) {
      console.error('   原因: 接收号码问题');
      console.error('   解决方案:');
      console.error('     1. 确保 +*********** 已加入WhatsApp沙盒');
      console.error('     2. 验证号码格式正确');
      console.error('     3. 检查沙盒权限');
    } else if (error.code === 63017) {
      console.error('   原因: 内容模板问题');
      console.error('   解决方案:');
      console.error('     1. 验证模板ID: HXb5b62575e6e4ff6129ad7c8efe1f983e');
      console.error('     2. 检查模板是否已批准');
      console.error('     3. 验证模板变量格式');
    } else if (error.code === 21211) {
      console.error('   原因: 电话号码格式无效');
      console.error('   解决方案: 使用正确的WhatsApp格式 whatsapp:+**********');
    } else if (error.code === 20003) {
      console.error('   原因: Twilio认证失败');
      console.error('   解决方案: 检查Account SID和Auth Token');
    } else {
      console.error('   错误代码:', error.code);
      console.error('   详细信息:', error.moreInfo || 'N/A');
    }

    throw error;
  }
}

// 测试不同的模板变量
async function testDifferentVariables() {
  console.log('🧪 测试不同的模板变量...\n');

  const testCases = [
    { variables: '{"1":"12/1","2":"3pm"}', description: '原始变量' },
    { variables: '{"1":"12/15","2":"2pm"}', description: '不同日期时间' },
    { variables: '{"1":"今天","2":"下午3点"}', description: '中文变量' },
  ];

  for (const testCase of testCases) {
    console.log(`📝 测试: ${testCase.description}`);
    console.log(`   变量: ${testCase.variables}`);
    
    try {
      const accountSid = process.env.TWILIO_ACCOUNT_SID;
      const authToken = process.env.TWILIO_AUTH_TOKEN;
      const client = require('twilio')(accountSid, authToken);

      const message = await client.messages.create({
        from: 'whatsapp:+***********',
        contentSid: 'HXb5b62575e6e4ff6129ad7c8efe1f983e',
        contentVariables: testCase.variables,
        to: 'whatsapp:+***********'
      });

      console.log(`   ✅ 发送成功: ${message.sid}`);
      
    } catch (error) {
      console.log(`   ❌ 发送失败: ${error.message}`);
    }

    // 等待1秒避免发送过快
    await new Promise(resolve => setTimeout(resolve, 1000));
    console.log('');
  }
}

// 主函数
async function main() {
  console.log('📱 Twilio WhatsApp模板消息发送工具');
  console.log('=' .repeat(60));
  console.log('🎯 基于您提供的代码参数');
  console.log('');

  const command = process.argv[2];

  try {
    switch (command) {
      case 'send':
        await sendYourTemplateMessage();
        break;
        
      case 'test':
        await testDifferentVariables();
        break;
        
      default:
        console.log('📋 可用命令:');
        console.log('   send - 发送您的模板消息');
        console.log('   test - 测试不同的模板变量');
        console.log('');
        console.log('📝 使用示例:');
        console.log('   node your-template-message.js send');
        console.log('   node your-template-message.js test');
        console.log('');
        console.log('⚠️ 注意事项:');
        console.log('   1. 确保接收号码 +*********** 已加入沙盒');
        console.log('   2. 验证模板ID HXb5b62575e6e4ff6129ad7c8efe1f983e 有效');
        console.log('   3. 检查.env文件中的TWILIO_AUTH_TOKEN');
        
        // 默认执行发送
        console.log('\n🚀 执行默认发送...');
        await sendYourTemplateMessage();
    }

  } catch (error) {
    console.log('\n💥 执行失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = {
  sendYourTemplateMessage,
  testDifferentVariables
};
