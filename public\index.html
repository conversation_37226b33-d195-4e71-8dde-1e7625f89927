<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Facebook API 测试工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4267B2, #365899);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .content {
            padding: 30px;
        }
        
        .section {
            margin-bottom: 40px;
            padding: 25px;
            border: 2px solid #f0f2f5;
            border-radius: 10px;
            background: #fafbfc;
        }
        
        .section h2 {
            color: #4267B2;
            margin-bottom: 20px;
            font-size: 1.5em;
            border-bottom: 2px solid #4267B2;
            padding-bottom: 10px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        
        input, textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        
        input:focus, textarea:focus {
            outline: none;
            border-color: #4267B2;
            box-shadow: 0 0 0 3px rgba(66, 103, 178, 0.1);
        }
        
        textarea {
            resize: vertical;
            min-height: 100px;
        }
        
        .btn {
            background: linear-gradient(135deg, #4267B2, #365899);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(66, 103, 178, 0.3);
        }
        
        .btn:active {
            transform: translateY(0);
        }
        
        .btn-secondary {
            background: linear-gradient(135deg, #42b883, #369870);
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
        }
        
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .result.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-online {
            background: #28a745;
        }
        
        .status-offline {
            background: #dc3545;
        }
        
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .loading {
            display: none;
            margin-left: 10px;
        }
        
        .spinner {
            border: 2px solid #f3f3f3;
            border-top: 2px solid #4267B2;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
            display: inline-block;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Facebook API 测试工具</h1>
            <p>测试和管理您的Facebook页面发布功能</p>
        </div>
        
        <div class="content">
            <!-- 服务器状态 -->
            <div class="section">
                <h2>📊 服务器状态</h2>
                <button class="btn" onclick="checkStatus()">
                    检查状态
                    <div class="loading" id="statusLoading">
                        <div class="spinner"></div>
                    </div>
                </button>
                <div id="statusResult" class="result" style="display: none;"></div>
            </div>
            
            <div class="grid">
                <!-- Token管理 -->
                <div class="section">
                    <h2>🔑 Token 管理</h2>
                    
                    <div class="form-group">
                        <label for="shortToken">短期用户Token:</label>
                        <input type="text" id="shortToken" placeholder="从Facebook Graph API Explorer获取">
                        <p style="margin-top: 5px; font-size: 14px; color: #666;">
                            不知道如何获取？<a href="/token-guide" target="_blank" style="color: #4267B2;">查看获取指南</a>
                        </p>
                    </div>
                    
                    <button class="btn" onclick="getLongTermToken()">
                        获取长期Token
                        <div class="loading" id="longTokenLoading">
                            <div class="spinner"></div>
                        </div>
                    </button>
                    
                    <div id="longTokenResult" class="result" style="display: none;"></div>
                    
                    <div class="form-group" style="margin-top: 20px;">
                        <label for="userToken">长期用户Token:</label>
                        <input type="text" id="userToken" placeholder="从上面获取或手动输入">
                    </div>
                    
                    <button class="btn btn-secondary" onclick="getPageToken()">
                        获取页面Token
                        <div class="loading" id="pageTokenLoading">
                            <div class="spinner"></div>
                        </div>
                    </button>
                    
                    <div id="pageTokenResult" class="result" style="display: none;"></div>
                </div>
                
                <!-- 发布文章 -->
                <div class="section">
                    <h2>📝 发布文章</h2>
                    
                    <div class="form-group">
                        <label for="postMessage">文章内容:</label>
                        <textarea id="postMessage" placeholder="输入您要发布的文章内容..."></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="postLink">链接 (可选):</label>
                        <input type="url" id="postLink" placeholder="https://example.com">
                    </div>
                    
                    <button class="btn" onclick="publishPost()">
                        发布文章
                        <div class="loading" id="postLoading">
                            <div class="spinner"></div>
                        </div>
                    </button>
                    
                    <button class="btn btn-secondary" onclick="fillTestContent()">填充测试内容</button>
                    
                    <div id="postResult" class="result" style="display: none;"></div>
                </div>
            </div>
            
            <!-- Webhook测试 -->
            <div class="section">
                <h2>🔗 Webhook 信息</h2>
                <p><strong>Webhook URL:</strong> <code>http://localhost:3002/webhook</code></p>
                <p><strong>验证Token:</strong> <code>jianglai</code></p>
                <p><strong>支持的HTTP方法:</strong></p>
                <ul style="margin-left: 20px; margin-top: 10px;">
                    <li><code>GET</code> - 用于Facebook验证</li>
                    <li><code>POST</code> - 用于接收事件</li>
                </ul>
            </div>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>
