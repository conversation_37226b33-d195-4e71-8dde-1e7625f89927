// 获取当前工作区的有效用户列表
require('dotenv').config();
const axios = require('axios');

async function getValidUsers() {
  console.log('👥 获取当前工作区的有效用户列表');
  console.log('=' .repeat(60));

  try {
    // 1. 首先测试当前Bot的身份信息
    console.log('1️⃣ 测试Bot身份信息...');
    const authResponse = await axios.post('https://slack.com/api/auth.test', {}, {
      headers: {
        'Authorization': `Bearer ${process.env.SLACK_BOT_TOKEN}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('📥 Bot身份验证响应:');
    console.log('   成功:', authResponse.data.ok);
    
    if (authResponse.data.ok) {
      console.log('   Bot ID:', authResponse.data.bot_id);
      console.log('   用户ID:', authResponse.data.user_id);
      console.log('   团队ID:', authResponse.data.team_id);
      console.log('   团队名:', authResponse.data.team);
      console.log('   用户名:', authResponse.data.user);
    } else {
      console.log('   错误:', authResponse.data.error);
      return;
    }

    // 2. 尝试获取用户列表
    console.log('\n2️⃣ 尝试获取用户列表...');
    const usersResponse = await axios.post('https://slack.com/api/users.list', {
      limit: 10
    }, {
      headers: {
        'Authorization': `Bearer ${process.env.SLACK_BOT_TOKEN}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('📥 用户列表响应:');
    console.log('   成功:', usersResponse.data.ok);
    
    if (usersResponse.data.ok) {
      console.log('   用户总数:', usersResponse.data.members.length);
      console.log('\n📋 前5个用户信息:');
      
      usersResponse.data.members.slice(0, 5).forEach((user, index) => {
        console.log(`   ${index + 1}. ${user.name} (${user.id})`);
        console.log(`      真实姓名: ${user.profile?.real_name || 'N/A'}`);
        console.log(`      显示名: ${user.profile?.display_name || 'N/A'}`);
        console.log(`      是否Bot: ${user.is_bot}`);
        console.log(`      是否删除: ${user.deleted}`);
        console.log('');
      });

      // 找到一个非Bot的活跃用户进行测试
      const activeUser = usersResponse.data.members.find(user => 
        !user.is_bot && !user.deleted && user.id !== authResponse.data.user_id
      );

      if (activeUser) {
        console.log('🎯 找到可用于测试的用户:');
        console.log('   用户ID:', activeUser.id);
        console.log('   用户名:', activeUser.name);
        console.log('   真实姓名:', activeUser.profile?.real_name || 'N/A');

        // 3. 测试与该用户打开私聊通道
        console.log('\n3️⃣ 测试与该用户打开私聊通道...');
        const conversationResponse = await axios.post('https://slack.com/api/conversations.open', {
          users: activeUser.id
        }, {
          headers: {
            'Authorization': `Bearer ${process.env.SLACK_BOT_TOKEN}`,
            'Content-Type': 'application/json'
          }
        });

        console.log('📥 conversations.open 响应:');
        console.log('   成功:', conversationResponse.data.ok);
        
        if (conversationResponse.data.ok) {
          console.log('   频道ID:', conversationResponse.data.channel.id);
          console.log('   ✅ 私聊通道打开成功！');
          
          // 4. 测试发送消息
          console.log('\n4️⃣ 测试发送消息...');
          const messageResponse = await axios.post('https://slack.com/api/chat.postMessage', {
            channel: conversationResponse.data.channel.id,
            text: '🧪 这是一条测试消息，用于验证私聊功能是否正常工作。'
          }, {
            headers: {
              'Authorization': `Bearer ${process.env.SLACK_BOT_TOKEN}`,
              'Content-Type': 'application/json'
            }
          });

          console.log('📥 消息发送响应:');
          console.log('   成功:', messageResponse.data.ok);
          
          if (messageResponse.data.ok) {
            console.log('   消息时间戳:', messageResponse.data.ts);
            console.log('   ✅ 消息发送成功！');
          } else {
            console.log('   错误:', messageResponse.data.error);
            if (messageResponse.data.needed) {
              console.log('   需要权限:', messageResponse.data.needed);
            }
          }

        } else {
          console.log('   错误:', conversationResponse.data.error);
          if (conversationResponse.data.needed) {
            console.log('   需要权限:', conversationResponse.data.needed);
          }
          if (conversationResponse.data.provided) {
            console.log('   当前权限:', conversationResponse.data.provided);
          }
        }
      } else {
        console.log('⚠️ 未找到可用于测试的非Bot用户');
      }

    } else {
      console.log('   错误:', usersResponse.data.error);
      if (usersResponse.data.needed) {
        console.log('   需要权限:', usersResponse.data.needed);
      }
      if (usersResponse.data.provided) {
        console.log('   当前权限:', usersResponse.data.provided);
      }
    }

  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error.message);
    if (error.response) {
      console.log('   HTTP状态:', error.response.status);
      console.log('   响应数据:', error.response.data);
    }
  }

  console.log('\n📊 诊断总结');
  console.log('=' .repeat(60));
  console.log('🔍 问题分析:');
  console.log('   1. 如果 users.list 失败 → 需要 users:read 权限');
  console.log('   2. 如果 conversations.open 失败 → 需要 im:write 权限');
  console.log('   3. 如果 chat.postMessage 失败 → 需要 chat:write 权限');
  console.log('');
  console.log('💡 解决方案:');
  console.log('   1. 在 Slack App 设置中添加以下权限:');
  console.log('      - users:read (读取用户信息)');
  console.log('      - im:write (发送私信)');
  console.log('      - chat:write (发送消息)');
  console.log('   2. 重新安装 App 到工作区');
  console.log('   3. 重新运行此测试验证权限');
  console.log('');
  console.log('🎯 权限配置步骤:');
  console.log('   1. 访问 https://api.slack.com/apps');
  console.log('   2. 选择您的 App');
  console.log('   3. 进入 "OAuth & Permissions"');
  console.log('   4. 在 "Bot Token Scopes" 中添加上述权限');
  console.log('   5. 点击 "Reinstall App"');
}

// 运行测试
getValidUsers();
