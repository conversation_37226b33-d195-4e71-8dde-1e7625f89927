import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Layout } from './components/Layout';
import { Dashboard } from './components/Dashboard';
import { ConversationsList } from './components/ConversationsList';
import { ConversationDetail } from './components/ConversationDetail';
import { UsersList } from './components/UsersList';
import { SearchResults } from './components/SearchResults';
import { KnowledgeManagement } from './components/KnowledgeManagement';
import { LayoutTest } from './components/LayoutTest';
import './index.css';

function App() {
  return (
    <Router>
      <Layout>
        <Routes>
          <Route path="/" element={<Navigate to="/dashboard" replace />} />
          <Route path="/dashboard" element={<Dashboard />} />
          <Route path="/conversations" element={<ConversationsList />} />
          <Route path="/conversations/:id" element={<ConversationDetail />} />
          <Route path="/users" element={<UsersList />} />
          <Route path="/search" element={<SearchResults />} />
          <Route path="/knowledge" element={<KnowledgeManagement />} />
          <Route path="/layout-test" element={<LayoutTest />} />
        </Routes>
      </Layout>
    </Router>
  );
}

export default App;
