// Webhook日志记录数据库管理
const sqlite3 = require('sqlite3').verbose();
const path = require('path');

class WebhookLogger {
  constructor() {
    // 数据库文件路径
    this.dbPath = path.join(__dirname, '../../data/webhook_logs.db');
    this.db = null;
    this.init();
  }

  // 初始化数据库
  async init() {
    return new Promise((resolve, reject) => {
      // 确保数据目录存在
      const fs = require('fs');
      const dataDir = path.dirname(this.dbPath);
      if (!fs.existsSync(dataDir)) {
        fs.mkdirSync(dataDir, { recursive: true });
      }

      this.db = new sqlite3.Database(this.dbPath, (err) => {
        if (err) {
          console.error('❌ Webhook日志数据库连接失败:', err.message);
          reject(err);
        } else {
          console.log('✅ Webhook日志数据库连接成功:', this.dbPath);
          this.createTables().then(resolve).catch(reject);
        }
      });
    });
  }

  // 创建表结构
  async createTables() {
    return new Promise((resolve, reject) => {
      const createWebhookLogsTable = `
        CREATE TABLE IF NOT EXISTS webhook_logs (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          webhook_type TEXT NOT NULL,
          raw_payload TEXT NOT NULL,
          headers TEXT,
          timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
          processed BOOLEAN DEFAULT FALSE,
          processing_error TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `;

      const createUsersTable = `
        CREATE TABLE IF NOT EXISTS users (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          platform_user_id TEXT NOT NULL,
          platform TEXT NOT NULL,
          display_name TEXT,
          phone_number TEXT,
          profile_data TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          UNIQUE(platform_user_id, platform)
        )
      `;

      const createConversationsTable = `
        CREATE TABLE IF NOT EXISTS conversations (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          user_id INTEGER,
          platform TEXT NOT NULL,
          channel_id TEXT,
          conversation_key TEXT UNIQUE,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (user_id) REFERENCES users (id)
        )
      `;

      const createMessagesTable = `
        CREATE TABLE IF NOT EXISTS messages (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          conversation_id INTEGER,
          webhook_log_id INTEGER,
          message_type TEXT NOT NULL,
          content TEXT NOT NULL,
          sender_id TEXT,
          recipient_id TEXT,
          message_sid TEXT,
          timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
          metadata TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (conversation_id) REFERENCES conversations (id),
          FOREIGN KEY (webhook_log_id) REFERENCES webhook_logs (id)
        )
      `;

      // 创建索引
      const createIndexes = [
        'CREATE INDEX IF NOT EXISTS idx_webhook_logs_type_timestamp ON webhook_logs(webhook_type, timestamp)',
        'CREATE INDEX IF NOT EXISTS idx_users_platform_user_id ON users(platform_user_id, platform)',
        'CREATE INDEX IF NOT EXISTS idx_conversations_user_platform ON conversations(user_id, platform)',
        'CREATE INDEX IF NOT EXISTS idx_messages_conversation_timestamp ON messages(conversation_id, timestamp)',
        'CREATE INDEX IF NOT EXISTS idx_messages_type_timestamp ON messages(message_type, timestamp)'
      ];

      this.db.serialize(() => {
        this.db.run(createWebhookLogsTable, (err) => {
          if (err) {
            console.error('❌ 创建webhook_logs表失败:', err.message);
            reject(err);
            return;
          }
          console.log('✅ webhook_logs表创建成功');
        });

        this.db.run(createUsersTable, (err) => {
          if (err) {
            console.error('❌ 创建users表失败:', err.message);
            reject(err);
            return;
          }
          console.log('✅ users表创建成功');
        });

        this.db.run(createConversationsTable, (err) => {
          if (err) {
            console.error('❌ 创建conversations表失败:', err.message);
            reject(err);
            return;
          }
          console.log('✅ conversations表创建成功');
        });

        this.db.run(createMessagesTable, (err) => {
          if (err) {
            console.error('❌ 创建messages表失败:', err.message);
            reject(err);
            return;
          }
          console.log('✅ messages表创建成功');

          // 创建索引
          let indexCount = 0;
          createIndexes.forEach((indexSql, index) => {
            this.db.run(indexSql, (err) => {
              if (err) {
                console.error(`❌ 创建索引${index + 1}失败:`, err.message);
              } else {
                console.log(`✅ 索引${index + 1}创建成功`);
              }
              indexCount++;
              if (indexCount === createIndexes.length) {
                resolve();
              }
            });
          });
        });
      });
    });
  }

  // 记录webhook请求
  async logWebhookRequest(webhookType, payload, headers = {}) {
    return new Promise((resolve, reject) => {
      const sql = `
        INSERT INTO webhook_logs (webhook_type, raw_payload, headers, timestamp)
        VALUES (?, ?, ?, CURRENT_TIMESTAMP)
      `;

      this.db.run(sql, [
        webhookType,
        JSON.stringify(payload),
        JSON.stringify(headers)
      ], function(err) {
        if (err) {
          console.error('❌ 记录webhook请求失败:', err.message);
          reject(err);
        } else {
          console.log(`✅ Webhook请求记录成功 - ID: ${this.lastID}, 类型: ${webhookType}`);
          resolve({ id: this.lastID, webhookType });
        }
      });
    });
  }

  // 标记webhook为已处理
  async markWebhookProcessed(webhookLogId, error = null) {
    return new Promise((resolve, reject) => {
      const sql = `
        UPDATE webhook_logs 
        SET processed = ?, processing_error = ?
        WHERE id = ?
      `;

      this.db.run(sql, [
        error ? false : true,
        error ? error.toString() : null,
        webhookLogId
      ], function(err) {
        if (err) {
          console.error('❌ 更新webhook处理状态失败:', err.message);
          reject(err);
        } else {
          console.log(`✅ Webhook处理状态更新成功 - ID: ${webhookLogId}`);
          resolve({ id: webhookLogId, processed: !error });
        }
      });
    });
  }

  // 创建或获取用户
  async createOrGetUser(platformUserId, platform, displayName = null, phoneNumber = null, profileData = {}) {
    return new Promise((resolve, reject) => {
      // 首先尝试获取现有用户
      const selectSql = `
        SELECT * FROM users 
        WHERE platform_user_id = ? AND platform = ?
      `;

      this.db.get(selectSql, [platformUserId, platform], (err, row) => {
        if (err) {
          console.error('❌ 查询用户失败:', err.message);
          reject(err);
          return;
        }

        if (row) {
          // 用户已存在，更新信息
          const updateSql = `
            UPDATE users 
            SET display_name = COALESCE(?, display_name),
                phone_number = COALESCE(?, phone_number),
                profile_data = ?,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
          `;

          this.db.run(updateSql, [
            displayName,
            phoneNumber,
            JSON.stringify(profileData),
            row.id
          ], function(err) {
            if (err) {
              console.error('❌ 更新用户信息失败:', err.message);
              reject(err);
            } else {
              console.log(`✅ 用户信息更新成功 - ID: ${row.id}`);
              resolve({ ...row, display_name: displayName || row.display_name });
            }
          });
        } else {
          // 创建新用户
          const insertSql = `
            INSERT INTO users (platform_user_id, platform, display_name, phone_number, profile_data)
            VALUES (?, ?, ?, ?, ?)
          `;

          this.db.run(insertSql, [
            platformUserId,
            platform,
            displayName,
            phoneNumber,
            JSON.stringify(profileData)
          ], function(err) {
            if (err) {
              console.error('❌ 创建用户失败:', err.message);
              reject(err);
            } else {
              console.log(`✅ 用户创建成功 - ID: ${this.lastID}`);
              resolve({
                id: this.lastID,
                platform_user_id: platformUserId,
                platform,
                display_name: displayName,
                phone_number: phoneNumber
              });
            }
          });
        }
      });
    });
  }

  // 创建或获取对话
  async createOrGetConversation(userId, platform, channelId = null) {
    return new Promise((resolve, reject) => {
      const conversationKey = `${platform}_${userId}_${channelId || 'default'}`;
      
      // 首先尝试获取现有对话
      const selectSql = `
        SELECT * FROM conversations 
        WHERE conversation_key = ?
      `;

      this.db.get(selectSql, [conversationKey], (err, row) => {
        if (err) {
          console.error('❌ 查询对话失败:', err.message);
          reject(err);
          return;
        }

        if (row) {
          // 对话已存在，更新时间
          const updateSql = `
            UPDATE conversations 
            SET updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
          `;

          this.db.run(updateSql, [row.id], function(err) {
            if (err) {
              console.error('❌ 更新对话时间失败:', err.message);
              reject(err);
            } else {
              console.log(`✅ 对话时间更新成功 - ID: ${row.id}`);
              resolve(row);
            }
          });
        } else {
          // 创建新对话
          const insertSql = `
            INSERT INTO conversations (user_id, platform, channel_id, conversation_key)
            VALUES (?, ?, ?, ?)
          `;

          this.db.run(insertSql, [
            userId,
            platform,
            channelId,
            conversationKey
          ], function(err) {
            if (err) {
              console.error('❌ 创建对话失败:', err.message);
              reject(err);
            } else {
              console.log(`✅ 对话创建成功 - ID: ${this.lastID}`);
              resolve({
                id: this.lastID,
                user_id: userId,
                platform,
                channel_id: channelId,
                conversation_key: conversationKey
              });
            }
          });
        }
      });
    });
  }

  // 关闭数据库连接
  close() {
    if (this.db) {
      this.db.close((err) => {
        if (err) {
          console.error('❌ 关闭webhook日志数据库连接失败:', err.message);
        } else {
          console.log('✅ Webhook日志数据库连接已关闭');
        }
      });
    }
  }
}

module.exports = WebhookLogger;
