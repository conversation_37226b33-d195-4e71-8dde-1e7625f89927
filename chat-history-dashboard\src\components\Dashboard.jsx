import React, { useState, useEffect } from 'react';
import { 
  MessageSquare, 
  Users, 
  TrendingUp, 
  Activity,
  Phone,
  Hash,
  MessageCircle
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Badge } from './ui/badge';
import { apiService } from '../services/api';

export function Dashboard() {
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    loadStatistics();
  }, []);

  const loadStatistics = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiService.getStatistics({ days: 30 });
      setStats(response.data);
    } catch (err) {
      setError(err.message);
      console.error('加载统计信息失败:', err);
    } finally {
      setLoading(false);
    }
  };

  const getPlatformIcon = (platform) => {
    switch (platform) {
      case 'whatsapp':
        return <Phone className="h-5 w-5" />;
      case 'slack':
        return <Hash className="h-5 w-5" />;
      case 'line':
        return <MessageCircle className="h-5 w-5" />;
      default:
        return <MessageSquare className="h-5 w-5" />;
    }
  };

  const getPlatformColor = (platform) => {
    switch (platform) {
      case 'whatsapp':
        return 'text-green-600 bg-green-100';
      case 'slack':
        return 'text-purple-600 bg-purple-100';
      case 'line':
        return 'text-green-500 bg-green-50';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <h1 className="text-3xl font-bold text-gray-900">仪表板</h1>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader className="pb-2">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-full"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <h1 className="text-3xl font-bold text-gray-900">仪表板</h1>
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2 text-red-600">
              <Activity className="h-5 w-5" />
              <span>加载统计信息失败: {error}</span>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  const { general, platforms, daily } = stats || {};

  // 计算最近7天的消息趋势
  const recentDays = daily?.slice(0, 7) || [];
  const totalRecentMessages = recentDays.reduce((sum, day) => sum + (day.totalMessages || 0), 0);
  const avgDailyMessages = recentDays.length > 0 ? Math.round(totalRecentMessages / recentDays.length) : 0;

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">仪表板</h1>
        <Badge variant="outline" className="text-sm">
          最近30天数据
        </Badge>
      </div>

      {/* 总体统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总用户数</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{general?.totalUsers || 0}</div>
            <p className="text-xs text-muted-foreground">
              跨所有平台的用户
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总对话数</CardTitle>
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{general?.totalConversations || 0}</div>
            <p className="text-xs text-muted-foreground">
              活跃对话会话
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总消息数</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{general?.totalMessages || 0}</div>
            <p className="text-xs text-muted-foreground">
              收发消息总数
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">日均消息</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{avgDailyMessages}</div>
            <p className="text-xs text-muted-foreground">
              最近7天平均
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 平台统计 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>平台分布</CardTitle>
            <CardDescription>各平台的用户和消息统计</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {platforms?.map((platform) => (
                <div key={platform.platform} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 rounded-lg ${getPlatformColor(platform.platform)}`}>
                      {getPlatformIcon(platform.platform)}
                    </div>
                    <div>
                      <p className="font-medium capitalize">{platform.platform}</p>
                      <p className="text-sm text-muted-foreground">
                        {platform.userCount || 0} 用户 · {platform.conversationCount || 0} 对话
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold">{platform.messageCount || 0}</p>
                    <p className="text-sm text-muted-foreground">消息</p>
                  </div>
                </div>
              )) || (
                <p className="text-center text-muted-foreground py-4">暂无平台数据</p>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>消息类型分布</CardTitle>
            <CardDescription>收发消息比例统计</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <span>接收消息</span>
                </div>
                <span className="font-semibold">{general?.incomingMessages || 0}</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span>发送消息</span>
                </div>
                <span className="font-semibold">{general?.outgoingMessages || 0}</span>
              </div>
              <div className="pt-2 border-t">
                <div className="flex items-center justify-between text-sm text-muted-foreground">
                  <span>响应率</span>
                  <span>
                    {general?.incomingMessages > 0 
                      ? Math.round((general.outgoingMessages / general.incomingMessages) * 100) 
                      : 0}%
                  </span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 最近活动 */}
      <Card>
        <CardHeader>
          <CardTitle>最近7天活动</CardTitle>
          <CardDescription>每日消息统计趋势</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {recentDays.map((day, index) => (
              <div key={day.date} className="flex items-center justify-between py-2">
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span className="text-sm font-medium">
                    {new Date(day.date).toLocaleDateString('zh-CN', { 
                      month: 'short', 
                      day: 'numeric' 
                    })}
                  </span>
                </div>
                <div className="flex items-center space-x-4 text-sm">
                  <span className="text-muted-foreground">
                    {day.totalMessages || 0} 消息
                  </span>
                  <span className="text-muted-foreground">
                    {day.activeConversations || 0} 活跃对话
                  </span>
                </div>
              </div>
            )) || (
              <p className="text-center text-muted-foreground py-4">暂无最近活动数据</p>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
