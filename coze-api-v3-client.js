// Coze API v3 客户端 - 基于官方文档
const axios = require('axios');

class CozeApiV3Client {
  constructor() {
    this.botId = '7528309468237529127';
    this.accessToken = 'pat_FSEGBGcfbYwabmxELRPZYAReTrVaWIMMPBwlyIfUeXnqaJsBTcbbIZrpNyEAZwLR';
    this.baseUrl = 'https://api.coze.cn';
    this.conversations = {};
    this.chatHistories = {}; // 存储每个用户的聊天历史
  }

  // 创建请求头
  getHeaders() {
    return {
      'Authorization': `Bearer ${this.accessToken}`,
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    };
  }

  // 发送聊天消息 - 非流式
  async sendMessage(message, userId, conversationId = null, mediaInfo = null) {
    try {
      // 如果没有提供会话ID，尝试从缓存获取
      if (!conversationId) {
        conversationId = this.getUserConversationId(userId);
      }

      console.log(`🤖 发送消息到Coze v3 (用户: ${userId}):`);
      console.log(`   消息: ${message}`);
      console.log(`   会话ID: ${conversationId || 'New conversation'}`);

      const url = `${this.baseUrl}/v3/chat`;

      // 获取用户的聊天历史
      const chatHistory = this.getChatHistory(userId);

      // 构建当前消息 - 根据Coze API文档要求
      let currentMessage;
      if (mediaInfo && mediaInfo.numMedia > 0 && mediaInfo.mediaType && mediaInfo.mediaType.startsWith('image/')) {
        console.log('📸 处理图片消息');

        if (message && message.trim()) {
          // 图片+文本消息：使用object_string数组格式
          console.log('📝 图片+文本组合消息');
          currentMessage = {
            role: "user",
            content: JSON.stringify([
              {
                type: "text",
                text: message
              },
              {
                type: "image",
                file_url: mediaInfo.mediaUrl
              }
            ]),
            content_type: "object_string"
          };
        } else {
          // 纯图片消息：需要确保前后有文本消息作为上下文
          console.log('🖼️ 纯图片消息');

          // 检查聊天历史中是否有最近的文本消息
          const hasRecentTextMessage = this.hasRecentTextMessage(userId);

          if (!hasRecentTextMessage) {
            // 如果没有最近的文本消息，先添加一个上下文消息
            console.log('➕ 添加上下文文本消息');
            const contextMessage = {
              role: "user",
              content: "请分析这张图片",
              content_type: "text"
            };

            // 更新聊天历史
            this.chatHistories[userId] = this.chatHistories[userId] || [];
            this.chatHistories[userId].push(contextMessage);

            // 添加到消息列表
            chatHistory.push(contextMessage);
          }

          // 纯图片消息
          currentMessage = {
            role: "user",
            content: JSON.stringify([
              {
                type: "image",
                file_url: mediaInfo.mediaUrl
              }
            ]),
            content_type: "object_string"
          };
        }
      } else {
        // 纯文本消息
        currentMessage = {
          role: "user",
          content: message,
          content_type: "text"
        };
      }

      // 构建消息列表，包含历史记录和当前消息
      const messages = [...chatHistory, currentMessage];

      const payload = {
        bot_id: this.botId,
        user_id: userId,
        stream: false,
        auto_save_history: true,
        additional_messages: messages
      };

      // 如果有会话ID，添加到请求中
      if (conversationId) {
        payload.conversation_id = conversationId;
      }

      console.log(`📚 包含聊天历史: ${chatHistory.length} 条记录`);
      console.log(`📝 总消息数: ${messages.length} 条`);

      console.log('📤 发送请求:', JSON.stringify(payload, null, 2));

      const response = await axios.post(url, payload, {
        headers: this.getHeaders(),
        timeout: 30000
      });

      console.log('📥 收到响应:', JSON.stringify(response.data, null, 2));

      // 检查是否需要轮询结果
      if (response.data.data && response.data.data.status === 'in_progress') {
        const chatId = response.data.data.id;
        const conversationId = response.data.data.conversation_id;

        console.log(`⏳ 等待Coze处理完成 (Chat ID: ${chatId})...`);

        // 轮询获取最终结果
        const finalResult = await this.pollChatResult(chatId, conversationId, userId, message, mediaInfo);

        // 保存会话ID
        if (conversationId) {
          this.conversations[userId] = conversationId;
          console.log(`   会话ID已保存: ${conversationId}`);
        }

        return finalResult;
      }

      // 如果是同步响应，直接处理
      if (response.data.conversation_id) {
        this.conversations[userId] = response.data.conversation_id;
        console.log(`   会话ID已保存: ${response.data.conversation_id}`);
      }

      // 提取回复内容
      const replyText = this.extractReply(response.data);

      // 更新聊天历史
      this.updateChatHistory(userId, message, replyText, mediaInfo);

      return {
        text: replyText,
        conversation_id: response.data.conversation_id,
        chat_id: response.data.id,
        success: true
      };

    } catch (error) {
      console.error('❌ Coze API v3调用失败:', error.message);
      if (error.response) {
        console.error('   状态码:', error.response.status);
        console.error('   响应数据:', JSON.stringify(error.response.data, null, 2));
      }
      
      return {
        text: `很抱歉，我现在无法处理您的消息。请稍后再试。(错误: ${error.message})`,
        conversation_id: conversationId,
        success: false
      };
    }
  }

  // 轮询获取聊天结果
  async pollChatResult(chatId, conversationId, userId, userMessage, mediaInfo = null, maxAttempts = 30, interval = 2000) {
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        console.log(`   轮询第 ${attempt} 次...`);

        const url = `${this.baseUrl}/v3/chat/retrieve`;
        const params = {
          chat_id: chatId,
          conversation_id: conversationId
        };

        const response = await axios.get(url, {
          headers: this.getHeaders(),
          params: params,
          timeout: 10000
        });

        console.log(`🔍 轮询响应 (第${attempt}次):`, JSON.stringify(response.data, null, 2));

        if (response.data.data && response.data.data.status === 'completed') {
          console.log('✅ Coze处理完成');

          // 获取消息列表
          const messages = await this.getChatMessages(chatId, conversationId);

          // 提取回复内容
          let replyText = '';
          if (messages && messages.data && messages.data.length > 0) {
            // 优先查找type为"answer"的助手回复
            const answerMessages = messages.data
              .filter(msg => msg.role === 'assistant' && msg.type === 'answer' && msg.content);

            if (answerMessages.length > 0) {
              replyText = answerMessages[0].content;
            } else {
              // 如果没有answer类型，查找普通的助手回复
              const assistantMessages = messages.data
                .filter(msg => msg.role === 'assistant' && msg.content && msg.type !== 'verbose')
                .reverse();

              if (assistantMessages.length > 0) {
                replyText = assistantMessages[0].content;
              }
            }
          }

          if (!replyText) {
            replyText = '我收到了您的消息，但暂时无法生成合适的回复。';
          }

          console.log(`✅ 最终回复: ${replyText.substring(0, 50)}...`);

          // 更新聊天历史
          this.updateChatHistory(userId, userMessage, replyText, mediaInfo);

          return {
            text: replyText,
            conversation_id: conversationId,
            chat_id: chatId,
            success: true
          };
        }

        if (response.data.data && response.data.data.status === 'failed') {
          const errorMsg = response.data.data.last_error?.msg || 'Unknown error';
          console.log(`❌ Coze处理失败: ${errorMsg}`);
          // 立即抛出错误，停止轮询
          throw new Error(`Coze处理失败: ${errorMsg}`);
        }

        // 如果还在处理中，等待后继续轮询
        if (attempt < maxAttempts) {
          await new Promise(resolve => setTimeout(resolve, interval));
        }

      } catch (error) {
        // 如果是Coze处理失败的错误，立即抛出，不再重试
        if (error.message && error.message.includes('Coze处理失败')) {
          throw error;
        }

        // 其他网络错误等，可以重试
        if (attempt === maxAttempts) {
          throw error;
        }
        console.log(`   轮询第 ${attempt} 次网络错误，继续尝试...`);
        await new Promise(resolve => setTimeout(resolve, interval));
      }
    }

    throw new Error(`轮询超时: 在 ${maxAttempts} 次尝试后仍未获得结果`);
  }

  // 获取聊天消息列表
  async getChatMessages(chatId, conversationId) {
    try {
      const url = `${this.baseUrl}/v3/chat/message/list`;
      const params = {
        chat_id: chatId,
        conversation_id: conversationId
      };

      const response = await axios.get(url, {
        headers: this.getHeaders(),
        params: params,
        timeout: 10000
      });

      return response.data;

    } catch (error) {
      console.error('❌ 获取聊天消息失败:', error.message);
      return null;
    }
  }

  // 发送聊天消息 - 流式
  async sendMessageStream(message, userId, conversationId = null) {
    try {
      console.log(`🌊 发送流式消息到Coze v3 (用户: ${userId}):`);
      console.log(`   消息: ${message}`);

      const url = `${this.baseUrl}/v3/chat`;
      const payload = {
        bot_id: this.botId,
        user_id: userId,
        stream: true,
        auto_save_history: true,
        additional_messages: [
          {
            role: "user",
            content: message,
            content_type: "text"
          }
        ]
      };

      if (conversationId) {
        payload.conversation_id = conversationId;
      }

      const response = await axios.post(url, payload, {
        headers: this.getHeaders(),
        responseType: 'stream',
        timeout: 30000
      });

      let fullResponse = '';
      let conversationIdFromStream = conversationId;

      return new Promise((resolve, reject) => {
        response.data.on('data', (chunk) => {
          const lines = chunk.toString().split('\n');
          
          for (const line of lines) {
            if (line.startsWith('data: ')) {
              try {
                const data = JSON.parse(line.slice(6));
                
                if (data.event === 'conversation.message.delta') {
                  if (data.data.content) {
                    fullResponse += data.data.content;
                    console.log('📝 流式内容:', data.data.content);
                  }
                }
                
                if (data.event === 'conversation.chat.completed') {
                  conversationIdFromStream = data.data.conversation_id;
                  console.log('✅ 流式对话完成');
                }
                
              } catch (parseError) {
                // 忽略解析错误，继续处理
              }
            }
          }
        });

        response.data.on('end', () => {
          if (conversationIdFromStream) {
            this.conversations[userId] = conversationIdFromStream;
          }
          
          resolve({
            text: fullResponse || '收到了您的消息，但暂时无法生成回复。',
            conversation_id: conversationIdFromStream,
            success: true
          });
        });

        response.data.on('error', (error) => {
          reject(error);
        });
      });

    } catch (error) {
      console.error('❌ Coze API v3流式调用失败:', error.message);
      return {
        text: `很抱歉，我现在无法处理您的消息。请稍后再试。`,
        conversation_id: conversationId,
        success: false
      };
    }
  }

  // 获取聊天历史
  async getChatHistory(chatId, conversationId) {
    try {
      console.log(`📚 获取聊天历史 (Chat ID: ${chatId})`);

      const url = `${this.baseUrl}/v3/chat/message/list`;
      const params = {
        chat_id: chatId,
        conversation_id: conversationId
      };

      const response = await axios.get(url, {
        headers: this.getHeaders(),
        params: params,
        timeout: 10000
      });

      console.log('📖 聊天历史:', JSON.stringify(response.data, null, 2));
      return response.data;

    } catch (error) {
      console.error('❌ 获取聊天历史失败:', error.message);
      throw error;
    }
  }

  // 提取回复内容
  extractReply(responseData) {
    try {
      // 从messages中提取助手的回复
      if (responseData.messages && responseData.messages.length > 0) {
        // 查找最后一条助手消息
        const assistantMessages = responseData.messages
          .filter(msg => msg.role === 'assistant' && msg.content)
          .reverse();
        
        if (assistantMessages.length > 0) {
          const reply = assistantMessages[0].content;
          console.log(`✅ 提取回复: ${reply.substring(0, 50)}...`);
          return reply;
        }
      }

      // 如果没有找到回复，返回默认消息
      console.log('⚠️ 未找到有效回复，使用默认消息');
      return '我收到了您的消息，正在思考如何回复...';

    } catch (error) {
      console.error('❌ 提取回复失败:', error.message);
      return '很抱歉，我现在无法正确处理您的消息。';
    }
  }

  // 获取用户会话ID
  getUserConversationId(userId) {
    return this.conversations[userId] || null;
  }

  // 设置用户会话ID
  setUserConversationId(userId, conversationId) {
    this.conversations[userId] = conversationId;
    return conversationId;
  }

  // 清除用户会话ID
  clearUserConversationId(userId) {
    delete this.conversations[userId];
    delete this.chatHistories[userId]; // 同时清除聊天历史
    return null;
  }

  // 获取用户聊天历史
  getChatHistory(userId) {
    if (!this.chatHistories[userId]) {
      this.chatHistories[userId] = [];
    }
    return this.chatHistories[userId];
  }

  // 更新用户聊天历史
  updateChatHistory(userId, userMessage, botReply, mediaInfo = null) {
    if (!this.chatHistories[userId]) {
      this.chatHistories[userId] = [];
    }

    // 添加用户消息 - 根据新的API格式
    let userMessageObj;
    if (mediaInfo && mediaInfo.numMedia > 0 && mediaInfo.mediaType && mediaInfo.mediaType.startsWith('image/')) {
      if (userMessage && userMessage.trim()) {
        // 图片+文本消息
        userMessageObj = {
          role: "user",
          content: JSON.stringify([
            {
              type: "text",
              text: userMessage
            },
            {
              type: "image",
              file_url: mediaInfo.mediaUrl
            }
          ]),
          content_type: "object_string"
        };
      } else {
        // 纯图片消息
        userMessageObj = {
          role: "user",
          content: JSON.stringify([
            {
              type: "image",
              file_url: mediaInfo.mediaUrl
            }
          ]),
          content_type: "object_string"
        };
      }
    } else {
      // 纯文本消息
      userMessageObj = {
        role: "user",
        content: userMessage,
        content_type: "text"
      };
    }

    this.chatHistories[userId].push(userMessageObj);

    // 添加机器人回复
    this.chatHistories[userId].push({
      role: "assistant",
      content: botReply,
      content_type: "text"
    });

    // 限制历史记录长度，保留最近的20条消息（10轮对话）
    const maxMessages = 20;
    if (this.chatHistories[userId].length > maxMessages) {
      this.chatHistories[userId] = this.chatHistories[userId].slice(-maxMessages);
    }

    console.log(`📚 更新聊天历史 (用户: ${userId}): ${this.chatHistories[userId].length} 条记录`);
  }

  // 获取用户聊天历史摘要
  getChatHistorySummary(userId) {
    const history = this.getChatHistory(userId);
    return {
      userId: userId,
      messageCount: history.length,
      conversationRounds: Math.floor(history.length / 2),
      lastActivity: new Date().toISOString()
    };
  }

  // 检查是否有最近的文本消息（用于纯图片消息的上下文要求）
  hasRecentTextMessage(userId) {
    const history = this.getChatHistory(userId);
    if (history.length === 0) return false;

    // 检查最后几条消息中是否有文本消息
    const recentMessages = history.slice(-3); // 检查最近3条消息
    return recentMessages.some(msg =>
      msg.role === 'user' &&
      msg.content_type === 'text' &&
      typeof msg.content === 'string' &&
      msg.content.trim().length > 0
    );
  }

  // 测试连接
  async testConnection() {
    console.log('🔄 测试Coze API v3连接...');
    
    try {
      const result = await this.sendMessage('Hello, this is a test message.', 'test_user');
      
      if (result.success && result.text && result.text.length > 10) {
        console.log('✅ Coze API v3连接成功!');
        console.log(`   测试回复: ${result.text}`);
        return { success: true, reply: result.text };
      } else {
        console.log('⚠️ Coze API v3连接有问题');
        return { success: false, error: 'API响应异常' };
      }
      
    } catch (error) {
      console.error('❌ Coze API v3连接失败:', error.message);
      return { success: false, error: error.message };
    }
  }

  // 生成智能回复（主要方法）
  async generateReply(userMessage, userId, useStream = false, mediaInfo = null) {
    console.log(`💬 为用户 ${userId} 生成回复 (${useStream ? '流式' : '非流式'})...`);
    console.log(`   用户消息: ${userMessage}`);
    
    try {
      const conversationId = this.getUserConversationId(userId);
      
      let result;
      if (useStream) {
        result = await this.sendMessageStream(userMessage, userId, conversationId, mediaInfo);
      } else {
        result = await this.sendMessage(userMessage, userId, conversationId, mediaInfo);
      }
      
      // 保存会话ID
      if (result.conversation_id) {
        this.setUserConversationId(userId, result.conversation_id);
      }
      
      return result.text;
      
    } catch (error) {
      console.error('❌ 生成回复失败:', error.message);
      
      // 返回友好的错误消息
      const errorReplies = [
        '很抱歉，我现在有点忙，请稍后再试。',
        '抱歉，我需要一点时间来思考您的问题。',
        '系统暂时繁忙，请您稍等片刻。'
      ];
      
      const randomReply = errorReplies[Math.floor(Math.random() * errorReplies.length)];
      return randomReply;
    }
  }
}

// 创建全局实例
const cozeApiV3Client = new CozeApiV3Client();

module.exports = cozeApiV3Client;
