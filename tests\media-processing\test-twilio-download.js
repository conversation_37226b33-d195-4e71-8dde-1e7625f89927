// Twilio媒体文件下载和TOS上传测试脚本
const axios = require('axios');
const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const VolcengineTOSClient = require('./volcengine-tos-client');
require('dotenv').config();

// 测试配置
const TEST_CONFIG = {
  mediaUrl: 'https://api.twilio.com/2010-04-01/Accounts/AC7657552c992e2a3737961532e7e609d1/Messages/MM21994ae953abd5c3485519e155f56a7d/Media/ME5959d96509ce669c358cafa8643ee2d9',
  accountSid: process.env.TWILIO_ACCOUNT_SID,
  authToken: process.env.TWILIO_AUTH_TOKEN,
  downloadDir: path.join(__dirname, 'downloads')
};

// 创建下载目录
function ensureDownloadDir() {
  if (!fs.existsSync(TEST_CONFIG.downloadDir)) {
    fs.mkdirSync(TEST_CONFIG.downloadDir, { recursive: true });
    console.log('📁 创建下载目录:', TEST_CONFIG.downloadDir);
  }
}

// 获取文件扩展名
function getFileExtension(contentType) {
  const typeMap = {
    'image/jpeg': '.jpg',
    'image/png': '.png',
    'image/gif': '.gif',
    'image/webp': '.webp',
    'audio/mpeg': '.mp3',
    'audio/mp4': '.m4a',
    'video/mp4': '.mp4',
    'video/quicktime': '.mov',
    'application/pdf': '.pdf',
    'text/plain': '.txt'
  };
  
  return typeMap[contentType] || '.bin';
}

// 格式化文件大小
function formatFileSize(bytes) {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 主要的下载测试函数
async function testTwilioMediaDownload() {
  console.log('🧪 Twilio媒体文件下载测试');
  console.log('=' .repeat(60));
  
  // 验证环境变量
  if (!TEST_CONFIG.accountSid || !TEST_CONFIG.authToken) {
    console.error('❌ 缺少必要的环境变量:');
    console.error('   TWILIO_ACCOUNT_SID:', TEST_CONFIG.accountSid ? '✅ 已设置' : '❌ 未设置');
    console.error('   TWILIO_AUTH_TOKEN:', TEST_CONFIG.authToken ? '✅ 已设置' : '❌ 未设置');
    console.error('');
    console.error('请在.env文件中设置这些变量');
    return false;
  }
  
  console.log('📋 测试配置:');
  console.log('   Account SID:', TEST_CONFIG.accountSid);
  console.log('   Auth Token:', TEST_CONFIG.authToken.substring(0, 8) + '...');
  console.log('   媒体URL:', TEST_CONFIG.mediaUrl);
  console.log('   下载目录:', TEST_CONFIG.downloadDir);
  console.log('');
  
  try {
    console.log('📥 开始下载Twilio媒体文件...');
    const startTime = Date.now();
    
    // 使用HTTP Basic Auth下载文件
    const response = await axios.get(TEST_CONFIG.mediaUrl, {
      auth: {
        username: TEST_CONFIG.accountSid,
        password: TEST_CONFIG.authToken
      },
      responseType: 'arraybuffer',
      timeout: 30000,
      headers: {
        'User-Agent': 'WhatsApp-Bot-Test/1.0'
      }
    });
    
    const downloadTime = Date.now() - startTime;
    
    console.log('✅ 下载成功!');
    console.log('   状态码:', response.status);
    console.log('   文件大小:', formatFileSize(response.data.length));
    console.log('   内容类型:', response.headers['content-type']);
    console.log('   下载时间:', downloadTime + 'ms');
    console.log('');
    
    // 生成文件名
    const contentType = response.headers['content-type'] || 'application/octet-stream';
    const extension = getFileExtension(contentType);
    const timestamp = Date.now();
    const fileName = `twilio-media-${timestamp}${extension}`;
    const filePath = path.join(TEST_CONFIG.downloadDir, fileName);
    
    // 保存文件到本地
    console.log('💾 保存文件到本地...');
    fs.writeFileSync(filePath, response.data);
    
    // 验证文件
    const fileStats = fs.statSync(filePath);
    const fileBuffer = fs.readFileSync(filePath);
    const hash = crypto.createHash('md5').update(fileBuffer).digest('hex');
    
    console.log('✅ 文件保存成功!');
    console.log('   文件路径:', filePath);
    console.log('   文件大小:', formatFileSize(fileStats.size));
    console.log('   MD5哈希:', hash);
    console.log('   文件类型:', contentType);
    console.log('');
    
    // 验证文件完整性
    if (fileStats.size === response.data.length) {
      console.log('✅ 文件完整性验证通过');
    } else {
      console.log('❌ 文件完整性验证失败');
      console.log('   期望大小:', response.data.length);
      console.log('   实际大小:', fileStats.size);
    }
    
    return {
      success: true,
      filePath: filePath,
      fileSize: fileStats.size,
      contentType: contentType,
      hash: hash,
      downloadTime: downloadTime
    };
    
  } catch (error) {
    console.error('❌ 下载失败:', error.message);
    
    if (error.response) {
      console.error('   状态码:', error.response.status);
      console.error('   状态文本:', error.response.statusText);
      
      if (error.response.status === 401) {
        console.error('   🔐 认证失败 - 请检查Account SID和Auth Token');
      } else if (error.response.status === 404) {
        console.error('   📄 文件不存在 - 请检查媒体URL');
      } else if (error.response.status === 403) {
        console.error('   🚫 访问被拒绝 - 请检查权限');
      }
    } else if (error.code === 'ECONNABORTED') {
      console.error('   ⏰ 请求超时 - 网络连接可能有问题');
    } else if (error.code === 'ENOTFOUND') {
      console.error('   🌐 DNS解析失败 - 请检查网络连接');
    }
    
    return {
      success: false,
      error: error.message,
      statusCode: error.response?.status
    };
  }
}

// 测试认证失败情况
async function testAuthenticationFailure() {
  console.log('🧪 测试认证失败处理...');
  console.log('-'.repeat(40));
  
  try {
    await axios.get(TEST_CONFIG.mediaUrl, {
      auth: {
        username: 'invalid_sid',
        password: 'invalid_token'
      },
      responseType: 'arraybuffer',
      timeout: 10000
    });
    
    console.log('❌ 测试失败: 应该返回认证错误');
    return false;
    
  } catch (error) {
    if (error.response && error.response.status === 401) {
      console.log('✅ 正确处理认证失败');
      console.log('   状态码:', error.response.status);
      console.log('   错误信息:', error.message);
      return true;
    } else {
      console.log('⚠️ 意外的错误类型:', error.message);
      return false;
    }
  }
}

// 测试完整的媒体处理流程：下载→上传TOS→清理
async function testCompleteMediaProcessing() {
  console.log('🧪 测试完整媒体处理流程');
  console.log('-'.repeat(50));

  let localFilePath = null;
  let tosClient = null;

  try {
    // 初始化TOS客户端
    console.log('🔥 初始化TOS客户端...');
    tosClient = new VolcengineTOSClient();
    const tosInitResult = await tosClient.initialize();

    if (!tosInitResult) {
      console.log('⚠️ TOS客户端初始化失败，跳过上传步骤');
    }

    // 步骤1: 下载媒体文件
    console.log('\n📥 步骤1: 下载Twilio媒体文件...');
    const downloadResponse = await axios.get(TEST_CONFIG.mediaUrl, {
      auth: {
        username: TEST_CONFIG.accountSid,
        password: TEST_CONFIG.authToken
      },
      responseType: 'arraybuffer',
      timeout: 30000
    });

    console.log('✅ 下载成功:');
    console.log('   文件大小:', formatFileSize(downloadResponse.data.length));
    console.log('   内容类型:', downloadResponse.headers['content-type']);

    // 步骤2: 保存到本地
    console.log('\n💾 步骤2: 保存到本地临时文件...');
    const contentType = downloadResponse.headers['content-type'] || 'image/jpeg';
    const extension = getFileExtension(contentType);
    const timestamp = Date.now();
    const fileName = `complete-flow-${timestamp}${extension}`;
    localFilePath = path.join(TEST_CONFIG.downloadDir, fileName);

    fs.writeFileSync(localFilePath, downloadResponse.data);
    const fileStats = fs.statSync(localFilePath);

    console.log('✅ 本地保存成功:');
    console.log('   文件路径:', localFilePath);
    console.log('   文件大小:', formatFileSize(fileStats.size));

    // 步骤3: 上传到TOS
    let tosUploadResult = null;
    if (tosClient && tosInitResult) {
      console.log('\n☁️ 步骤3: 上传到火山引擎TOS...');

      try {
        tosUploadResult = await tosClient.processWhatsAppMedia(
          TEST_CONFIG.mediaUrl,
          TEST_CONFIG.accountSid,
          TEST_CONFIG.authToken
        );

        console.log('✅ TOS上传成功:');
        console.log('   公开URL:', tosUploadResult.publicUrl);
        console.log('   文件名:', tosUploadResult.fileName);
        console.log('   存储桶:', tosUploadResult.bucket);

      } catch (tosError) {
        console.log('⚠️ TOS上传失败:', tosError.message);
        console.log('   继续进行本地清理测试');
      }
    } else {
      console.log('\n⚠️ 跳过TOS上传步骤（客户端未初始化）');
    }

    // 步骤4: 清理本地文件
    console.log('\n🧹 步骤4: 清理本地缓存文件...');

    // 验证文件存在
    if (fs.existsSync(localFilePath)) {
      // 计算文件哈希（用于验证）
      const fileBuffer = fs.readFileSync(localFilePath);
      const hash = crypto.createHash('md5').update(fileBuffer).digest('hex');

      console.log('📋 清理前验证:');
      console.log('   文件存在:', '✅');
      console.log('   文件大小:', formatFileSize(fileBuffer.length));
      console.log('   MD5哈希:', hash);

      // 删除文件
      fs.unlinkSync(localFilePath);

      // 验证删除成功
      const fileDeleted = !fs.existsSync(localFilePath);

      console.log('✅ 本地文件清理成功:');
      console.log('   文件已删除:', fileDeleted ? '✅' : '❌');
      console.log('   清理路径:', localFilePath);

      return {
        success: true,
        downloadSuccess: true,
        uploadSuccess: tosUploadResult !== null,
        cleanupSuccess: fileDeleted,
        publicUrl: tosUploadResult?.publicUrl || null,
        fileSize: downloadResponse.data.length,
        contentType: contentType
      };

    } else {
      throw new Error('本地文件不存在，无法进行清理测试');
    }

  } catch (error) {
    console.error('❌ 完整流程测试失败:', error.message);

    // 确保清理本地文件（即使测试失败）
    if (localFilePath && fs.existsSync(localFilePath)) {
      try {
        fs.unlinkSync(localFilePath);
        console.log('🧹 已清理失败测试的本地文件');
      } catch (cleanupError) {
        console.error('⚠️ 清理失败:', cleanupError.message);
      }
    }

    return {
      success: false,
      error: error.message
    };
  }
}

// 主函数
async function main() {
  console.log('🎯 Twilio媒体下载和TOS上传测试套件');
  console.log('=' .repeat(60));

  // 确保下载目录存在
  ensureDownloadDir();

  // 执行基本下载测试
  console.log('\n🔸 基本下载测试');
  const downloadResult = await testTwilioMediaDownload();

  // 等待2秒
  await new Promise(resolve => setTimeout(resolve, 2000));

  // 测试认证失败
  console.log('\n🔸 认证失败测试');
  const authResult = await testAuthenticationFailure();

  // 等待2秒
  await new Promise(resolve => setTimeout(resolve, 2000));

  // 测试完整流程
  console.log('\n🔸 完整流程测试');
  const completeResult = await testCompleteMediaProcessing();

  // 测试总结
  console.log('\n🎊 测试总结:');
  console.log('=' .repeat(60));
  console.log('1. 基本媒体下载:', downloadResult.success ? '✅ 通过' : '❌ 失败');
  console.log('2. 认证失败处理:', authResult ? '✅ 通过' : '❌ 失败');
  console.log('3. 完整处理流程:', completeResult.success ? '✅ 通过' : '❌ 失败');

  if (completeResult.success) {
    console.log('\n📊 完整流程统计:');
    console.log('   下载成功:', completeResult.downloadSuccess ? '✅' : '❌');
    console.log('   TOS上传:', completeResult.uploadSuccess ? '✅' : '⚠️ 跳过');
    console.log('   本地清理:', completeResult.cleanupSuccess ? '✅' : '❌');
    console.log('   文件大小:', formatFileSize(completeResult.fileSize));
    console.log('   文件类型:', completeResult.contentType);

    if (completeResult.publicUrl) {
      console.log('   公开URL:', completeResult.publicUrl);
    }
  }

  if (downloadResult.success) {
    console.log('\n📊 基本下载统计:');
    console.log('   文件大小:', formatFileSize(downloadResult.fileSize));
    console.log('   文件类型:', downloadResult.contentType);
    console.log('   下载时间:', downloadResult.downloadTime + 'ms');
  }

  console.log('\n📝 注意事项:');
  console.log('- 确保.env文件中包含正确的Twilio凭据');
  console.log('- 媒体URL必须是有效的Twilio媒体链接');
  console.log('- TOS上传需要正确的火山引擎配置');
  console.log('- 本地文件会在测试后自动清理');

  return downloadResult.success && authResult && completeResult.success;
}

// 运行测试
if (require.main === module) {
  main().then(success => {
    process.exit(success ? 0 : 1);
  }).catch(error => {
    console.error('💥 测试执行失败:', error);
    process.exit(1);
  });
}

module.exports = {
  testTwilioMediaDownload,
  testAuthenticationFailure,
  TEST_CONFIG
};
