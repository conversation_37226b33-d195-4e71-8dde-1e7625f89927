import React, { useState, useEffect } from 'react';
import { 
  Users, 
  Phone, 
  Hash, 
  MessageCircle, 
  MessageSquare,
  Filter,
  RefreshCw,
  Clock,
  Activity
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { apiService } from '../services/api';
import { formatDistanceToNow } from 'date-fns';
import { zhCN } from 'date-fns/locale';

export function UsersList() {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [filters, setFilters] = useState({
    platform: '',
    page: 1,
    limit: 20
  });

  useEffect(() => {
    loadUsers();
  }, [filters]);

  const loadUsers = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiService.getUsers(filters);
      setUsers(response.data || []);
    } catch (err) {
      setError(err.message);
      console.error('加载用户列表失败:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = () => {
    loadUsers();
  };

  const handlePlatformFilter = (platform) => {
    setFilters(prev => ({
      ...prev,
      platform: prev.platform === platform ? '' : platform,
      page: 1
    }));
  };

  const getPlatformIcon = (platform) => {
    switch (platform) {
      case 'whatsapp':
        return <Phone className="h-4 w-4" />;
      case 'slack':
        return <Hash className="h-4 w-4" />;
      case 'line':
        return <MessageCircle className="h-4 w-4" />;
      default:
        return <MessageSquare className="h-4 w-4" />;
    }
  };

  const getPlatformColor = (platform) => {
    switch (platform) {
      case 'whatsapp':
        return 'bg-green-100 text-green-800';
      case 'slack':
        return 'bg-purple-100 text-purple-800';
      case 'line':
        return 'bg-green-50 text-green-700';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatLastActivity = (timestamp) => {
    if (!timestamp) return '从未活跃';
    try {
      return formatDistanceToNow(new Date(timestamp), { 
        addSuffix: true, 
        locale: zhCN 
      });
    } catch {
      return '时间格式错误';
    }
  };

  const getActivityStatus = (lastActivity) => {
    if (!lastActivity) return { status: 'inactive', color: 'bg-gray-400' };
    
    const now = new Date();
    const activityTime = new Date(lastActivity);
    const diffHours = (now - activityTime) / (1000 * 60 * 60);
    
    if (diffHours < 1) return { status: 'active', color: 'bg-green-500' };
    if (diffHours < 24) return { status: 'recent', color: 'bg-yellow-500' };
    return { status: 'inactive', color: 'bg-gray-400' };
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold text-gray-900">用户管理</h1>
          <Button disabled>
            <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
            加载中...
          </Button>
        </div>
        <div className="grid gap-4">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
                  <div className="flex-1 space-y-2">
                    <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                  <div className="space-y-2">
                    <div className="h-4 bg-gray-200 rounded w-16"></div>
                    <div className="h-3 bg-gray-200 rounded w-12"></div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">用户管理</h1>
        <div className="flex items-center space-x-3">
          <Button variant="outline" onClick={handleRefresh}>
            <RefreshCw className="h-4 w-4 mr-2" />
            刷新
          </Button>
        </div>
      </div>

      {/* 过滤器 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Filter className="h-5 w-5 mr-2" />
            筛选条件
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            <Button
              variant={filters.platform === '' ? 'default' : 'outline'}
              size="sm"
              onClick={() => handlePlatformFilter('')}
            >
              全部平台
            </Button>
            {['whatsapp', 'slack', 'line'].map((platform) => (
              <Button
                key={platform}
                variant={filters.platform === platform ? 'default' : 'outline'}
                size="sm"
                onClick={() => handlePlatformFilter(platform)}
                className="capitalize"
              >
                {getPlatformIcon(platform)}
                <span className="ml-2">{platform}</span>
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* 错误提示 */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="text-red-600">
              加载用户列表失败: {error}
            </div>
          </CardContent>
        </Card>
      )}

      {/* 用户列表 */}
      <div className="grid gap-4">
        {users.length === 0 ? (
          <Card>
            <CardContent className="pt-6">
              <div className="text-center text-gray-500 py-8">
                <Users className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p className="text-lg font-medium">暂无用户记录</p>
                <p className="text-sm">当有用户发送消息时，用户信息将显示在这里</p>
              </div>
            </CardContent>
          </Card>
        ) : (
          users.map((user) => {
            const activityStatus = getActivityStatus(user.lastActivity);
            
            return (
              <Card key={user.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-center space-x-4">
                    {/* 用户头像和状态 */}
                    <div className="relative">
                      <div className={`w-12 h-12 rounded-full flex items-center justify-center ${getPlatformColor(user.platform)}`}>
                        {getPlatformIcon(user.platform)}
                      </div>
                      <div className={`absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-white ${activityStatus.color}`}></div>
                    </div>

                    {/* 用户信息 */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-1">
                        <h3 className="text-lg font-medium text-gray-900 truncate">
                          {user.displayName || user.platformUserId}
                        </h3>
                        <Badge variant="outline" className="capitalize">
                          {user.platform}
                        </Badge>
                      </div>
                      
                      <div className="space-y-1">
                        <p className="text-sm text-gray-600">
                          ID: {user.platformUserId}
                        </p>
                        {user.phoneNumber && (
                          <p className="text-sm text-gray-600">
                            📱 {user.phoneNumber}
                          </p>
                        )}
                        <div className="flex items-center text-sm text-gray-500">
                          <Clock className="h-4 w-4 mr-1" />
                          最后活跃: {formatLastActivity(user.lastActivity)}
                        </div>
                      </div>
                    </div>

                    {/* 统计信息 */}
                    <div className="text-right space-y-2">
                      <div className="flex items-center space-x-4 text-sm">
                        <div className="text-center">
                          <div className="font-semibold text-gray-900">
                            {user.conversationCount || 0}
                          </div>
                          <div className="text-gray-500">对话</div>
                        </div>
                        <div className="text-center">
                          <div className="font-semibold text-gray-900">
                            {user.messageCount || 0}
                          </div>
                          <div className="text-gray-500">消息</div>
                        </div>
                      </div>
                      
                      <div className="flex items-center justify-end">
                        <Badge 
                          variant={activityStatus.status === 'active' ? 'default' : 'secondary'}
                          className="text-xs"
                        >
                          <Activity className="h-3 w-3 mr-1" />
                          {activityStatus.status === 'active' ? '活跃' : 
                           activityStatus.status === 'recent' ? '最近' : '不活跃'}
                        </Badge>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })
        )}
      </div>

      {/* 分页 */}
      {users.length > 0 && (
        <div className="flex justify-center">
          <Button 
            variant="outline"
            onClick={() => setFilters(prev => ({ ...prev, page: prev.page + 1 }))}
          >
            加载更多
          </Button>
        </div>
      )}
    </div>
  );
}
