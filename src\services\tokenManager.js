/**
 * Token 管理服务
 * 负责管理 Slack OAuth Token 的存储、刷新和过期处理
 */
const axios = require('axios');
const SlackAuthDatabase = require('../database/slackAuth');

class TokenManager {
  constructor() {
    this.db = new SlackAuthDatabase();
    this.tokenCache = new Map(); // 内存缓存: userId -> {token, expiresAt}
    this.refreshingTokens = new Map(); // 正在刷新的token: userId -> Promise
  }

  /**
   * 保存完整的OAuth令牌信息
   * @param {Object} oauthData - OAuth响应数据
   */
  async saveOAuthData(oauthData) {
    try {
      console.log('💾 保存OAuth令牌信息...');
      console.log('🔍 OAuth数据结构:', {
        access_token: oauthData.access_token ? '存在' : '不存在',
        refresh_token: oauthData.refresh_token ? '存在' : '不存在',
        expires_in: oauthData.expires_in,
        bot_user_id: oauthData.bot_user_id,
        team: oauthData.team?.name,
        authed_user: {
          id: oauthData.authed_user?.id,
          access_token: oauthData.authed_user?.access_token ? '存在' : '不存在',
          refresh_token: oauthData.authed_user?.refresh_token ? '存在' : '不存在',
          expires_in: oauthData.authed_user?.expires_in
        }
      });

      // 保存Bot令牌信息
      const botTokenData = {
        team_id: oauthData.team.id,
        team_name: oauthData.team.name,
        bot_user_id: oauthData.bot_user_id,
        bot_user_access_token: oauthData.access_token, // 这是bot token
        authed_user_id: oauthData.authed_user.id,
        scope: oauthData.scope,
        installed_at: new Date().toISOString()
      };

      // 如果有bot refresh token和过期时间，添加它们
      if (oauthData.refresh_token) {
        botTokenData.bot_refresh_token = oauthData.refresh_token;
      }
      if (oauthData.expires_in) {
        botTokenData.bot_token_expires_at = Date.now() + (oauthData.expires_in * 1000);
      }

      await this.db.saveWorkspaceAuth(botTokenData);

      // 保存用户令牌信息（如果存在）
      if (oauthData.authed_user && oauthData.authed_user.access_token) {
        const userTokenData = {
          user_id: oauthData.authed_user.id,
          team_id: oauthData.team.id,
          access_token: oauthData.authed_user.access_token,
          scope: oauthData.authed_user.scope
        };

        // 如果有用户refresh token和过期时间，添加它们
        if (oauthData.authed_user.refresh_token) {
          userTokenData.refresh_token = oauthData.authed_user.refresh_token;
        }
        if (oauthData.authed_user.expires_in) {
          userTokenData.token_expires_at = Date.now() + (oauthData.authed_user.expires_in * 1000);
        }

        await this.db.saveUserToken(userTokenData);
      }

      // 保存用户映射
      await this.db.saveUserMapping(
        oauthData.authed_user.id,
        oauthData.team.id
      );

      console.log('✅ OAuth令牌信息保存成功');
      console.log('   团队:', oauthData.team.name);
      console.log('   用户ID:', oauthData.authed_user.id);
      console.log('   Bot ID:', oauthData.bot_user_id);
      console.log('   Bot Token过期时间:', botTokenData.bot_token_expires_at ? new Date(botTokenData.bot_token_expires_at).toISOString() : '未设置');
      console.log('   Bot Refresh Token:', botTokenData.bot_refresh_token ? '已保存' : '未提供');

      return true;
    } catch (error) {
      console.error('❌ 保存OAuth令牌信息失败:', error.message);
      throw error;
    }
  }

  /**
   * 获取用户的访问令牌
   * @param {string} userId - Slack用户ID
   * @returns {Promise<string>} - 访问令牌
   */
  async getUserToken(userId) {
    try {
      console.log(`🔑 获取用户 ${userId} 的访问令牌...`);
      
      // 1. 检查内存缓存
      if (this.tokenCache.has(userId)) {
        const cachedData = this.tokenCache.get(userId);
        
        // 检查是否过期
        if (cachedData.expiresAt > Date.now()) {
          console.log('✅ 使用缓存的用户令牌');
          return cachedData.token;
        } else {
          console.log('⚠️ 缓存的用户令牌已过期');
          this.tokenCache.delete(userId);
        }
      }

      // 2. 从数据库获取
      const tokenData = await this.db.getUserToken(userId);
      
      if (!tokenData) {
        console.log('⚠️ 数据库中未找到用户令牌');
        return null;
      }

      // 3. 检查是否过期
      if (tokenData.token_expires_at > Date.now()) {
        // 缓存并返回有效令牌
        this.tokenCache.set(userId, {
          token: tokenData.access_token,
          expiresAt: tokenData.token_expires_at
        });
        
        console.log('✅ 使用数据库中的用户令牌');
        return tokenData.access_token;
      }

      // 4. 令牌已过期，尝试刷新
      console.log('⚠️ 用户令牌已过期，尝试刷新...');
      
      // 检查是否已经有刷新操作在进行中
      if (this.refreshingTokens.has(userId)) {
        console.log('⏳ 等待正在进行的令牌刷新...');
        return this.refreshingTokens.get(userId);
      }

      // 创建刷新令牌的Promise
      const refreshPromise = this.refreshUserToken(userId, tokenData.refresh_token, tokenData.team_id);
      this.refreshingTokens.set(userId, refreshPromise);

      try {
        const newToken = await refreshPromise;
        return newToken;
      } finally {
        // 无论成功失败，都移除正在刷新的标记
        this.refreshingTokens.delete(userId);
      }
    } catch (error) {
      console.error(`❌ 获取用户 ${userId} 令牌失败:`, error.message);
      return null;
    }
  }

  /**
   * 刷新用户访问令牌
   * @param {string} userId - Slack用户ID
   * @param {string} refreshToken - 刷新令牌
   * @param {string} teamId - 团队ID
   * @returns {Promise<string>} - 新的访问令牌
   */
  async refreshUserToken(userId, refreshToken, teamId) {
    try {
      console.log(`🔄 刷新用户 ${userId} 的访问令牌...`);
      
      const response = await axios.post('https://slack.com/api/oauth.v2.access', {
        client_id: process.env.SLACK_CLIENT_ID,
        client_secret: process.env.SLACK_CLIENT_SECRET,
        grant_type: 'refresh_token',
        refresh_token: refreshToken
      }, {
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!response.data.ok) {
        throw new Error(`刷新令牌失败: ${response.data.error}`);
      }

      const newTokenData = response.data;
      
      // 更新数据库中的令牌信息
      await this.db.saveUserToken({
        user_id: userId,
        team_id: teamId,
        access_token: newTokenData.authed_user.access_token,
        refresh_token: newTokenData.authed_user.refresh_token,
        token_expires_at: Date.now() + (newTokenData.authed_user.expires_in * 1000),
        scope: newTokenData.authed_user.scope
      });

      // 更新缓存
      this.tokenCache.set(userId, {
        token: newTokenData.authed_user.access_token,
        expiresAt: Date.now() + (newTokenData.authed_user.expires_in * 1000)
      });

      console.log('✅ 用户令牌刷新成功');
      return newTokenData.authed_user.access_token;
    } catch (error) {
      console.error(`❌ 刷新用户 ${userId} 令牌失败:`, error.message);
      throw error;
    }
  }

  /**
   * 获取Bot访问令牌
   * @param {string} teamId - 团队ID
   * @returns {Promise<string>} - Bot访问令牌
   */
  async getBotToken(teamId) {
    try {
      console.log(`🔑 获取团队 ${teamId} 的Bot令牌...`);

      // 从数据库获取工作区信息
      const workspace = await this.db.getWorkspaceByTeamId(teamId);

      if (!workspace) {
        console.log(`⚠️ 未找到团队 ${teamId} 的工作区信息`);
        return null;
      }

      console.log(`📋 工作区信息:`, {
        team_name: workspace.team_name,
        bot_user_id: workspace.bot_user_id,
        has_bot_token: !!workspace.bot_user_access_token,
        has_refresh_token: !!workspace.bot_refresh_token,
        token_expires_at: workspace.bot_token_expires_at ? new Date(workspace.bot_token_expires_at).toISOString() : '未设置'
      });

      // 如果没有Bot令牌，直接返回null
      if (!workspace.bot_user_access_token) {
        console.log(`❌ 团队 ${teamId} 没有Bot令牌`);
        return null;
      }

      // 检查令牌是否过期
      if (workspace.bot_token_expires_at) {
        const now = Date.now();
        const isExpired = workspace.bot_token_expires_at <= now;

        if (!isExpired) {
          const timeLeft = workspace.bot_token_expires_at - now;
          const hoursLeft = Math.floor(timeLeft / (1000 * 60 * 60));
          console.log(`✅ Bot令牌有效，剩余时间: ${hoursLeft}小时`);
          return workspace.bot_user_access_token;
        }

        console.log(`⚠️ 团队 ${teamId} 的Bot令牌已过期`);

        // 令牌已过期，尝试刷新
        if (workspace.bot_refresh_token) {
          console.log(`🔄 尝试使用刷新令牌刷新...`);
          try {
            const newToken = await this.refreshBotToken(teamId, workspace.bot_refresh_token);
            if (newToken) {
              console.log(`✅ Bot令牌刷新成功`);
              return newToken;
            }
          } catch (refreshError) {
            console.error(`❌ 刷新Bot令牌失败: ${refreshError.message}`);

            // 如果刷新失败，检查是否是因为刷新令牌无效
            if (refreshError.message.includes('刷新令牌无效')) {
              console.log(`⚠️ 刷新令牌无效，返回原始令牌（可能仍可使用）`);
              // 即使过期，也返回原始令牌，让调用方决定如何处理
              return workspace.bot_user_access_token;
            }

            // 其他刷新错误，返回null
            return null;
          }
        } else {
          console.log(`⚠️ 没有刷新令牌，返回过期的Bot令牌（可能仍可使用）`);
          // 即使过期，也返回原始令牌，让调用方决定如何处理
          return workspace.bot_user_access_token;
        }
      } else {
        // 没有过期时间设置，可能是永久令牌
        console.log(`✅ Bot令牌没有过期时间设置，假设为永久有效`);
        return workspace.bot_user_access_token;
      }

      // 如果所有尝试都失败，返回原始令牌
      console.log(`⚠️ 所有刷新尝试失败，返回原始Bot令牌`);
      return workspace.bot_user_access_token;

    } catch (error) {
      console.error(`❌ 获取团队 ${teamId} 的Bot令牌失败:`, error.message);
      return null;
    }
  }

  /**
   * 刷新Bot访问令牌
   * @param {string} teamId - 团队ID
   * @param {string} refreshToken - 刷新令牌
   * @returns {Promise<string>} - 新的Bot访问令牌
   */
  async refreshBotToken(teamId, refreshToken) {
    try {
      console.log(`🔄 刷新团队 ${teamId} 的Bot令牌...`);
      console.log(`   使用刷新令牌: ${refreshToken ? refreshToken.substring(0, 20) + '...' : 'null'}`);

      // 验证必要的环境变量
      if (!process.env.SLACK_CLIENT_ID || !process.env.SLACK_CLIENT_SECRET) {
        throw new Error('缺少必要的Slack客户端配置 (SLACK_CLIENT_ID 或 SLACK_CLIENT_SECRET)');
      }

      if (!refreshToken) {
        throw new Error('刷新令牌为空');
      }

      const requestData = {
        client_id: process.env.SLACK_CLIENT_ID,
        client_secret: process.env.SLACK_CLIENT_SECRET,
        grant_type: 'refresh_token',
        refresh_token: refreshToken
      };

      console.log(`📤 发送刷新请求到 Slack API...`);
      console.log(`   Client ID: ${process.env.SLACK_CLIENT_ID}`);
      console.log(`   Grant Type: ${requestData.grant_type}`);

      const response = await axios.post('https://slack.com/api/oauth.v2.access', requestData, {
        headers: {
          'Content-Type': 'application/json'
        }
      });

      console.log(`📥 收到刷新响应:`, {
        status: response.status,
        ok: response.data.ok,
        error: response.data.error,
        has_access_token: !!response.data.access_token,
        has_refresh_token: !!response.data.refresh_token,
        expires_in: response.data.expires_in
      });

      if (!response.data.ok) {
        const error = response.data.error;
        console.error(`❌ Slack API 返回错误: ${error}`);

        // 根据不同的错误类型提供更详细的处理
        switch (error) {
          case 'invalid_code':
            console.error('💡 刷新令牌无效或已过期，需要重新授权');
            // 标记刷新令牌为无效
            await this.markRefreshTokenInvalid(teamId);
            throw new Error(`刷新令牌无效，需要重新授权: ${error}`);

          case 'invalid_client_id':
            console.error('💡 客户端ID无效，请检查SLACK_CLIENT_ID配置');
            throw new Error(`客户端ID无效: ${error}`);

          case 'invalid_client_secret':
            console.error('💡 客户端密钥无效，请检查SLACK_CLIENT_SECRET配置');
            throw new Error(`客户端密钥无效: ${error}`);

          case 'invalid_grant_type':
            console.error('💡 授权类型无效');
            throw new Error(`授权类型无效: ${error}`);

          default:
            throw new Error(`刷新Bot令牌失败: ${error}`);
        }
      }

      const newTokenData = response.data;

      // 更新数据库中的令牌信息
      const updateData = {
        bot_user_access_token: newTokenData.access_token,
        bot_refresh_token: newTokenData.refresh_token,
        bot_token_expires_at: Date.now() + (newTokenData.expires_in * 1000)
      };

      await this.db.updateWorkspaceToken(teamId, updateData);

      console.log('✅ Bot令牌刷新成功');
      console.log(`   新令牌长度: ${newTokenData.access_token ? newTokenData.access_token.length : 0}`);
      console.log(`   新过期时间: ${new Date(updateData.bot_token_expires_at).toISOString()}`);

      return newTokenData.access_token;
    } catch (error) {
      console.error(`❌ 刷新团队 ${teamId} 的Bot令牌失败:`, error.message);

      // 如果是网络错误，提供更详细的信息
      if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
        console.error('💡 网络连接失败，请检查网络连接和代理设置');
      }

      throw error;
    }
  }

  /**
   * 标记刷新令牌为无效
   * @param {string} teamId - 团队ID
   */
  async markRefreshTokenInvalid(teamId) {
    try {
      console.log(`🚫 标记团队 ${teamId} 的刷新令牌为无效...`);

      await this.db.updateWorkspaceToken(teamId, {
        bot_refresh_token: null,
        bot_token_expires_at: null
      });

      console.log('✅ 刷新令牌已标记为无效');
    } catch (error) {
      console.error('❌ 标记刷新令牌无效失败:', error.message);
    }
  }

  /**
   * 清除用户令牌缓存
   * @param {string} userId - Slack用户ID
   */
  clearUserTokenCache(userId) {
    if (this.tokenCache.has(userId)) {
      this.tokenCache.delete(userId);
      console.log(`🧹 已清除用户 ${userId} 的令牌缓存`);
    }
  }
}

module.exports = TokenManager;
