// 测试Coze原始API响应
const axios = require('axios');

async function testCozeRaw() {
  console.log('🧪 测试Coze原始API响应');
  console.log('发送消息: 你好');
  console.log('');

  const botId = '7528309468237529127';
  const accessToken = 'pat_FSEGBGcfbYwabmxELRPZYAReTrVaWIMMPBwlyIfUeXnqaJsBTcbbIZrpNyEAZwLR';
  const baseUrl = 'https://api.coze.cn';

  try {
    // 发送请求
    const url = `${baseUrl}/v3/chat`;
    const payload = {
      bot_id: botId,
      user_id: 'test_raw_user',
      stream: false,
      auto_save_history: true,
      additional_messages: [
        {
          role: "user",
          content: "你好",
          content_type: "text"
        }
      ]
    };

    console.log('📤 发送请求到:', url);
    console.log('📤 请求数据:', JSON.stringify(payload, null, 2));
    console.log('');

    const response = await axios.post(url, payload, {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      timeout: 30000
    });

    console.log('📥 原始响应:');
    console.log('状态码:', response.status);
    console.log('响应数据:', JSON.stringify(response.data, null, 2));
    console.log('');

    // 如果是异步响应，轮询结果
    if (response.data.data && response.data.data.status === 'in_progress') {
      const chatId = response.data.data.id;
      const conversationId = response.data.data.conversation_id;
      
      console.log(`⏳ 等待处理完成 (Chat ID: ${chatId})...`);
      
      // 轮询3次
      for (let i = 1; i <= 3; i++) {
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        console.log(`📊 轮询第 ${i} 次...`);
        
        const retrieveUrl = `${baseUrl}/v3/chat/retrieve`;
        const retrieveResponse = await axios.get(retrieveUrl, {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          },
          params: {
            chat_id: chatId,
            conversation_id: conversationId
          },
          timeout: 10000
        });

        console.log(`轮询响应 ${i}:`, JSON.stringify(retrieveResponse.data, null, 2));
        
        if (retrieveResponse.data.data && retrieveResponse.data.data.status === 'completed') {
          console.log('✅ 处理完成！');
          
          // 获取消息列表
          const messagesUrl = `${baseUrl}/v3/chat/message/list`;
          const messagesResponse = await axios.get(messagesUrl, {
            headers: {
              'Authorization': `Bearer ${accessToken}`,
              'Content-Type': 'application/json'
            },
            params: {
              chat_id: chatId,
              conversation_id: conversationId
            },
            timeout: 10000
          });

          console.log('📝 消息列表:', JSON.stringify(messagesResponse.data, null, 2));
          
          // 提取回复
          if (messagesResponse.data.data && messagesResponse.data.data.length > 0) {
            const assistantMessages = messagesResponse.data.data.filter(msg => msg.role === 'assistant');
            if (assistantMessages.length > 0) {
              console.log('');
              console.log('🤖 Coze回复:', assistantMessages[assistantMessages.length - 1].content);
            }
          }
          
          break;
        }
      }
    }

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.response) {
      console.error('错误状态码:', error.response.status);
      console.error('错误响应:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

testCozeRaw();
