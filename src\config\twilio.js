const twilio = require('twilio');
const logger = require('./logger');

class TwilioService {
  constructor() {
    this.client = null;
    this.accountSid = process.env.TWILIO_ACCOUNT_SID;
    this.authToken = process.env.TWILIO_AUTH_TOKEN;
    this.phoneNumber = process.env.TWILIO_PHONE_NUMBER;
    
    this.init();
  }

  init() {
    try {
      if (!this.accountSid || !this.authToken) {
        throw new Error('Twilio credentials not found in environment variables');
      }

      this.client = twilio(this.accountSid, this.authToken);
      logger.info('Twilio client initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize Twilio client:', error);
      throw error;
    }
  }

  getClient() {
    if (!this.client) {
      throw new Error('Twilio client not initialized');
    }
    return this.client;
  }

  getPhoneNumber() {
    if (!this.phoneNumber) {
      throw new Error('Twilio phone number not configured');
    }
    return this.phoneNumber;
  }

  // 验证Twilio连接
  async validateConnection() {
    try {
      const account = await this.client.api.accounts(this.accountSid).fetch();
      logger.info('Twilio connection validated successfully', { 
        accountSid: account.sid,
        status: account.status 
      });
      return true;
    } catch (error) {
      logger.error('Twilio connection validation failed:', error);
      throw error;
    }
  }

  // 获取账户信息
  async getAccountInfo() {
    try {
      const account = await this.client.api.accounts(this.accountSid).fetch();
      return {
        sid: account.sid,
        friendlyName: account.friendlyName,
        status: account.status,
        type: account.type
      };
    } catch (error) {
      logger.error('Failed to get account info:', error);
      throw error;
    }
  }
}

module.exports = new TwilioService();
