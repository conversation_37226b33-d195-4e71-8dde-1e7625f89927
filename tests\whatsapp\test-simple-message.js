// 简单测试消息处理
const coze = require('./coze-api-v3-client');

async function testSimpleMessage() {
  console.log('🧪 简单测试消息处理');
  console.log('=' .repeat(50));
  
  try {
    console.log('👤 用户: 你好');
    const result = await coze.sendMessage('你好', 'simple_test_user');
    console.log('🤖 机器人:', result.text);
    console.log('✅ 测试成功');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.error('错误详情:', error);
  }
}

testSimpleMessage();
