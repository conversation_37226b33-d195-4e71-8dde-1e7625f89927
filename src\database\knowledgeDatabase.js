// 知识图谱数据库管理
const sqlite3 = require('sqlite3').verbose();
const path = require('path');

class KnowledgeDatabase {
  constructor() {
    // 使用与其他服务相同的数据库目录
    this.dbPath = path.join(__dirname, '../../data/knowledge_base.db');
    this.db = null;
    this.init();
  }

  // 初始化数据库
  async init() {
    return new Promise((resolve, reject) => {
      // 确保数据目录存在
      const fs = require('fs');
      const dataDir = path.dirname(this.dbPath);
      if (!fs.existsSync(dataDir)) {
        fs.mkdirSync(dataDir, { recursive: true });
      }

      this.db = new sqlite3.Database(this.dbPath, (err) => {
        if (err) {
          console.error('❌ 知识库数据库连接失败:', err.message);
          reject(err);
        } else {
          console.log('✅ 知识库数据库连接成功:', this.dbPath);
          this.createTables().then(resolve).catch(reject);
        }
      });
    });
  }

  // 创建表结构
  async createTables() {
    return new Promise((resolve, reject) => {
      const createKnowledgeItemsTable = `
        CREATE TABLE IF NOT EXISTS knowledge_items (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          category TEXT NOT NULL,
          title TEXT NOT NULL,
          content TEXT NOT NULL,
          tags TEXT,
          priority INTEGER DEFAULT 1,
          is_active BOOLEAN DEFAULT TRUE,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          synced_to_zep BOOLEAN DEFAULT FALSE,
          zep_sync_time DATETIME
        )
      `;

      const createKnowledgeCategoriesTable = `
        CREATE TABLE IF NOT EXISTS knowledge_categories (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT UNIQUE NOT NULL,
          description TEXT,
          color TEXT DEFAULT '#3B82F6',
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `;

      const createSyncLogsTable = `
        CREATE TABLE IF NOT EXISTS sync_logs (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          action TEXT NOT NULL,
          item_count INTEGER,
          success BOOLEAN,
          error_message TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `;

      // 创建索引
      const createIndexes = [
        'CREATE INDEX IF NOT EXISTS idx_knowledge_items_category ON knowledge_items(category)',
        'CREATE INDEX IF NOT EXISTS idx_knowledge_items_active ON knowledge_items(is_active)',
        'CREATE INDEX IF NOT EXISTS idx_knowledge_items_synced ON knowledge_items(synced_to_zep)',
        'CREATE INDEX IF NOT EXISTS idx_sync_logs_created_at ON sync_logs(created_at)'
      ];

      this.db.serialize(() => {
        this.db.run(createKnowledgeItemsTable, (err) => {
          if (err) {
            console.error('❌ 创建knowledge_items表失败:', err.message);
            reject(err);
            return;
          }
          console.log('✅ knowledge_items表创建成功');
        });

        this.db.run(createKnowledgeCategoriesTable, (err) => {
          if (err) {
            console.error('❌ 创建knowledge_categories表失败:', err.message);
            reject(err);
            return;
          }
          console.log('✅ knowledge_categories表创建成功');
        });

        this.db.run(createSyncLogsTable, (err) => {
          if (err) {
            console.error('❌ 创建sync_logs表失败:', err.message);
            reject(err);
            return;
          }
          console.log('✅ sync_logs表创建成功');

          // 创建索引
          let indexCount = 0;
          createIndexes.forEach((indexSql, index) => {
            this.db.run(indexSql, (err) => {
              if (err) {
                console.error(`❌ 创建知识库索引${index + 1}失败:`, err.message);
              } else {
                console.log(`✅ 知识库索引${index + 1}创建成功`);
              }
              indexCount++;
              if (indexCount === createIndexes.length) {
                this.initializeDefaultData().then(resolve).catch(reject);
              }
            });
          });
        });
      });
    });
  }

  // 初始化默认数据
  async initializeDefaultData() {
    try {
      // 检查是否已有数据
      const existingCategories = await this.getCategories();
      if (existingCategories.length > 0) {
        console.log('✅ 知识库已有数据，跳过初始化');
        return;
      }

      console.log('📚 初始化知识库默认数据...');

      // 创建默认分类
      const defaultCategories = [
        { name: '产品信息', description: '产品型号、规格、价格等信息', color: '#3B82F6' },
        { name: '流量套餐', description: '各种流量套餐的详细信息', color: '#10B981' },
        { name: '售前服务', description: '售前咨询、试用、配送等政策', color: '#F59E0B' },
        { name: '售后服务', description: '保修、技术支持、维修等服务', color: '#EF4444' },
        { name: '使用指南', description: '设备使用方法和技巧', color: '#8B5CF6' }
      ];

      for (const category of defaultCategories) {
        await this.createCategory(category.name, category.description, category.color);
      }

      // 创建默认知识条目
      const defaultKnowledgeItems = [
        {
          category: '产品信息',
          title: '畅游 Mini 路由器',
          content: '价格299元，8-10小时续航，支持5台设备，4G LTE网络。超便携设计，适合个人短途旅行，1-2人使用。',
          tags: 'Mini,299,便携,个人,旅行',
          priority: 1
        },
        {
          category: '产品信息',
          title: '畅游 Pro 路由器',
          content: '价格599元，15-18小时续航，支持10台设备，5G/4G智能切换。平衡性能与续航，适合商务差旅，3-6人使用。',
          tags: 'Pro,599,商务,平衡',
          priority: 1
        },
        {
          category: '产品信息',
          title: '畅游 Max 路由器',
          content: '价格999元，20-24小时续航，支持16台设备，全球主流5G/4G频段。旗舰性能，多设备支持，适合家庭团队旅行，7+人使用。',
          tags: 'Max,999,旗舰,家庭,团队',
          priority: 1
        },
        {
          category: '流量套餐',
          title: '按量计费套餐',
          content: '适合低频用户：80元10GB(30天有效)，150元25GB(60天有效)。灵活计费，按需使用。',
          tags: '按量,80元,150元,灵活',
          priority: 1
        },
        {
          category: '流量套餐',
          title: '月度套餐',
          content: '适合常规用户：128元50GB，198元100GB，288元200GB。月度计费，稳定使用。',
          tags: '月度,128元,198元,288元',
          priority: 1
        },
        {
          category: '售前服务',
          title: '7天无理由试用',
          content: '用户收到设备后7天内可申请无理由退货（设备完好不影响二次销售）。收到退货后3个工作日内完成退款。',
          tags: '试用,7天,退货,退款',
          priority: 1
        },
        {
          category: '售后服务',
          title: '一年硬件保修',
          content: '设备主机享受购买日起一年免费保修服务。保修期内非人为损坏的性能故障，提供免费维修或更换。',
          tags: '保修,一年,免费,维修',
          priority: 1
        }
      ];

      for (const item of defaultKnowledgeItems) {
        await this.createKnowledgeItem(
          item.category,
          item.title,
          item.content,
          item.tags,
          item.priority
        );
      }

      console.log('✅ 知识库默认数据初始化完成');
    } catch (error) {
      console.error('❌ 初始化知识库默认数据失败:', error.message);
      throw error;
    }
  }

  // 创建分类
  async createCategory(name, description = '', color = '#3B82F6') {
    return new Promise((resolve, reject) => {
      const sql = `
        INSERT INTO knowledge_categories (name, description, color)
        VALUES (?, ?, ?)
      `;

      this.db.run(sql, [name, description, color], function(err) {
        if (err) {
          console.error('❌ 创建知识分类失败:', err.message);
          reject(err);
        } else {
          console.log(`✅ 知识分类创建成功 - ID: ${this.lastID}, 名称: ${name}`);
          resolve({ id: this.lastID, name, description, color });
        }
      });
    });
  }

  // 获取所有分类
  async getCategories() {
    return new Promise((resolve, reject) => {
      const sql = 'SELECT * FROM knowledge_categories ORDER BY name';

      this.db.all(sql, [], (err, rows) => {
        if (err) {
          console.error('❌ 获取知识分类失败:', err.message);
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }

  // 创建知识条目
  async createKnowledgeItem(category, title, content, tags = '', priority = 1) {
    return new Promise((resolve, reject) => {
      const sql = `
        INSERT INTO knowledge_items (category, title, content, tags, priority)
        VALUES (?, ?, ?, ?, ?)
      `;

      this.db.run(sql, [category, title, content, tags, priority], function(err) {
        if (err) {
          console.error('❌ 创建知识条目失败:', err.message);
          reject(err);
        } else {
          console.log(`✅ 知识条目创建成功 - ID: ${this.lastID}, 标题: ${title}`);
          resolve({
            id: this.lastID,
            category,
            title,
            content,
            tags,
            priority
          });
        }
      });
    });
  }

  // 获取所有知识条目
  async getKnowledgeItems(category = null, activeOnly = true) {
    return new Promise((resolve, reject) => {
      let sql = 'SELECT * FROM knowledge_items';
      const params = [];

      const conditions = [];
      if (category) {
        conditions.push('category = ?');
        params.push(category);
      }
      if (activeOnly) {
        conditions.push('is_active = ?');
        params.push(true);
      }

      if (conditions.length > 0) {
        sql += ' WHERE ' + conditions.join(' AND ');
      }

      sql += ' ORDER BY priority DESC, updated_at DESC';

      this.db.all(sql, params, (err, rows) => {
        if (err) {
          console.error('❌ 获取知识条目失败:', err.message);
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }

  // 更新知识条目
  async updateKnowledgeItem(id, updates) {
    return new Promise((resolve, reject) => {
      const fields = [];
      const params = [];

      Object.keys(updates).forEach(key => {
        if (updates[key] !== undefined) {
          fields.push(`${key} = ?`);
          params.push(updates[key]);
        }
      });

      if (fields.length === 0) {
        resolve({ id, updated: false });
        return;
      }

      fields.push('updated_at = CURRENT_TIMESTAMP');
      params.push(id);

      const sql = `
        UPDATE knowledge_items 
        SET ${fields.join(', ')}
        WHERE id = ?
      `;

      this.db.run(sql, params, function(err) {
        if (err) {
          console.error('❌ 更新知识条目失败:', err.message);
          reject(err);
        } else {
          console.log(`✅ 知识条目更新成功 - ID: ${id}`);
          resolve({ id, updated: this.changes > 0 });
        }
      });
    });
  }

  // 删除知识条目（软删除）
  async deleteKnowledgeItem(id) {
    return new Promise((resolve, reject) => {
      const sql = `
        UPDATE knowledge_items 
        SET is_active = FALSE, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `;

      this.db.run(sql, [id], function(err) {
        if (err) {
          console.error('❌ 删除知识条目失败:', err.message);
          reject(err);
        } else {
          console.log(`✅ 知识条目删除成功 - ID: ${id}`);
          resolve({ id, deleted: this.changes > 0 });
        }
      });
    });
  }

  // 记录同步日志
  async logSync(action, itemCount, success, errorMessage = null) {
    return new Promise((resolve, reject) => {
      const sql = `
        INSERT INTO sync_logs (action, item_count, success, error_message)
        VALUES (?, ?, ?, ?)
      `;

      this.db.run(sql, [action, itemCount, success, errorMessage], function(err) {
        if (err) {
          console.error('❌ 记录同步日志失败:', err.message);
          reject(err);
        } else {
          resolve({ id: this.lastID });
        }
      });
    });
  }

  // 获取同步日志
  async getSyncLogs(limit = 50) {
    return new Promise((resolve, reject) => {
      const sql = `
        SELECT * FROM sync_logs 
        ORDER BY created_at DESC 
        LIMIT ?
      `;

      this.db.all(sql, [limit], (err, rows) => {
        if (err) {
          console.error('❌ 获取同步日志失败:', err.message);
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }

  // 关闭数据库连接
  close() {
    if (this.db) {
      this.db.close((err) => {
        if (err) {
          console.error('❌ 关闭知识库数据库连接失败:', err.message);
        } else {
          console.log('✅ 知识库数据库连接已关闭');
        }
      });
    }
  }
}

module.exports = KnowledgeDatabase;
