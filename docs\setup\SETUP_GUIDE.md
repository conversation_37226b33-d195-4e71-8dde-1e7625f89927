# Twilio Business Messaging API - 完整设置指南

## 🎯 当前状态

✅ **Twilio账户已验证**
- Account SID: AC7657552c992e2a3737961532e7e609d1
- 账户状态: 活跃 (试用账户)
- 余额: $15.495 USD
- API连接: 正常

⚠️ **需要完成的步骤**
- 购买Twilio电话号码
- 配置Webhook URL（可选）

## 📞 第一步：获取Twilio电话号码

### 方法1：手动购买（推荐）

1. **访问Twilio控制台**
   ```
   https://console.twilio.com/us1/develop/phone-numbers/manage/search
   ```

2. **搜索电话号码**
   - 选择国家：United States
   - 勾选 "SMS" 功能
   - 点击搜索

3. **推荐号码**（基于可用性检查）
   ```
   +*********** (Rebecca, GA)
   +*********** (Marlton, NJ)
   +*********** (West Bend, WI)
   ```

4. **购买号码**
   - 选择一个号码
   - 点击 "Buy" 按钮
   - 确认购买

5. **更新配置**
   编辑 `.env` 文件：
   ```env
   TWILIO_PHONE_NUMBER=+***********
   ```

### 方法2：试用号码（如果可用）

某些试用账户可能提供免费的测试号码，请检查Twilio控制台。

## 🔧 第二步：启动完整服务器

### 1. 验证配置
```bash
# 测试Twilio连接
node test-twilio-connection.js
```

### 2. 启动服务器
```bash
# 方式1：使用npm
npm start

# 方式2：直接运行
node src/app.js
```

### 3. 验证服务器
```bash
# 健康检查
curl http://localhost:3000/health

# 或使用浏览器访问
http://localhost:3000/health
```

## 📱 第三步：测试消息发送

### 1. 创建测试脚本
```javascript
// test-real-message.js
const axios = require('axios');

async function testRealMessage() {
  try {
    const response = await axios.post('http://localhost:3000/api/messages/send', {
      to: '+**********', // 替换为您的手机号码
      body: 'Hello from Twilio Business Messaging API!',
      campaignId: 'test-campaign'
    }, {
      headers: {
        'X-API-Key': 'demo_api_key_12345',
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ 消息发送成功:', response.data);
  } catch (error) {
    console.error('❌ 发送失败:', error.response?.data || error.message);
  }
}

testRealMessage();
```

### 2. 运行测试
```bash
node test-real-message.js
```

## 🌐 第四步：配置Webhook（可选）

### 1. 获取公网URL

**选项A：使用ngrok（开发环境）**
```bash
# 安装ngrok
npm install -g ngrok

# 启动隧道
ngrok http 3000

# 复制HTTPS URL，例如：https://abc123.ngrok.io
```

**选项B：部署到云服务**
- Heroku
- Vercel
- AWS
- 其他云平台

### 2. 配置Webhook URL

在Twilio控制台中配置：

1. **消息状态回调**
   ```
   https://your-domain.com/webhook/message-status
   ```

2. **入站消息处理**
   ```
   https://your-domain.com/webhook/incoming-message
   ```

### 3. 测试Webhook
```bash
# 测试webhook健康检查
curl https://your-domain.com/webhook/health
```

## 🧪 第五步：完整功能测试

### 1. 运行完整测试套件
```bash
# 快速API测试
node quick-test.js

# 单元测试
npm test
```

### 2. 测试所有API端点

**发送消息**
```bash
curl -X POST http://localhost:3000/api/messages/send \
  -H "X-API-Key: demo_api_key_12345" \
  -H "Content-Type: application/json" \
  -d '{"to":"+**********","body":"Test message"}'
```

**查询状态**
```bash
curl http://localhost:3000/api/status/message/MESSAGE_ID \
  -H "X-API-Key: demo_api_key_12345"
```

**传递报告**
```bash
curl http://localhost:3000/api/status/delivery-report \
  -H "X-API-Key: demo_api_key_12345"
```

## 📊 监控和日志

### 1. 查看日志
```bash
# 实时日志
tail -f logs/combined.log

# 错误日志
tail -f logs/error.log
```

### 2. 健康检查
```bash
# 服务器健康
curl http://localhost:3000/health

# Webhook健康
curl http://localhost:3000/webhook/health
```

## 🔒 安全配置

### 1. 更新API密钥
编辑 `.env` 文件：
```env
API_KEY=your_secure_api_key_here
JWT_SECRET=your_secure_jwt_secret_here
```

### 2. 配置CORS（生产环境）
编辑 `src/app.js`：
```javascript
app.use(cors({
  origin: ['https://your-frontend-domain.com'],
  credentials: true
}));
```

## 🚀 生产部署

### 1. 环境变量
```env
NODE_ENV=production
PORT=3000
MONGODB_URI=mongodb://your-production-db
WEBHOOK_BASE_URL=https://your-production-domain.com
LOG_LEVEL=warn
```

### 2. 进程管理
```bash
# 使用PM2
npm install -g pm2
pm2 start src/app.js --name twilio-messaging
pm2 startup
pm2 save
```

## 📞 支持和故障排除

### 常见问题

1. **消息发送失败**
   - 检查电话号码格式（+**********）
   - 验证Twilio余额
   - 检查试用账户限制

2. **Webhook不工作**
   - 确保URL公网可访问
   - 检查HTTPS配置
   - 验证Webhook签名

3. **数据库连接失败**
   - 检查MongoDB服务状态
   - 验证连接字符串
   - 检查网络连接

### 获取帮助

- 查看 `docs/API.md` 获取API文档
- 检查 `logs/` 目录获取错误信息
- 运行 `node test-twilio-connection.js` 验证Twilio连接

---

**下一步**: 购买Twilio电话号码并开始发送真实消息！
