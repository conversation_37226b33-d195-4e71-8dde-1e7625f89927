// 测试WhatsApp消息发送
require('dotenv').config();
const twilio = require('twilio');

async function testWhatsAppMessage() {
  console.log('📱 测试WhatsApp消息发送...\n');

  try {
    const accountSid = process.env.TWILIO_ACCOUNT_SID;
    const authToken = process.env.TWILIO_AUTH_TOKEN;
    const client = twilio(accountSid, authToken);

    // 1. 检查WhatsApp沙盒状态
    console.log('1. 检查WhatsApp沙盒配置...');
    
    // Twilio WhatsApp沙盒号码
    const whatsappFrom = 'whatsapp:+***********'; // Twilio沙盒号码
    
    // 请输入您的WhatsApp号码（需要先加入沙盒）
    const whatsappTo = 'whatsapp:+*************'; // 请替换为您的WhatsApp号码
    
    console.log('📋 WhatsApp配置:');
    console.log('   发送方:', whatsappFrom);
    console.log('   接收方:', whatsappTo);
    console.log('');

    // 2. 发送WhatsApp消息
    console.log('2. 发送WhatsApp消息...');
    
    const message = await client.messages.create({
      from: whatsappFrom,
      to: whatsappTo,
      body: '🎉 Hello from Twilio Business Messaging API!\n\n这是一条来自您的Twilio API的测试消息。\n\n✅ 系统运行正常\n📱 WhatsApp集成成功\n🚀 准备投入使用'
    });

    console.log('✅ WhatsApp消息发送成功!');
    console.log('   消息SID:', message.sid);
    console.log('   状态:', message.status);
    console.log('   发送时间:', new Date().toISOString());
    console.log('');

    // 3. 查询消息状态
    console.log('3. 查询消息状态...');
    setTimeout(async () => {
      try {
        const updatedMessage = await client.messages(message.sid).fetch();
        console.log('📊 消息状态更新:');
        console.log('   当前状态:', updatedMessage.status);
        console.log('   错误代码:', updatedMessage.errorCode || 'None');
        console.log('   错误信息:', updatedMessage.errorMessage || 'None');
        console.log('   价格:', updatedMessage.price || 'N/A', updatedMessage.priceUnit || '');
      } catch (statusError) {
        console.log('⚠️  状态查询失败:', statusError.message);
      }
    }, 3000);

    console.log('🎯 成功发送WhatsApp消息!');
    console.log('\n💡 提示:');
    console.log('   - 请检查您的WhatsApp应用');
    console.log('   - 消息应该会在几秒内到达');
    console.log('   - 如果没有收到，请检查号码格式和沙盒设置');

  } catch (error) {
    console.error('❌ WhatsApp消息发送失败:', error.message);
    
    if (error.code === 63016) {
      console.error('\n📋 解决方案:');
      console.error('   1. 确保您的WhatsApp号码已加入Twilio沙盒');
      console.error('   2. 发送 "join <sandbox-keyword>" 到 +1 415 523 8886');
      console.error('   3. 等待确认消息');
      console.error('   4. 更新脚本中的whatsappTo号码');
    } else if (error.code === 21211) {
      console.error('   错误: 无效的电话号码格式');
      console.error('   请使用格式: whatsapp:+1234567890');
    } else if (error.code === 20003) {
      console.error('   错误: 认证失败 - 请检查Twilio凭据');
    } else {
      console.error('   错误代码:', error.code);
      console.error('   详细信息:', error.moreInfo || 'N/A');
    }

    console.log('\n📱 WhatsApp沙盒设置步骤:');
    console.log('   1. 访问: https://console.twilio.com/us1/develop/sms/try-it-out/whatsapp-learn');
    console.log('   2. 找到您的沙盒号码和关键词');
    console.log('   3. 用WhatsApp发送 "join <keyword>" 到沙盒号码');
    console.log('   4. 等待确认消息');
    console.log('   5. 更新此脚本中的电话号码');
  }
}

// 显示设置说明
console.log('📱 WhatsApp消息发送测试');
console.log('=' .repeat(50));
console.log('⚠️  重要: 在运行此脚本之前，请确保:');
console.log('   1. 您的WhatsApp号码已加入Twilio沙盒');
console.log('   2. 更新脚本中的whatsappTo号码');
console.log('   3. 确保号码格式为: whatsapp:+1234567890');
console.log('');

// 询问是否继续
console.log('💡 如需继续，请更新脚本中的whatsappTo号码，然后重新运行');
console.log('');

// 运行测试（请先更新号码）
testWhatsAppMessage();
