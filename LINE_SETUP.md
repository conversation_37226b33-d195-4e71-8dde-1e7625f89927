# LINE Messaging API 集成配置指南

本指南将帮助您配置LINE Messaging API集成，使智能客服系统能够接收和回复LINE消息。

## 前提条件

1. 拥有LINE开发者账号
2. 已创建LINE Official Account
3. 已安装Node.js和npm

## 配置步骤

### 1. 创建LINE Messaging API Channel

1. 访问 [LINE Developers Console](https://developers.line.biz/console/)
2. 登录您的LINE开发者账号
3. 创建新的Provider（如果还没有）
4. 在Provider中创建新的Messaging API Channel
5. 填写必要信息：
   - Channel name: 您的机器人名称
   - Channel description: 机器人描述
   - Category: 选择合适的分类
   - Subcategory: 选择合适的子分类

### 2. 获取Channel Access Token

1. 在LINE Developers Console中，进入您创建的Messaging API Channel
2. 点击"Messaging API"标签
3. 在"Channel access token"部分，点击"Issue"按钮生成token
4. 复制生成的Channel Access Token

### 3. 配置环境变量

在项目根目录的`.env`文件中添加以下配置：

```env
# LINE Configuration
LINE_CHANNEL_ID=**********
LINE_CHANNEL_SECRET=2e4c782ab842a7d9ac469a57ee9a3521
LINE_CHANNEL_ACCESS_TOKEN=您的Channel_Access_Token
```

**注意：** 
- `LINE_CHANNEL_ID` 和 `LINE_CHANNEL_SECRET` 已经预配置
- 您只需要设置 `LINE_CHANNEL_ACCESS_TOKEN`

### 4. 配置Webhook URL

1. 启动应用程序：
   ```bash
   npm start
   ```

2. 使用ngrok暴露本地服务到公网：
   ```bash
   ngrok http 3002
   ```

3. 复制ngrok提供的HTTPS URL（例如：`https://abc123.ngrok.io`）

4. 在LINE Developers Console中：
   - 进入您的Messaging API Channel
   - 点击"Messaging API"标签
   - 在"Webhook settings"部分：
     - 设置Webhook URL为：`https://your-ngrok-url.ngrok.io/line-webhook`
     - 启用"Use webhook"
     - 可选：启用"Webhook redelivery"

5. 点击"Verify"按钮测试webhook连接

### 5. 配置LINE Official Account设置

1. 在LINE Developers Console中，点击"Messaging API"标签
2. 在"LINE Official Account features"部分：
   - 禁用"Auto-reply messages"（自动回复消息）
   - 禁用"Greeting messages"（欢迎消息）
   - 启用"Webhooks"

### 6. 测试集成

1. 使用LINE应用扫描您的LINE Official Account的QR码
2. 添加为好友
3. 发送测试消息
4. 检查应用程序日志确认消息被正确接收和处理

## 功能特性

### 支持的消息类型
- 文本消息
- 用户关注/取消关注事件

### 消息处理流程
1. 用户发送消息到LINE Official Account
2. LINE Platform发送webhook事件到您的服务器
3. 服务器验证请求签名
4. 使用现有的消息处理服务处理用户消息
5. 通过LINE Messaging API发送回复消息

### 安全特性
- 请求签名验证
- 环境变量保护敏感信息
- 错误处理和日志记录

## 故障排除

### 常见问题

1. **Webhook验证失败**
   - 确保ngrok URL是HTTPS
   - 检查webhook URL格式是否正确
   - 确认服务器正在运行

2. **消息发送失败**
   - 检查Channel Access Token是否正确
   - 确认token没有过期
   - 检查网络连接

3. **签名验证失败**
   - 确认Channel Secret配置正确
   - 检查请求体是否被修改

### 调试技巧

1. 查看应用程序日志：
   ```bash
   # 启动应用时会显示详细日志
   npm start
   ```

2. 检查健康状态：
   ```
   GET http://localhost:3002/health
   ```

3. 测试webhook连接：
   - 在LINE Developers Console中使用"Verify"功能
   - 检查ngrok日志确认请求到达

## API端点

- **LINE Webhook**: `POST /line-webhook`
- **健康检查**: `GET /health`
- **会话查看**: `GET /sessions`

## 相关文档

- [LINE Messaging API 官方文档](https://developers.line.biz/en/docs/messaging-api/)
- [LINE Bot SDK for Node.js](https://github.com/line/line-bot-sdk-nodejs)
- [ngrok 文档](https://ngrok.com/docs)

## 支持

如果您在配置过程中遇到问题，请检查：
1. 环境变量配置是否正确
2. 网络连接是否正常
3. LINE Developers Console中的设置是否正确
4. 应用程序日志中的错误信息
