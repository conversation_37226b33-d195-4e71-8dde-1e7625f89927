// 测试聊天历史功能
const coze = require('./coze-api-v3-client');

async function testChatHistory() {
  console.log('🧪 测试聊天历史功能');
  console.log('=' .repeat(50));
  
  const userId = 'history_test_user';
  
  try {
    // 第一轮对话
    console.log('\n1️⃣ 第一轮对话');
    console.log('👤 用户: 我经常出差住酒店');
    const result1 = await coze.sendMessage('我经常出差住酒店', userId);
    console.log('🤖 机器人:', result1.text.substring(0, 100) + '...');
    
    // 显示聊天历史
    const history1 = coze.getChatHistory(userId);
    console.log(`📚 聊天历史: ${history1.length} 条记录`);
    
    // 等待2秒
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 第二轮对话
    console.log('\n2️⃣ 第二轮对话');
    console.log('👤 用户: 有什么推荐吗？');
    const result2 = await coze.sendMessage('有什么推荐吗？', userId);
    console.log('🤖 机器人:', result2.text.substring(0, 100) + '...');
    
    // 显示聊天历史
    const history2 = coze.getChatHistory(userId);
    console.log(`📚 聊天历史: ${history2.length} 条记录`);
    
    // 等待2秒
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 第三轮对话
    console.log('\n3️⃣ 第三轮对话');
    console.log('👤 用户: 价格大概多少？');
    const result3 = await coze.sendMessage('价格大概多少？', userId);
    console.log('🤖 机器人:', result3.text.substring(0, 100) + '...');
    
    // 显示完整聊天历史
    const finalHistory = coze.getChatHistory(userId);
    console.log(`📚 最终聊天历史: ${finalHistory.length} 条记录`);
    
    console.log('\n📋 完整对话历史:');
    finalHistory.forEach((msg, index) => {
      const role = msg.role === 'user' ? '👤 用户' : '🤖 机器人';
      const content = msg.content.length > 80 ? msg.content.substring(0, 80) + '...' : msg.content;
      console.log(`   ${index + 1}. ${role}: ${content}`);
    });
    
    // 获取聊天历史摘要
    const summary = coze.getChatHistorySummary(userId);
    console.log('\n📊 聊天历史摘要:');
    console.log(`   用户ID: ${summary.userId}`);
    console.log(`   消息总数: ${summary.messageCount}`);
    console.log(`   对话轮数: ${summary.conversationRounds}`);
    console.log(`   最后活动: ${summary.lastActivity}`);
    
    // 验证会话ID一致性
    console.log('\n🔍 会话ID一致性检查:');
    console.log(`   第一轮: ${result1.conversation_id}`);
    console.log(`   第二轮: ${result2.conversation_id}`);
    console.log(`   第三轮: ${result3.conversation_id}`);
    
    const allSame = result1.conversation_id === result2.conversation_id && 
                    result2.conversation_id === result3.conversation_id;
    console.log(`   一致性: ${allSame ? '✅ 一致' : '❌ 不一致'}`);
    
    console.log('\n✅ 聊天历史测试完成');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

// 测试清除历史功能
async function testClearHistory() {
  console.log('\n🧪 测试清除历史功能');
  console.log('-'.repeat(30));
  
  const userId = 'clear_test_user';
  
  try {
    // 发送一条消息
    console.log('👤 发送消息: 你好');
    await coze.sendMessage('你好', userId);
    
    const historyBefore = coze.getChatHistory(userId);
    console.log(`📚 清除前历史: ${historyBefore.length} 条记录`);
    
    // 清除历史
    coze.clearUserConversationId(userId);
    
    const historyAfter = coze.getChatHistory(userId);
    console.log(`📚 清除后历史: ${historyAfter.length} 条记录`);
    
    console.log(`✅ 清除功能: ${historyAfter.length === 0 ? '正常' : '异常'}`);
    
  } catch (error) {
    console.error('❌ 清除测试失败:', error.message);
  }
}

// 主函数
async function main() {
  await testChatHistory();
  await testClearHistory();
}

main();
