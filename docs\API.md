# Twilio Business Messaging API Documentation

## 概述

这是一个基于Node.js和Express的Twilio Business-Initiated消息API，提供完整的消息发送、接收、状态跟踪和webhook处理功能。

## 基础信息

- **Base URL**: `http://localhost:3000`
- **API Version**: `1.0.0`
- **认证方式**: API Key 或 JWT Token

## 认证

### API Key认证
在请求头中添加：
```
X-API-Key: your_api_key_here
```

### JWT认证
在请求头中添加：
```
Authorization: Bearer your_jwt_token_here
```

## API端点

### 1. 消息发送

#### 发送单条消息
```http
POST /api/messages/send
Content-Type: application/json
X-API-Key: your_api_key

{
  "to": "+1234567890",
  "body": "Hello from our business!",
  "mediaUrls": ["https://example.com/image.jpg"],
  "campaignId": "campaign_123",
  "metadata": {
    "source": "website",
    "userId": "user_456"
  }
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "messageId": "60f7b3b3b3b3b3b3b3b3b3b3",
    "twilioSid": "SM1234567890abcdef",
    "status": "queued",
    "conversationId": "conv_789"
  }
}
```

#### 批量发送消息
```http
POST /api/messages/send-bulk
Content-Type: application/json
X-API-Key: your_api_key

{
  "messages": [
    {
      "to": "+1234567890",
      "body": "Message 1"
    },
    {
      "to": "+0987654321",
      "body": "Message 2"
    }
  ]
}
```

### 2. 消息历史

#### 获取消息历史
```http
GET /api/messages/history?phoneNumber=+1234567890&limit=50&offset=0
X-API-Key: your_api_key
```

#### 获取会话消息
```http
GET /api/messages/conversation/conv_789?limit=50&offset=0
X-API-Key: your_api_key
```

### 3. 状态跟踪

#### 获取消息状态
```http
GET /api/status/message/60f7b3b3b3b3b3b3b3b3b3b3
X-API-Key: your_api_key
```

#### 同步Twilio状态
```http
POST /api/status/sync
Content-Type: application/json
X-API-Key: your_api_key

{
  "twilioSid": "SM1234567890abcdef"
}
```

#### 获取传递报告
```http
GET /api/status/delivery-report?startDate=2023-01-01&endDate=2023-12-31&direction=outbound
X-API-Key: your_api_key
```

#### 实时状态查询
```http
POST /api/status/real-time
Content-Type: application/json
X-API-Key: your_api_key

{
  "messageIds": ["60f7b3b3b3b3b3b3b3b3b3b3", "60f7b3b3b3b3b3b3b3b3b3b4"]
}
```

### 4. Webhook端点

#### 消息状态回调
```http
POST /webhook/message-status
Content-Type: application/x-www-form-urlencoded

MessageSid=SM1234567890abcdef&MessageStatus=delivered&From=+1234567890&To=+0987654321
```

#### 入站消息处理
```http
POST /webhook/incoming-message
Content-Type: application/x-www-form-urlencoded

MessageSid=SM1234567890abcdef&From=+1234567890&To=+0987654321&Body=Hello
```

## 错误处理

API使用标准HTTP状态码：

- `200` - 成功
- `400` - 请求错误
- `401` - 认证失败
- `404` - 资源不存在
- `429` - 请求过于频繁
- `500` - 服务器错误

错误响应格式：
```json
{
  "success": false,
  "error": "Error message",
  "details": "Additional error details"
}
```

## 限流

- 通用API: 100请求/15分钟
- 消息发送: 10请求/分钟
- Webhook: 1000请求/分钟

## 数据模型

### 消息对象
```json
{
  "_id": "60f7b3b3b3b3b3b3b3b3b3b3",
  "twilioSid": "SM1234567890abcdef",
  "direction": "outbound",
  "from": "+0987654321",
  "to": "+1234567890",
  "body": "Hello from our business!",
  "mediaUrls": ["https://example.com/image.jpg"],
  "messageType": "text",
  "status": "delivered",
  "conversationId": "conv_789",
  "campaignId": "campaign_123",
  "sentAt": "2023-01-01T12:00:00.000Z",
  "deliveredAt": "2023-01-01T12:00:05.000Z",
  "createdAt": "2023-01-01T12:00:00.000Z",
  "updatedAt": "2023-01-01T12:00:05.000Z"
}
```

### 会话对象
```json
{
  "conversationId": "conv_789",
  "participants": [
    {
      "phoneNumber": "+0987654321",
      "role": "business"
    },
    {
      "phoneNumber": "+1234567890",
      "role": "customer"
    }
  ],
  "status": "active",
  "messageCount": {
    "total": 5,
    "outbound": 3,
    "inbound": 2,
    "unread": 1
  },
  "lastActivityAt": "2023-01-01T12:00:00.000Z"
}
```

## 示例代码

### JavaScript/Node.js
```javascript
const axios = require('axios');

const sendMessage = async () => {
  try {
    const response = await axios.post('http://localhost:3000/api/messages/send', {
      to: '+1234567890',
      body: 'Hello from our business!'
    }, {
      headers: {
        'X-API-Key': 'your_api_key_here',
        'Content-Type': 'application/json'
      }
    });
    
    console.log('Message sent:', response.data);
  } catch (error) {
    console.error('Error:', error.response.data);
  }
};
```

### Python
```python
import requests

def send_message():
    url = 'http://localhost:3000/api/messages/send'
    headers = {
        'X-API-Key': 'your_api_key_here',
        'Content-Type': 'application/json'
    }
    data = {
        'to': '+1234567890',
        'body': 'Hello from our business!'
    }
    
    response = requests.post(url, json=data, headers=headers)
    print(response.json())
```

### cURL
```bash
curl -X POST http://localhost:3000/api/messages/send \
  -H "X-API-Key: your_api_key_here" \
  -H "Content-Type: application/json" \
  -d '{
    "to": "+1234567890",
    "body": "Hello from our business!"
  }'
```
