const { ZepClient } = require('@getzep/zep-cloud');
require('dotenv').config();

/**
 * Zep Cloud 功能演示
 * 这个脚本演示了如何使用Zep Cloud进行：
 * 1. 用户和会话管理
 * 2. 聊天记忆存储和检索
 * 3. 知识图谱构建和搜索
 */

class ZepDemo {
  constructor() {
    this.zepClient = new ZepClient({
      apiKey: process.env.ZEP_API_KEY
    });
    
    this.userId = `demo-user-${Date.now()}`;
    this.sessionId = `demo-session-${Date.now()}`;
  }

  async initialize() {
    console.log('🚀 初始化Zep演示...\n');
    
    // 创建用户
    console.log('👤 创建演示用户...');
    const user = await this.zepClient.user.add({
      userId: this.userId,
      email: '<EMAIL>',
      firstName: '演示',
      lastName: '用户',
      metadata: {
        source: 'zep-demo',
        created: new Date().toISOString()
      }
    });
    console.log(`✅ 用户创建成功: ${user.firstName} ${user.lastName} (${user.email})\n`);

    // 创建会话
    console.log('💬 创建聊天会话...');
    const session = await this.zepClient.memory.addSession({
      sessionId: this.sessionId,
      userId: this.userId,
      metadata: {
        type: 'demo-chat',
        created: new Date().toISOString()
      }
    });
    console.log(`✅ 会话创建成功: ${session.sessionId}\n`);
  }

  async demonstrateMemory() {
    console.log('🧠 演示记忆功能...\n');

    // 模拟一段对话
    const conversations = [
      {
        user: '你好，我是李明，是一名前端开发工程师。',
        assistant: '你好李明！很高兴认识你。作为前端开发工程师，你主要使用哪些技术栈呢？'
      },
      {
        user: '我主要使用React、Vue.js和TypeScript。最近在学习Next.js。',
        assistant: '很棒的技术选择！React和Vue.js都是很流行的框架，TypeScript也能提高代码质量。Next.js是一个很好的React框架，特别适合全栈开发。你在学习Next.js的过程中遇到什么挑战吗？'
      },
      {
        user: '主要是服务端渲染和API路由的部分比较复杂。',
        assistant: '确实，SSR和API路由是Next.js的核心特性，但也是比较复杂的部分。建议你可以从简单的静态生成开始，然后逐步学习动态路由和API设计。有什么具体的项目想法吗？'
      }
    ];

    for (let i = 0; i < conversations.length; i++) {
      const conv = conversations[i];
      console.log(`📝 添加对话 ${i + 1}...`);
      
      // 添加用户消息
      await this.zepClient.memory.add(this.sessionId, {
        messages: [
          { roleType: 'user', content: conv.user },
          { roleType: 'assistant', content: conv.assistant }
        ],
        returnContext: true
      });
      
      console.log(`   用户: ${conv.user.substring(0, 50)}...`);
      console.log(`   助手: ${conv.assistant.substring(0, 50)}...\n`);
    }

    // 获取记忆
    console.log('🔍 获取会话记忆...');
    const memory = await this.zepClient.memory.get(this.sessionId);
    
    console.log(`✅ 记忆获取成功:`);
    console.log(`   - 消息数量: ${memory.messages?.length || 0}`);
    console.log(`   - 有摘要: ${memory.summary ? '是' : '否'}`);
    console.log(`   - 有上下文: ${memory.context ? '是' : '否'}`);
    
    if (memory.context) {
      console.log(`\n📋 上下文信息预览:`);
      console.log(memory.context.substring(0, 200) + '...\n');
    }

    return memory;
  }

  async demonstrateKnowledgeGraph() {
    console.log('🕸️ 演示知识图谱功能...\n');

    // 添加结构化数据到知识图谱
    console.log('📊 添加用户档案到知识图谱...');
    const profileData = {
      name: '李明',
      occupation: '前端开发工程师',
      skills: ['React', 'Vue.js', 'TypeScript', 'JavaScript', 'HTML', 'CSS'],
      learning: ['Next.js', '服务端渲染', 'API设计'],
      experience: '3年',
      interests: ['Web开发', '用户体验', '性能优化'],
      location: '上海',
      education: '计算机科学学士',
      projects: [
        {
          name: '电商网站',
          tech: ['React', 'Redux', 'TypeScript'],
          description: '使用React构建的响应式电商平台'
        },
        {
          name: '管理系统',
          tech: ['Vue.js', 'Element UI', 'Axios'],
          description: '企业内部管理系统前端开发'
        }
      ]
    };

    await this.zepClient.graph.add({
      userId: this.userId,
      type: 'json',
      data: JSON.stringify(profileData)
    });

    console.log('✅ 知识图谱数据添加成功\n');

    // 等待数据处理
    console.log('⏳ 等待知识图谱数据处理...');
    await new Promise(resolve => setTimeout(resolve, 5000));

    // 搜索知识图谱
    console.log('🔍 搜索知识图谱...');
    const queries = [
      '用户有什么技能？',
      '用户在学习什么？',
      '用户做过什么项目？',
      '用户的工作经验如何？'
    ];

    for (const query of queries) {
      try {
        console.log(`\n❓ 查询: ${query}`);
        const searchResults = await this.zepClient.graph.search({
          userId: this.userId,
          query: query
        });

        if (searchResults.edges && searchResults.edges.length > 0) {
          console.log('📊 搜索结果:');
          searchResults.edges.slice(0, 3).forEach((edge, index) => {
            console.log(`   ${index + 1}. ${edge.fact}`);
          });
        } else {
          console.log('   暂无相关结果（数据可能还在处理中）');
        }
      } catch (error) {
        console.log(`   搜索出错: ${error.message}`);
      }
    }
  }

  async demonstrateContextualChat() {
    console.log('\n💭 演示上下文感知聊天...\n');

    // 添加新的用户消息
    const newUserMessage = '我想转向全栈开发，你有什么建议吗？';
    console.log(`👤 用户新消息: ${newUserMessage}`);

    const result = await this.zepClient.memory.add(this.sessionId, {
      messages: [{ roleType: 'user', content: newUserMessage }],
      returnContext: true
    });

    console.log('\n📋 Zep提供的上下文信息:');
    if (result.context) {
      // 解析上下文中的事实和实体
      const contextLines = result.context.split('\n').filter(line => line.trim());
      const facts = contextLines.filter(line => line.includes('- ') && !line.includes('FACTS') && !line.includes('ENTITIES'));
      const entities = contextLines.filter(line => line.includes('- ') && contextLines.indexOf(line) > contextLines.findIndex(l => l.includes('ENTITIES')));

      if (facts.length > 0) {
        console.log('\n🔍 相关事实:');
        facts.slice(0, 5).forEach(fact => {
          console.log(`   ${fact.trim()}`);
        });
      }

      if (entities.length > 0) {
        console.log('\n👥 相关实体:');
        entities.slice(0, 3).forEach(entity => {
          console.log(`   ${entity.trim()}`);
        });
      }
    }

    // 模拟基于上下文的回复
    const contextualReply = `基于你的背景（前端开发工程师，熟悉React、Vue.js、TypeScript，正在学习Next.js），我建议你：

1. 继续深入Next.js，它是很好的全栈入门框架
2. 学习Node.js和Express.js作为后端基础
3. 掌握数据库操作（如MongoDB或PostgreSQL）
4. 了解API设计和RESTful服务
5. 学习云服务部署（如Vercel、AWS）

你的前端经验是很好的基础，特别是TypeScript技能在全栈开发中很有价值。`;

    console.log('\n🤖 基于上下文的智能回复:');
    console.log(contextualReply);

    // 添加助手回复到记忆
    await this.zepClient.memory.add(this.sessionId, {
      messages: [{ roleType: 'assistant', content: contextualReply }]
    });

    console.log('\n✅ 回复已添加到记忆中');
  }

  async cleanup() {
    console.log('\n🧹 清理演示数据...');
    try {
      await this.zepClient.user.delete(this.userId);
      console.log('✅ 演示数据清理完成');
    } catch (error) {
      console.log(`⚠️ 清理时出错: ${error.message}`);
    }
  }

  async run() {
    try {
      await this.initialize();
      await this.demonstrateMemory();
      await this.demonstrateKnowledgeGraph();
      await this.demonstrateContextualChat();
      
      console.log('\n🎉 Zep Cloud演示完成！');
      console.log('\n📝 演示总结:');
      console.log('✅ 用户和会话管理');
      console.log('✅ 聊天记忆存储和检索');
      console.log('✅ 知识图谱数据添加');
      console.log('✅ 上下文感知的智能对话');
      console.log('\n💡 Zep Cloud可以帮助您构建具有长期记忆的AI助手！');
      
    } catch (error) {
      console.error('\n❌ 演示过程中出错:', error.message);
      if (error.statusCode) {
        console.error('状态码:', error.statusCode);
      }
    } finally {
      await this.cleanup();
    }
  }
}

// 运行演示
if (require.main === module) {
  const demo = new ZepDemo();
  demo.run().catch(console.error);
}

module.exports = ZepDemo;
