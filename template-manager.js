// Twilio WhatsApp模板管理工具
require('dotenv').config();
const twilio = require('twilio');

class TemplateManager {
  constructor() {
    this.accountSid = process.env.TWILIO_ACCOUNT_SID;
    this.authToken = process.env.TWILIO_AUTH_TOKEN;
    this.client = twilio(this.accountSid, this.authToken);
  }

  // 获取所有内容模板
  async getAllTemplates() {
    console.log('📋 获取所有内容模板...\n');

    try {
      const contents = await this.client.content.contents.list({ limit: 50 });

      console.log(`✅ 找到 ${contents.length} 个内容模板:\n`);

      contents.forEach((content, index) => {
        console.log(`${index + 1}. ${content.friendlyName || 'Unnamed Template'}`);
        console.log(`   SID: ${content.sid}`);
        console.log(`   语言: ${content.language || 'N/A'}`);
        console.log(`   类型: ${content.types || 'N/A'}`);
        console.log(`   创建时间: ${content.dateCreated || 'N/A'}`);
        console.log(`   更新时间: ${content.dateUpdated || 'N/A'}`);
        console.log('');
      });

      return contents;

    } catch (error) {
      console.error('❌ 获取模板失败:', error.message);
      throw error;
    }
  }

  // 获取特定模板详情
  async getTemplateDetails(contentSid) {
    console.log(`🔍 获取模板详情: ${contentSid}\n`);

    try {
      const content = await this.client.content.contents(contentSid).fetch();

      console.log('📄 模板详情:');
      console.log(`   名称: ${content.friendlyName || 'Unnamed'}`);
      console.log(`   SID: ${content.sid}`);
      console.log(`   语言: ${content.language || 'N/A'}`);
      console.log(`   类型: ${content.types || 'N/A'}`);
      console.log(`   创建时间: ${content.dateCreated}`);
      console.log(`   更新时间: ${content.dateUpdated}`);
      console.log('');

      // 获取模板版本
      const versions = await this.client.content.contents(contentSid).approvalRequests.list();
      
      if (versions.length > 0) {
        console.log('📝 模板版本:');
        versions.forEach((version, index) => {
          console.log(`   版本 ${index + 1}:`);
          console.log(`     状态: ${version.status}`);
          console.log(`     名称: ${version.name || 'N/A'}`);
          console.log(`     类别: ${version.category || 'N/A'}`);
          console.log(`     创建时间: ${version.dateCreated}`);
          console.log('');
        });
      }

      return { content, versions };

    } catch (error) {
      console.error('❌ 获取模板详情失败:', error.message);
      throw error;
    }
  }

  // 测试模板消息发送
  async testTemplate(templateSid, toNumber, variables = '{}') {
    console.log(`🧪 测试模板消息发送...\n`);
    console.log(`   模板SID: ${templateSid}`);
    console.log(`   接收号码: ${toNumber}`);
    console.log(`   变量: ${variables}`);
    console.log('');

    try {
      const message = await this.client.messages.create({
        from: 'whatsapp:+14155238886',
        contentSid: templateSid,
        contentVariables: variables,
        to: toNumber
      });

      console.log('✅ 测试消息发送成功!');
      console.log(`   消息SID: ${message.sid}`);
      console.log(`   状态: ${message.status}`);
      console.log(`   发送时间: ${new Date().toISOString()}`);

      // 等待状态更新
      setTimeout(async () => {
        try {
          const updatedMessage = await this.client.messages(message.sid).fetch();
          console.log('\n📊 状态更新:');
          console.log(`   当前状态: ${updatedMessage.status}`);
          console.log(`   错误代码: ${updatedMessage.errorCode || 'None'}`);
          console.log(`   价格: ${updatedMessage.price || 'N/A'} ${updatedMessage.priceUnit || ''}`);
        } catch (statusError) {
          console.log('⚠️ 状态查询失败:', statusError.message);
        }
      }, 3000);

      return message;

    } catch (error) {
      console.error('❌ 测试发送失败:', error.message);
      
      // 详细错误分析
      this.analyzeError(error);
      throw error;
    }
  }

  // 批量测试多个模板
  async batchTestTemplates(templates, toNumber) {
    console.log(`🔄 批量测试 ${templates.length} 个模板...\n`);

    const results = [];

    for (const template of templates) {
      console.log(`📤 测试模板: ${template.sid}`);
      
      try {
        const result = await this.testTemplate(
          template.sid, 
          toNumber, 
          template.variables || '{}'
        );
        
        results.push({
          templateSid: template.sid,
          success: true,
          messageSid: result.sid,
          status: result.status
        });

        console.log('   ✅ 成功\n');

      } catch (error) {
        results.push({
          templateSid: template.sid,
          success: false,
          error: error.message,
          code: error.code
        });

        console.log(`   ❌ 失败: ${error.message}\n`);
      }

      // 等待1秒避免发送过快
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    // 显示批量测试结果
    console.log('📊 批量测试结果汇总:');
    console.log(`   总测试数: ${results.length}`);
    console.log(`   成功数: ${results.filter(r => r.success).length}`);
    console.log(`   失败数: ${results.filter(r => !r.success).length}`);
    console.log('');

    results.forEach((result, index) => {
      const status = result.success ? '✅' : '❌';
      console.log(`   ${index + 1}. ${status} ${result.templateSid}`);
      if (result.success) {
        console.log(`      消息SID: ${result.messageSid}`);
      } else {
        console.log(`      错误: ${result.error}`);
      }
    });

    return results;
  }

  // 创建新的内容模板
  async createTemplate(templateData) {
    console.log('📝 创建新的内容模板...\n');

    try {
      const content = await this.client.content.contents.create({
        friendlyName: templateData.friendlyName,
        language: templateData.language || 'en',
        variables: templateData.variables || {}
      });

      console.log('✅ 模板创建成功!');
      console.log(`   SID: ${content.sid}`);
      console.log(`   名称: ${content.friendlyName}`);
      console.log(`   语言: ${content.language}`);

      return content;

    } catch (error) {
      console.error('❌ 模板创建失败:', error.message);
      throw error;
    }
  }

  // 错误分析
  analyzeError(error) {
    console.log('\n🔧 错误分析:');
    
    switch (error.code) {
      case 63016:
        console.log('   原因: 接收号码未加入沙盒或无效');
        console.log('   解决方案: 确保号码已正确加入WhatsApp沙盒');
        break;
      case 63017:
        console.log('   原因: 内容模板无效或未批准');
        console.log('   解决方案: 检查模板ID和批准状态');
        break;
      case 21211:
        console.log('   原因: 电话号码格式无效');
        console.log('   解决方案: 使用正确格式 whatsapp:+**********');
        break;
      case 20003:
        console.log('   原因: 认证失败');
        console.log('   解决方案: 检查Account SID和Auth Token');
        break;
      default:
        console.log(`   错误代码: ${error.code}`);
        console.log(`   详细信息: ${error.moreInfo || 'N/A'}`);
    }
  }

  // 验证模板变量格式
  validateVariables(variables) {
    try {
      const parsed = JSON.parse(variables);
      console.log('✅ 变量格式有效:', parsed);
      return true;
    } catch (error) {
      console.log('❌ 变量格式无效:', error.message);
      return false;
    }
  }
}

// 主函数
async function main() {
  console.log('🛠️ Twilio WhatsApp模板管理工具');
  console.log('=' .repeat(50));

  const manager = new TemplateManager();
  const command = process.argv[2];

  try {
    switch (command) {
      case 'list':
        await manager.getAllTemplates();
        break;

      case 'details':
        const templateSid = process.argv[3];
        if (!templateSid) {
          console.log('❌ 请提供模板SID');
          console.log('使用方法: node template-manager.js details HX123456');
          return;
        }
        await manager.getTemplateDetails(templateSid);
        break;

      case 'test':
        const testSid = process.argv[3];
        const testNumber = process.argv[4];
        const testVariables = process.argv[5] || '{}';
        
        if (!testSid || !testNumber) {
          console.log('❌ 请提供模板SID和接收号码');
          console.log('使用方法: node template-manager.js test HX123456 whatsapp:+********** \'{"1":"value"}\'');
          return;
        }
        
        await manager.testTemplate(testSid, testNumber, testVariables);
        break;

      case 'batch':
        const batchNumber = process.argv[3];
        if (!batchNumber) {
          console.log('❌ 请提供接收号码');
          console.log('使用方法: node template-manager.js batch whatsapp:+**********');
          return;
        }

        // 获取所有模板并批量测试
        const templates = await manager.getAllTemplates();
        const testTemplates = templates.slice(0, 3).map(t => ({ sid: t.sid })); // 只测试前3个
        await manager.batchTestTemplates(testTemplates, batchNumber);
        break;

      case 'validate':
        const variables = process.argv[3];
        if (!variables) {
          console.log('❌ 请提供变量JSON');
          console.log('使用方法: node template-manager.js validate \'{"1":"value","2":"value2"}\'');
          return;
        }
        manager.validateVariables(variables);
        break;

      default:
        console.log('📋 可用命令:');
        console.log('   list                           - 列出所有模板');
        console.log('   details <templateSid>          - 获取模板详情');
        console.log('   test <sid> <number> [vars]     - 测试单个模板');
        console.log('   batch <number>                 - 批量测试模板');
        console.log('   validate <variables>           - 验证变量格式');
        console.log('');
        console.log('📝 示例:');
        console.log('   node template-manager.js list');
        console.log('   node template-manager.js details HXb5b62575e6e4ff6129ad7c8efe1f983e');
        console.log('   node template-manager.js test HX123456 whatsapp:+********** \'{"1":"12/1","2":"3pm"}\'');
        console.log('   node template-manager.js validate \'{"1":"test","2":"value"}\'');
    }

  } catch (error) {
    console.log('\n💥 执行失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = TemplateManager;
